using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace MauiApp1.Models
{
    public class CartItem : INotifyPropertyChanged
    {
        private int _quantity;
        
        public Product Product { get; set; } = new();
        
        public int Quantity
        {
            get => _quantity;
            set
            {
                if (_quantity != value)
                {
                    _quantity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TotalPrice));
                    OnPropertyChanged(nameof(FormattedTotalPrice));
                }
            }
        }
        
        public decimal TotalPrice => Product.Price * Quantity;
        
        public string FormattedTotalPrice => $"${TotalPrice:F2}";
        
        public string DisplayText => $"{Product.Name} x{Quantity}";
        
        public event PropertyChangedEventHandler? PropertyChanged;
        
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
