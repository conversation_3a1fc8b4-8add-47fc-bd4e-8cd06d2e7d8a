using System.ComponentModel.DataAnnotations;

namespace MauiApp1.Models
{
    public class Product
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;
        
        [Range(0.01, double.MaxValue, ErrorMessage = "Price must be greater than 0")]
        public decimal Price { get; set; }
        
        public string Category { get; set; } = string.Empty;
        
        public string ImageUrl { get; set; } = string.Empty;
        
        public bool IsAvailable { get; set; } = true;
        
        // For display purposes
        public string FormattedPrice => $"${Price:F2}";
        
        // Color coding for categories (for UI)
        public Color CategoryColor => Category.ToLower() switch
        {
            "food" => Color.FromArgb("#EF4444"),        // Modern red
            "beverage" => Color.FromArgb("#3B82F6"),    // Modern blue
            "dessert" => Color.FromArgb("#EC4899"),     // Modern pink
            "appetizer" => Color.FromArgb("#10B981"),   // Modern green
            _ => Color.FromArgb("#6B7280")              // Modern gray
        };

        // Category gradient colors for enhanced visual appeal
        public Color CategoryLightColor => Category.ToLower() switch
        {
            "food" => Color.FromArgb("#FEE2E2"),        // Light red
            "beverage" => Color.FromArgb("#DBEAFE"),    // Light blue
            "dessert" => Color.FromArgb("#FCE7F3"),     // Light pink
            "appetizer" => Color.FromArgb("#D1FAE5"),   // Light green
            _ => Color.FromArgb("#F3F4F6")              // Light gray
        };
    }
}
