namespace MauiApp1.Models
{
    public class Order
    {
        public int Id { get; set; }
        
        public DateTime OrderDate { get; set; } = DateTime.Now;
        
        public List<CartItem> Items { get; set; } = new();
        
        public decimal Subtotal => Items.Sum(item => item.TotalPrice);
        
        public decimal TaxRate { get; set; } = 0.08m; // 8% tax rate
        
        public decimal TaxAmount => Subtotal * TaxRate;
        
        public decimal Total => Subtotal + TaxAmount;
        
        public PaymentMethod PaymentMethod { get; set; }
        
        public OrderStatus Status { get; set; } = OrderStatus.Pending;
        
        public string? Notes { get; set; }
        
        // Formatted display properties
        public string FormattedSubtotal => $"${Subtotal:F2}";
        public string FormattedTaxAmount => $"${TaxAmount:F2}";
        public string FormattedTotal => $"${Total:F2}";
        public string FormattedOrderDate => OrderDate.ToString("MM/dd/yyyy HH:mm");
        
        public int TotalItemCount => Items.Sum(item => item.Quantity);
    }
    
    public enum OrderStatus
    {
        Pending,
        Completed,
        Cancelled,
        Refunded
    }
    
    public enum PaymentMethod
    {
        Cash,
        CreditCard,
        DebitCard,
        DigitalWallet,
        GiftCard
    }
}
