﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <_PostProcessingItem Include="MauiApp1.app/Contents/MacOS/MauiApp1">
      <IsAppExtension>false</IsAppExtension>
      <BCSymbolMapName>MauiApp1.bcsymbolmap</BCSymbolMapName>
      <NoDSymUtil>true</NoDSymUtil>
      <dSYMSourcePathExists>false</dSYMSourcePathExists>
      <NoSymbolStrip>true</NoSymbolStrip>
      <dSYMInfoPlistRelativePath>MauiApp1.app.dSYM\Contents\Info.plist</dSYMInfoPlistRelativePath>
      <SymbolFile>/Users/<USER>/Desktop/maui apps/MauiApp1/obj/Debug/net9.0-maccatalyst/maccatalyst-arm64/mtouch-symbols.list</SymbolFile>
      <DSymName>MauiApp1.app.dSYM</DSymName>
      <SourceProjectPath>/Users/<USER>/Desktop/maui apps/MauiApp1</SourceProjectPath>
      <IsXPCService></IsXPCService>
    </_PostProcessingItem>
  </ItemGroup>
</Project>