﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <_CodesignBundle Include="MauiApp1.app">
      <CodesignSigningKey>-</CodesignSigningKey>
      <CodesignEntitlements>/Users/<USER>/Desktop/maui apps/MauiApp1/obj/Debug/net9.0-maccatalyst/maccatalyst-arm64/Entitlements.xcent</CodesignEntitlements>
      <CodesignUseSecureTimestamp></CodesignUseSecureTimestamp>
      <CodesignAllocate>/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/codesign_allocate</CodesignAllocate>
      <RequireCodeSigning>false</RequireCodeSigning>
      <CodesignExtraArgs></CodesignExtraArgs>
      <CodesignResourceRules></CodesignResourceRules>
      <CodesignDisableTimestamp>true</CodesignDisableTimestamp>
      <CodesignKeychain></CodesignKeychain>
      <CodesignUseHardenedRuntime></CodesignUseHardenedRuntime>
      <SourceProjectPath>/Users/<USER>/Desktop/maui apps/MauiApp1</SourceProjectPath>
    </_CodesignBundle>
  </ItemGroup>
</Project>