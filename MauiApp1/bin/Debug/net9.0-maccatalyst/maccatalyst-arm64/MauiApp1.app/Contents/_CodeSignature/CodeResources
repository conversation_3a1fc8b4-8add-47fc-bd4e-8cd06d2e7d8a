<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/AboutAssets.txt</key>
		<data>
		87D3//yZmTijel0tM/5kvTDtVRM=
		</data>
		<key>Resources/Assets.car</key>
		<data>
		EuEwxbWjPVJpiw6UZHguI2OraTc=
		</data>
		<key>Resources/MauiInfo.plist</key>
		<data>
		iEtuUGCV6JM3gxS/pO33swhmpp8=
		</data>
		<key>Resources/MonoTouchDebugConfiguration.txt</key>
		<data>
		oowkf1sL6T19nanXUObgmuayWqM=
		</data>
		<key>Resources/OpenSans-Regular.ttf</key>
		<data>
		ur6NzpOj5ItsPHlyCgwEjojdH+c=
		</data>
		<key>Resources/OpenSans-Semibold.ttf</key>
		<data>
		4lb4ORcY72HyU9+06Vu+s8WFevw=
		</data>
		<key>Resources/appicon.icns</key>
		<data>
		i90ghU/x0dES9mVeschyOSGQjKE=
		</data>
		<key>Resources/dotnet_bot.png</key>
		<data>
		TCPYt8l244J9a2sCFwVUdoBhDdA=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		nsnZE/LubM8lWn5PzK0t5hD6/Qg=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		km7arW01LDKD2LzrEduLpKYf69k=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>MonoBundle/MauiApp1.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			uvnEs29hS2ebK/2hJnBqGpjxvWT+uYt77AKrA5U8Smw=
			</data>
		</dict>
		<key>MonoBundle/MauiApp1.pdb</key>
		<dict>
			<key>hash2</key>
			<data>
			GGS3LBs4/elf5CPgIl8z/v+5ww5Ew1mu88nn3TQerUY=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.CSharp.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			xUNXm6aVboAZP/LSr8kPEZ8F5cQ3FAftue1htD8svZU=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Configuration.Abstractions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			BXbVvzXtzU3Jtn4o3nxiFmt1EMPrklZbr7pY5+i5IeI=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Configuration.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			X+f9SgR23haohmwk5tk9zYfdv6qCFqGkvWdDygCc1NI=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.DependencyInjection.Abstractions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			P/Igkglgo+bsVIsebQDgeLFuuAZ6aT4dE68eMgvcmXM=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.DependencyInjection.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			oBrzb4SPVFSTIzMlZbpThdfLOW48/dCiXtlHpx+zlDA=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Logging.Abstractions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			J/Xubo3dAZOLwr1aQaxB+N7JxwLrws9wvOR0GiIVflo=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Logging.Debug.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			wQY2V5hkThUF6RFjkXI3bT4W8Mpk0XuqJaQuofIKCPM=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Logging.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			aixSiEiZl4n486f785HLrykuMY9wBiMAralMOmpTf5g=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Options.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			jO7jAQ7MAPfujrVLJXW7kn4vbwJ48JDJitqOY4/FTR4=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Extensions.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			vQ1ywOC2GH9bndvR6dnF2+dwSY61EsiNwp+Uahou3dE=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.MacCatalyst.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			QgSog0ycS4SHAnGAB8kOljzxkSYtItrvyOdnMKtVemU=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.MacCatalyst.pdb</key>
		<dict>
			<key>hash2</key>
			<data>
			j+KiWbZcCJUOyam/4b/5JndkCv+mKF0/hcalQ0Bqtgg=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Maui.Controls.HotReload.Forms.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			XWFvQUbrEKy5gYQN4OHirpPzVf6lfmWo8M5EDnvJJHE=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Maui.Controls.Xaml.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			k2liDCoA5Jk/9MH29nfl1VvDojeiBpk2b9N3xMK76f8=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Maui.Controls.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			h+VHvl6HsYVrvDEvDzhboryciWcV/f4yypD4ogtMKFY=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Maui.Essentials.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			tG3G7O5OzAH7uDh9VMveJ1l5ENnfykM4WEmgywXjG1I=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Maui.Graphics.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			hfQu3BKI4ZBYrHA4gtSmXKFM/0i5N7Cz2xtiAb2ruCY=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Maui.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			BIMJBzsPpPo61GP4W/sX/GEuN5ffptz4g9d7LnexCfE=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.VisualBasic.Core.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			CALSiF68L8JojvrjjMcqPlbY1+zqSd8bSPoW57xlldo=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.VisualBasic.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			QHaL7wKqXdrlQjJcT/qIXqcxO0fQpTZiEoF1+LYOcmc=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.VisualStudio.DesignTools.MobileTapContracts.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			exoJIWO+xZR7QYGcJrIpooCoAw0EWs6//Al7YTkXnVc=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.VisualStudio.DesignTools.TapContract.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			sFZ0RIZ9MawV847xJ0Eux85HB7wkdqvr52ZWZ7OatbM=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.VisualStudio.DesignTools.XamlTapContract.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			NHj1lE0oL04MxhIOGiP2kynvmTTN5PWl6mzs02Lvjiw=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Win32.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ofUQseBDKmfOKivrrLuoDmau2MPfLmQixN07d0/a2KY=
			</data>
		</dict>
		<key>MonoBundle/Microsoft.Win32.Registry.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			VoL6i/ebvjtvRzwa3trVFoeq5kPKyyeqpC81S5X+070=
			</data>
		</dict>
		<key>MonoBundle/System.AppContext.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			+BHzth1SflVOaLjfuqVUuJgr45CW9jnuwvD1SDgBcOQ=
			</data>
		</dict>
		<key>MonoBundle/System.Buffers.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			urpGCGaz4th7TTdrO3aTQ7U/jKu2KSnWnEsGwXNw1pw=
			</data>
		</dict>
		<key>MonoBundle/System.Collections.Concurrent.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			LRM899DlqjxImuQBouHbQLSM+ISpVrx/E3j0zMkgY68=
			</data>
		</dict>
		<key>MonoBundle/System.Collections.Immutable.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			grKCjZwCsjctQXcKD7SC5WmaqiVpZFEE7YsFP7Cjgq4=
			</data>
		</dict>
		<key>MonoBundle/System.Collections.NonGeneric.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			nQDZ6X0uSp2hQDSM7VlAjDWg0W3DBQ68OlzEWxiiB+w=
			</data>
		</dict>
		<key>MonoBundle/System.Collections.Specialized.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Osok3vA4MAdDrBcsuXEaE8dhAnaTwiPgl0UZGXWo/dA=
			</data>
		</dict>
		<key>MonoBundle/System.Collections.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			BdLcIcm08YB/QJq7xGfnOlCxHaF55SrBn6T5miV1GEY=
			</data>
		</dict>
		<key>MonoBundle/System.ComponentModel.Annotations.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			RepiGUnpyAFvoaWUfUH3UfQg65VruRQ6kY4x1ocsaaE=
			</data>
		</dict>
		<key>MonoBundle/System.ComponentModel.DataAnnotations.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			/0KkuBdntdXYKIC3cNwnoqmdpdTeQSn7YY8KsFn4Trg=
			</data>
		</dict>
		<key>MonoBundle/System.ComponentModel.EventBasedAsync.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			884KB5KWe1G9eWIo64ldIaVMFUvuirBjCRNwU08JMQg=
			</data>
		</dict>
		<key>MonoBundle/System.ComponentModel.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			vaH2XR+oD9K46kbBl8Q2URi0WCYXmJycp0lXSzuisFY=
			</data>
		</dict>
		<key>MonoBundle/System.ComponentModel.TypeConverter.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			gDW8hjtnM6GEs6moBvgu0sm08RYeu4fFbfOUv1ByvPU=
			</data>
		</dict>
		<key>MonoBundle/System.ComponentModel.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			sUh+9ghCJOOmWHJZ2iMqcqrHLLVYdHdR0zHkC4SoLXc=
			</data>
		</dict>
		<key>MonoBundle/System.Configuration.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Eh1omAyNI7L265+Nx4y6qcM6bpoyW9B1E/4cT1UDoKY=
			</data>
		</dict>
		<key>MonoBundle/System.Console.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			YAB06OlBd4RcYrcjQ/LPo+0FdsVIjpkA+IzpzCDnGQg=
			</data>
		</dict>
		<key>MonoBundle/System.Core.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			uRXGqDIZky3E62MGfMIpvTug4zA1aa85uYUBjuYSNvI=
			</data>
		</dict>
		<key>MonoBundle/System.Data.Common.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			etY6kCF/bmBIPXECQlKEnChTk2+yF60rxPy4osBtRrc=
			</data>
		</dict>
		<key>MonoBundle/System.Data.DataSetExtensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			2oIAQpkMF+nZJBfCOjmlY8zmoD1YqzUDjDeK8+CvA1A=
			</data>
		</dict>
		<key>MonoBundle/System.Data.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			6I8oau/LUPAijiLhjxpzfj7Wn5ZYQBimFrYnfAUbuFk=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.Contracts.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			GyCC9lnVPZa1lfBxpD/t3vyeZEeeD+q+9JZL38ffbWo=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.Debug.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			F3CCq+SCCNJhrWx/rAsmspunHkZ7Cooa2Niv8jT0ezY=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.DiagnosticSource.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			4Iylk/TcdEmvjk5R5ohc0uEypC8OGCfO2h340YmPnmU=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.FileVersionInfo.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			S2xS/w2LcHYhmTEoMz30uJfx6ySdZAX5ja/13mPDG98=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.Process.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HF/AwOQabtjSFRSbJvQDIAoiwu5JziHkCwO00jlQ6W8=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.StackTrace.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			lP7R7rLarMM2c9OaS+l70zzTXAo9V4c9YzAX1RBf/VY=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.TextWriterTraceListener.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			tjxNvA94Gsdb9pk//JrmR6hqK7FdMsjamjeUgt9JaVw=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.Tools.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			lNL4rS5RPF72fHUPX0rBkaOb+15QsAuZtNPeE9zQtY4=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.TraceSource.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			zIgsCk94G89WgUmNTG2+x2ZNZCxwpnwQmyyDeb8xJKY=
			</data>
		</dict>
		<key>MonoBundle/System.Diagnostics.Tracing.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			sr1nqH5xhj8MNrNPEXYoQeGegMxs5JnSkiciE+So2pU=
			</data>
		</dict>
		<key>MonoBundle/System.Drawing.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			zy07/LznWw9aVIYeq9sMkAIGdmTISSjsLmLLUo6DJjw=
			</data>
		</dict>
		<key>MonoBundle/System.Drawing.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			u8ZODv/nZ7/hRmMCJ/eJGSsMlMV78iA90Yx1Gp0nCQQ=
			</data>
		</dict>
		<key>MonoBundle/System.Dynamic.Runtime.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			6wItTn5kusXA2pN5XowdkVp6yRmL1bm1qLKxav5SvsM=
			</data>
		</dict>
		<key>MonoBundle/System.Formats.Asn1.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			9kWDDg2+QPZSymrY4DItgx4uRb4W5Myzc74pLVJeG3A=
			</data>
		</dict>
		<key>MonoBundle/System.Formats.Tar.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HT3BvZRlPHD2qv03a45rfIacdqyBaZap4xexWkdVUJA=
			</data>
		</dict>
		<key>MonoBundle/System.Globalization.Calendars.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			xsh4hHjqkiuEqwxdEHjVjPqnzbsjm5r8F3M7xpk4Vp4=
			</data>
		</dict>
		<key>MonoBundle/System.Globalization.Extensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			8lZmm8131oSqzXpk6TTqXggqxqzhaGZ8FjDQN3OUMe0=
			</data>
		</dict>
		<key>MonoBundle/System.Globalization.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			SWcvKE1UXE2av6Nt9vgRINSyMRMCDsQQD+c4jj5QSUM=
			</data>
		</dict>
		<key>MonoBundle/System.IO.Compression.Brotli.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			LeFGcMJP2PdzvzvudlWr/llaSdoIgi9J5bxgdmkrZQE=
			</data>
		</dict>
		<key>MonoBundle/System.IO.Compression.FileSystem.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			6H+0imwm9jbcPu46+/edD1ZDFDMVoWBy0ANA3tXXgys=
			</data>
		</dict>
		<key>MonoBundle/System.IO.Compression.ZipFile.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			8lR7QYjloYkQ01KbDu578OLIFIGDh88FZaFHm0MLa+g=
			</data>
		</dict>
		<key>MonoBundle/System.IO.Compression.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			O7ml1CmHqZsuriKxaD890Szwa4XaOzpjHh+Fj8Gtjvw=
			</data>
		</dict>
		<key>MonoBundle/System.IO.FileSystem.AccessControl.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ZkmcMpaIpDCuUAKncICpGVShMu+bU13q6zUtvy2atH8=
			</data>
		</dict>
		<key>MonoBundle/System.IO.FileSystem.DriveInfo.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			6Eai4kK7I5jvSdBcwSNmZqQH3KBG4AbGaOBkLJCPCf4=
			</data>
		</dict>
		<key>MonoBundle/System.IO.FileSystem.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			FX7Lud3GhgZ+6C9TXk0vawkNPnz8vKmHmmGNatT8RPs=
			</data>
		</dict>
		<key>MonoBundle/System.IO.FileSystem.Watcher.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			+TcYzwBXwVKJJ8cSnN+CXgYko599R6GUHIEPTDyaBwY=
			</data>
		</dict>
		<key>MonoBundle/System.IO.FileSystem.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			vWBK3asV2p/e4WZoG02iGGC93Rse5+Hpwp9+GiCoGOc=
			</data>
		</dict>
		<key>MonoBundle/System.IO.IsolatedStorage.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			MoQbd86j0FBI6ThFBnGWz1Ms/MJwbDTJc6mpGwPDKYM=
			</data>
		</dict>
		<key>MonoBundle/System.IO.MemoryMappedFiles.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			eHo263M/t6+hc/UorJmAdJTeR/c1bg+AHcZs5+4YV6M=
			</data>
		</dict>
		<key>MonoBundle/System.IO.Pipelines.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			wjXCMHe+pD+XBRbYPN6IhQiHnMmIDkGAkteYSqQdO4s=
			</data>
		</dict>
		<key>MonoBundle/System.IO.Pipes.AccessControl.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			NXofa9ETTBvEnHVK0cwrs43pQOpAkUaB7W2xCayGf3c=
			</data>
		</dict>
		<key>MonoBundle/System.IO.Pipes.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			fgjBmPvuD1P3xjSsjV3ejTooMdcQ7OkosEFXvy1mFg8=
			</data>
		</dict>
		<key>MonoBundle/System.IO.UnmanagedMemoryStream.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			fwAXIGrN1FLL8fEz0YgvDNCV+Lo2huN1LGSmTkaLLQs=
			</data>
		</dict>
		<key>MonoBundle/System.IO.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			vqFRwrPebNgLfiazypEUF0O4eLBdnBl2CJljzP6djlE=
			</data>
		</dict>
		<key>MonoBundle/System.Linq.Expressions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			IA0asiXCDLQn5lVO25x/G2yRQgKAOkS91GGyUB7hLtM=
			</data>
		</dict>
		<key>MonoBundle/System.Linq.Parallel.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			STx5dljAzGtv08O39hWexP0aH1PGd3eCJ8xgIFiFGg0=
			</data>
		</dict>
		<key>MonoBundle/System.Linq.Queryable.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			0Mzc7UHLd++QJgSuwbpCko8Nx4YdbGKErsKGkTIJbto=
			</data>
		</dict>
		<key>MonoBundle/System.Linq.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			xsT7o2m0La+xQE3hIQzQ1jZW3FwPefYz/CwdwZnMdQ4=
			</data>
		</dict>
		<key>MonoBundle/System.Memory.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			btDlcynA7b+lAj2/WNi39AkdvtjrUZduSEpy0FB/oDw=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Http.Json.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			2ATS/FGv/QzXdjLT4PWxb0tIZA4hyeb0KOE5UzFIihY=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Http.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			xCrGYZuTVXavNn8+ptF1hu18wUlcJ/dcHopjBpJ4tRU=
			</data>
		</dict>
		<key>MonoBundle/System.Net.HttpListener.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ngNMdYHGkLffVn0DcmbXvqP6Eykjqk7BVhwQSa+siEQ=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Mail.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			WMJb/eu/iP0uEFafZ1gqV9wfRCxh/Y9m9JjXV1w/zeU=
			</data>
		</dict>
		<key>MonoBundle/System.Net.NameResolution.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			g9gsoeXYwjfPJqGxvnkFnSO6XPPcnjkQaL5ut1uOOzA=
			</data>
		</dict>
		<key>MonoBundle/System.Net.NetworkInformation.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			yU6v1C+Y5CC3q4KgJN3PIRgqcRP72k0U1jKGIWc7FBE=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Ping.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			jEkFBN6EmIDEN4kxB6pABdXUX9OhC8nag6TXSA2/Oac=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ND9KzyC6oxJGZRloyckg1AO54pzFxLhonSjmfPO+w6g=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Quic.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			GjJBe72FmEuDC172vBOG7LczxSftPC5zLsEa3JfN+eo=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Requests.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HX1J8cJZRsWqYD7wT4VnSy8rAwg0K/NF3QcppVyxw7g=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Security.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			YXVG0F0RcjdTNmbp/2emr2SV0VVdpFZgU6Yzpmflhqk=
			</data>
		</dict>
		<key>MonoBundle/System.Net.ServicePoint.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			eteZK1rJnwJrIzo269P+Mg7Ok+FKLvlXbndeCpklfYA=
			</data>
		</dict>
		<key>MonoBundle/System.Net.Sockets.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			zW+Rv9ZYg+rg5j+o+r03O7fPCWDHrwnOAAsGch2o5Ys=
			</data>
		</dict>
		<key>MonoBundle/System.Net.WebClient.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			MDVBX0KXlKajh3CjAekyFcA9a5X3bwE2Y8tyN3cUF9k=
			</data>
		</dict>
		<key>MonoBundle/System.Net.WebHeaderCollection.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			FFoAnKQVfZmurVr/KRxXbJXXt85RQa21+PoUCqgLFng=
			</data>
		</dict>
		<key>MonoBundle/System.Net.WebProxy.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			sxTHwXYAp8FPMlbbURUvrgvbmqvEdU2CiJK1Gn6papU=
			</data>
		</dict>
		<key>MonoBundle/System.Net.WebSockets.Client.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			rxIFoQaZ6vOhIFDwof0Mj5m/n/eK9h0kmI3myN8jkcs=
			</data>
		</dict>
		<key>MonoBundle/System.Net.WebSockets.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ufuAZzPhfKehq+U4/Uzs8K4eWv+tmsrL8bxRj0ZjVHQ=
			</data>
		</dict>
		<key>MonoBundle/System.Net.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			EVQ6XNBiz0KeSy9wQWLk8ny6IJe95mh17oYIVD951kI=
			</data>
		</dict>
		<key>MonoBundle/System.Numerics.Vectors.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			/YyCpqTYtIj6OWmTc4GTKarj6Fe1SwCtqjGeGB2Mews=
			</data>
		</dict>
		<key>MonoBundle/System.Numerics.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HDsTI3YKAdu6RGJw3cC4t/Fbe+xwuZW2e2eIt5W4TPk=
			</data>
		</dict>
		<key>MonoBundle/System.ObjectModel.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			7qcHa4WT1phep4THm0FEa5UovE4keO+RaiTxPP/z9jg=
			</data>
		</dict>
		<key>MonoBundle/System.Private.CoreLib.aotdata.arm64</key>
		<dict>
			<key>hash2</key>
			<data>
			6osL+2HsHaUGVHxB1B5BiTb16TkBcjQ+abeikt0BJMo=
			</data>
		</dict>
		<key>MonoBundle/System.Private.CoreLib.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			eHsIaI1gGXEgRr1l9yF7h1s5K0rnMcG2zT2Oex9twCo=
			</data>
		</dict>
		<key>MonoBundle/System.Private.DataContractSerialization.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			wDPc456JzVQYrLu+hDWU+L1NMsfiklVxIg4hajIeDRw=
			</data>
		</dict>
		<key>MonoBundle/System.Private.Uri.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			EHEBcQfvp+pCfjS+EIl9oUkmklEC9ToHtOWDRZlynSA=
			</data>
		</dict>
		<key>MonoBundle/System.Private.Xml.Linq.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Zjyk4wn7ZXQJf0qI65EBP4e8bbx0iw+rXxAWaKNhLG4=
			</data>
		</dict>
		<key>MonoBundle/System.Private.Xml.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			dzBobPrA/MCccP0ZCt+MflU7N9O7FUGUq0v2IClAJkw=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.DispatchProxy.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			OX4NwkHcaq0/z5tR4N5FIh/P1UStAIiAI4M/BIP0UEo=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.Emit.ILGeneration.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			upBjcTxrBhd+HCgFZ3BmvMxlYG4CBeuybrQ6O3qRtyE=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.Emit.Lightweight.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			jioLQP+JN463cXY7NIZYOk9FrdccmzgNTNQi019tig4=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.Emit.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kuOTwwBYDyaZQFSUqQBQk4oBWmkgCoK28L16M7SfuG0=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.Extensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			dLrdHrcmvQgh8EOPsmLdZHVnGDZlVNyoAdriCvd+9SI=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.Metadata.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			s9isZdIWO80mMWezbTRTxKsMqyr8eCN/322LMDNoyVM=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			FPKD1jEGhWtoOdRfi2yhfJZl3GAJ4OwkS4XEYvvr5Yo=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.TypeExtensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			iBhDVc9iNxrlL0a9VrrtkkkVLWwXZw65lGvt1MIeBK0=
			</data>
		</dict>
		<key>MonoBundle/System.Reflection.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			A4CXWEqPLX3PKU6anth5QqGYcsMyjX180Gr89UFEYa4=
			</data>
		</dict>
		<key>MonoBundle/System.Resources.Reader.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			pONZ3lpMOwyQlGozlUPJu364avTUrm8oGosDhBMh2w4=
			</data>
		</dict>
		<key>MonoBundle/System.Resources.ResourceManager.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			fRn6WQudAU0f97qHDxlH+aEsbXqNJJ8JNrrARoj8tJs=
			</data>
		</dict>
		<key>MonoBundle/System.Resources.Writer.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kobGSwt3oN8ZijRDKt/VN8L6ovOR5IQSmlOIpdSmRqc=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.CompilerServices.Unsafe.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			cZzp/dvJES+Ja/2Z+gW6Stx8E9z336JMlZKlX1YtQq0=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.CompilerServices.VisualC.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Fr+LyoMrGy2ZuDPEJwSMuxsrhObY8Jovvpc3gAZ5cV4=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Extensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			NjmFa6TEFfg/9JWdgagvVG/I1Ao4lV3WAYcA2l1splo=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Handles.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Gme5NXpWGAsvhTDJvNLqV2dK045p7RWfFiJv5meLEdA=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.InteropServices.JavaScript.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ZiH9F71Xg+W23poiGNlCLy2Cgb1pdMAUeKw5jRMCcd0=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.InteropServices.RuntimeInformation.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Nfg+lQ1+SyXf8l0q6FkOkbqAplPbLi6LhqhYgwRvmd4=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.InteropServices.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			SSpa1qIsHqHgRabpCYa9OZgW9Gnp3JLbhqdeukg3oSU=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Intrinsics.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			dqIt0lrwE7za3nTwpPkrdehdjO1zMahJzTsO+jr+Xt0=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Loader.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			aLABpk0ZYJZHFxOp/SkwLoGHHplNtQF04Ff5/DxuajY=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Numerics.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			H/ZKM5otLviWiFShG/veUEQzwMOQulOkd5Xk3XBHBhY=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Serialization.Formatters.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			jx21caQFsXs4Fr8zsE//cJstGfZZbkQEJ6bTKJfQhvE=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Serialization.Json.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			xjKTxks901UoAnbKFTQqAdn4f0eHK6syNsADe3kuaxw=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Serialization.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			9nH7Fz/puopXJ5axIjA2Rj67nVzjtGihwEXcvQp+fTk=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Serialization.Xml.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			8bO5ZqWuEYHxlrt1HOswgA8wyTufslaBJ3LK6CTHSYQ=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.Serialization.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			4rrT+YL/Y1MIi86u450osDtOZ1//mgUIWoACJxwSPq0=
			</data>
		</dict>
		<key>MonoBundle/System.Runtime.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			IQvwVYVnW1NgqTKt3k4rteYY7hr89LQLK3pz1+k+8EQ=
			</data>
		</dict>
		<key>MonoBundle/System.Security.AccessControl.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			RzSQbCYEQet3ewhObvNnDiLPTLn7ixPgHa3YV4gz0ec=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Claims.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ikmpftIqiBj7oSqCr12V+RPw+8tJf8kUUz1gt0iIX8Y=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.Algorithms.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			gPayriMy/n8W2sIYPN0iARhP69vJ1v8lR5uyHdsX/04=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.Cng.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			YxF4sOJI8kBRpaSX79y9Hy3Zm6U6nlomf794ueMeVJo=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.Csp.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			bHlhBELAHi17DaS7PhBpJW61qz6VGJrRrSGDdbPn9Yo=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.Encoding.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HNvpYdk+mOmeLqG7uSHqxiG+nblLBGWQ2Z6F0vramE8=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.OpenSsl.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			i9SU7T9yo7wohtXBt/QvxGnUQQx7euChnRNd/JN8FBw=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			z4gmSOo5PMohe8ntcmcpMEVzNM7n6EqZenD4eo3SMdg=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.X509Certificates.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			mZFa9bhkuGZqt8w6rVX+N/URd8K5vneMY9SnVRyANuI=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Cryptography.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			E15RI3imEJw11nC52XgjPCe3/r/axZBzZzoQMDuKzcc=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Principal.Windows.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			XZEZUlWWbs30/dnMK8HNSu5nn78ksuGzoIdaxYsBijY=
			</data>
		</dict>
		<key>MonoBundle/System.Security.Principal.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Une+uuNHtvbOGGgcfwh49FkrSldUNbGSDePdCgyM720=
			</data>
		</dict>
		<key>MonoBundle/System.Security.SecureString.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			X/uB3APxc7mPioNmU8aa54l4ox2i+PjLvKTSZ2buNQY=
			</data>
		</dict>
		<key>MonoBundle/System.Security.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			26qu/oNFDkHoZQB+xpNP+8Lx4duz0/ys/haXpaQa4hs=
			</data>
		</dict>
		<key>MonoBundle/System.ServiceModel.Web.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			KsI2mB7H5MoN1khkmeuk9urfA0d5q2f4RLIdze67WCU=
			</data>
		</dict>
		<key>MonoBundle/System.ServiceProcess.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			mx6Cpb+cpkLZ78r1lj+pCenwatBUR6+wApF0fL11ERw=
			</data>
		</dict>
		<key>MonoBundle/System.Text.Encoding.CodePages.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			6+7J+dwCZ14S9I50kKKIDFauPhIoJp1nuLyuS6pbUMM=
			</data>
		</dict>
		<key>MonoBundle/System.Text.Encoding.Extensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Vo+KvcsJthw8Qpznh7tWhV3VXEkln3WbyPDU7zNPvUU=
			</data>
		</dict>
		<key>MonoBundle/System.Text.Encoding.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Ce/RQgLMwJ2iqkzTfWrqCv5DN8auxJAUQV2jxHP2nf0=
			</data>
		</dict>
		<key>MonoBundle/System.Text.Encodings.Web.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			YQeMjKQiYvvWVTLEjB5bMB1r0jbIzaR3VRm8aEojVs8=
			</data>
		</dict>
		<key>MonoBundle/System.Text.Json.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			V0MwB7L7ecpUrMfanXod/DtJkyfbQUN3JnuawzZgfbk=
			</data>
		</dict>
		<key>MonoBundle/System.Text.RegularExpressions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			PesPrIQWP/We2CYZ6vuWJtyY87fOk6jiLUT36/C4N8Y=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Channels.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			r3tHboySjY7Q3e6vnCzXuz7jyWZWN/Nz6JzS+ioXfM4=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Overlapped.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			imEamESJGGqkd2nnBbChTaYQNM7YQBjifmfGEvjGhsY=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Tasks.Dataflow.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			A1zwb2OZsW+cT7HYagxS2D52apmTTDbJJmr0qw6upxs=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Tasks.Extensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			rVhfaN9OJus1m9BrHfhIHWuCR8WbFCVEMQWS/33L9YY=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Tasks.Parallel.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			MY9r1SFcn/y5HXV3HgYCATGt/jafXovDs4fy5jCnO5w=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Tasks.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			XvubqtTBI0wGQXETt9CxaVtMhsD+te9K+LO6hGRxJIQ=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Thread.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			n1ZJ7xzlhZBZ/Eb2pNXJNbd6Aepsd57Qdjkg2j5Bh1k=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.ThreadPool.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			DbqikjTBpq80bqVsOOs7BHDs/3SBzdOpDwJLERo9Hug=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.Timer.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			C7sFnwB1s7Qg7ZZf8KHRu/bM+pT6Wxtgiz2cjkB/ay8=
			</data>
		</dict>
		<key>MonoBundle/System.Threading.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			xUzf1ldZp2AodjiJeJud3t48pl3BXyDCG49tUctJjrc=
			</data>
		</dict>
		<key>MonoBundle/System.Transactions.Local.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			IzGmJYh4iSs9VcPTDkqBIIc5Z5mEk4aj+I715q4ZtDE=
			</data>
		</dict>
		<key>MonoBundle/System.Transactions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ayEVkdA3flky7V7Zgi82/VuwGQUmVSooCglobvVxM6s=
			</data>
		</dict>
		<key>MonoBundle/System.ValueTuple.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Sq0peeAB09fT9YLQcwudc1Rh/TStYUs/a4acSCZN7W0=
			</data>
		</dict>
		<key>MonoBundle/System.Web.HttpUtility.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			y6sc8VKOF7i6bibMeiLOU1H69n1EIhv9vX8ZylEhJpE=
			</data>
		</dict>
		<key>MonoBundle/System.Web.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			X4DCUY+ErQBbQWT6h527kWfg/cHLv+GQjyVndEufyJs=
			</data>
		</dict>
		<key>MonoBundle/System.Windows.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			fVALEgXQMIjczOTa+BhEBw25or83He9f5tiVtYxjiyw=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.Linq.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			OWn8LHsJ0yQdxXxc7NJgTu8Ijf3SDoBfapaCYvdj2MM=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.ReaderWriter.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			5m8ng2phV+H6dAgafbFPt0K2q4M3PyFu7AW3SuiaaH4=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.Serialization.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			18Qa0xL9+HiEQgHnf8u+SGHDOS/cgOVqSZsFCvIQepw=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.XDocument.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			lLu92MpUKswVPaHJ7WOi7HOUePFb/bPQekP3ns8ZUIc=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.XPath.XDocument.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			rRFX/KgSgDBRhkz06HjKPMkOT+kgbI+SEPsBEdb0Kt4=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.XPath.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			bfRbDEG3d2aBMosYF9IvcRo42Lubg2+9spr3eu1tNDc=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.XmlDocument.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			7cy0WEwa6blxWCS1jr88fblUxg8n4Th5iuWyF4CG/Xs=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.XmlSerializer.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HqoZr/KlILoCm7uJEM3tsuKxFSpTQlvXF2JkpRM2gOg=
			</data>
		</dict>
		<key>MonoBundle/System.Xml.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HSjwmBym9sKGfSbZivN0mDW10aJYZe50jZJR4f1OPnw=
			</data>
		</dict>
		<key>MonoBundle/System.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ohH4aCBv3cmK+2Sxtz5yuwKMFf5S0pjujnGFTzpRHgI=
			</data>
		</dict>
		<key>MonoBundle/WindowsBase.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			1UiNq/2Se//cgzJmGonxdz7r0bIA8UlmPumV6KEf+nc=
			</data>
		</dict>
		<key>MonoBundle/ar/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HLqJRI6Ui3NjtHLsoiNGgp7r8fQwRgvAE9HQGaKuhJ8=
			</data>
		</dict>
		<key>MonoBundle/ca/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			bu2K9kPtjgP+Mnd2BwCqOsTJ+dih31AYAJTWwx78JYg=
			</data>
		</dict>
		<key>MonoBundle/cs/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			8P4KuGeDt31nWdbXbI6K/HE44rdLngcvAEcNnmyYINE=
			</data>
		</dict>
		<key>MonoBundle/da/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			5n0ZDDZCNqvGPcBDMX3RicgplnIHkpOtlA8eytFogvA=
			</data>
		</dict>
		<key>MonoBundle/de/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			hk980Wky0Z3kRgYd2+9FWPS91vq7NuUQqgkmGIdlEaM=
			</data>
		</dict>
		<key>MonoBundle/el/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			EgKlEg/CoSsIFd+Ac/qFx8+nXi71cUmvPjg8Eq1KwcE=
			</data>
		</dict>
		<key>MonoBundle/es/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			nXG/HclVc8fQ5CiHN5PRo1i7XqnfZwPytVNMyeTczRY=
			</data>
		</dict>
		<key>MonoBundle/fi/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kQZLvcyzU/2KOEgRxkBb8YkVd69+cHKfm4vk9ds3ip0=
			</data>
		</dict>
		<key>MonoBundle/fr/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			KOsyciw+nzTm9O6YOm43wjm5Ihhz6BxptGqXmoLd/Bk=
			</data>
		</dict>
		<key>MonoBundle/he/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			witgr7eRK10lOP0HFdtXOhrV9ZqUgrAXwiFU0r4tu+Y=
			</data>
		</dict>
		<key>MonoBundle/hi/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			sMm8gIzyFPgyuOZDfPLFYVfrTnODxLDP3MmjnuzCQb8=
			</data>
		</dict>
		<key>MonoBundle/hr/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Eb0XsP6vnxGnhpKVOUSKuU6MEeMJ6Ernb739+ygSwqY=
			</data>
		</dict>
		<key>MonoBundle/hu/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			IfZssnZFwgK3CZ2VxfLmh954MY4AwD24OoboTMQ9cFw=
			</data>
		</dict>
		<key>MonoBundle/id/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			rRZ9A/Gn3raWLd/fBE6oQJIg1MJxb7iIaGk5vbvabpY=
			</data>
		</dict>
		<key>MonoBundle/it/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			MEzy+qamhVR1BBCXmhUMnQEkOdVeRlsWh26+1hgS3oA=
			</data>
		</dict>
		<key>MonoBundle/ja/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			+Z/VRnFkG43fqVLKNGREHOY2ZeNHdq1xU2FmfDnXhE8=
			</data>
		</dict>
		<key>MonoBundle/ko/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			BboUsOxkmWiw62GqR58cwXwInKB9NvzRULCoW/vpQgM=
			</data>
		</dict>
		<key>MonoBundle/ms/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			lfDQzvNCOXNCxiwPbb1n2Zl9sw5V24HtamdGE5eIl1U=
			</data>
		</dict>
		<key>MonoBundle/mscorlib.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			UAxxsSBnfzhDVPRNm5Bn62nTBK6vVRmk5HJHu7tqDrA=
			</data>
		</dict>
		<key>MonoBundle/nb/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			jTuOOfIY7TdtdxORL4t9P+0w120kYOqS9/8G8wXpYaE=
			</data>
		</dict>
		<key>MonoBundle/netstandard.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			34f9kUv+TdUzfWxrPTE2Q7B8OOPT+EV/08+Q7VTV5MU=
			</data>
		</dict>
		<key>MonoBundle/nl/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			hqPp3S6ZP9qIkk83/pDSQC+R3zRmjQtBFHeEpH7I+cI=
			</data>
		</dict>
		<key>MonoBundle/pl/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			g7dDtJ11RZ3MrH7LsrpTftMZI/hOgnM3sJjpU+8L6Qs=
			</data>
		</dict>
		<key>MonoBundle/pt-BR/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			1H/Lq4RPQh9oJ059WvSC6J1vT7L5P8vS/hkI45ojB+w=
			</data>
		</dict>
		<key>MonoBundle/pt/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HW2cO4wjuD5AHad9nk4Ipc5OFjODiAxZ151bNHh9Szg=
			</data>
		</dict>
		<key>MonoBundle/ro/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			BjYAM7vZXV1cTDqhC5W4i+u5I8Xd6JQfrZJJCco6RCE=
			</data>
		</dict>
		<key>MonoBundle/ru/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			evlUVIK6jLKvowIv5NKSks9lyFRrAqY8hslvV+h/EpE=
			</data>
		</dict>
		<key>MonoBundle/runtimeconfig.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			ukI0fdPR7dGn2kpmuj/bboWWo7QDFLau3KPDDx3bHBI=
			</data>
		</dict>
		<key>MonoBundle/sk/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			RXPRp/OFg/53tDYBdx+pJsCTzh1jaAt6VH2WT7DjrLI=
			</data>
		</dict>
		<key>MonoBundle/sv/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ytzPCpjBIOJoB3UuDY/zgtFpwQL9RDYVm7y1ABPnNFE=
			</data>
		</dict>
		<key>MonoBundle/th/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Sm2qB82JuZqfzDU9/R4zhtgwS3PxFuCMIN4gQbLWEPk=
			</data>
		</dict>
		<key>MonoBundle/tr/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			TjS1rC/q70kdWWaXXz+SIUJ9B/imK4iRfyCc+ewgxCM=
			</data>
		</dict>
		<key>MonoBundle/uk/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			qZ1nz6iQevlq6vq1IPsT9T8Ce/6YKfAFctOo50H9msI=
			</data>
		</dict>
		<key>MonoBundle/vi/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			5+yVFdu6d0urHCETuORet3ywkXSnceI+OaOiNLLgkBo=
			</data>
		</dict>
		<key>MonoBundle/zh-HK/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			sTe9q05Q2oZ/Tw4n93zmk2K+MZ7KEy6LoXhoRG05hd4=
			</data>
		</dict>
		<key>MonoBundle/zh-Hans/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			oRxzT9kfK5IdOG0FpcMBQlR7hT1imhQZv5WWRi0QqIw=
			</data>
		</dict>
		<key>MonoBundle/zh-Hant/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			emXX8EN6/tiHQIE8KVU/HIMEWzHTCpRgkel8L0KBcgQ=
			</data>
		</dict>
		<key>Resources/AboutAssets.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			fVnsP1e2L88aqi4aLkmySlc7msxv3FPDQxEKAAgiips=
			</data>
		</dict>
		<key>Resources/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			DAiYBGf6aLF5ncs3VEHofu0lGEwefyuwlqYEXZWmAy0=
			</data>
		</dict>
		<key>Resources/MauiInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			tx0KXqEC+DvcCGyogQTEQWHcCWJYYqH24NtPwhpQEjo=
			</data>
		</dict>
		<key>Resources/MonoTouchDebugConfiguration.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			RS51BO98d+zhJWK1abJOyD5jB4nYJnWJPtL2DZyA/zw=
			</data>
		</dict>
		<key>Resources/OpenSans-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			A3I27Uv1ioX2cHTBZdMIJg/WvgHIbX30556hbrJz+MU=
			</data>
		</dict>
		<key>Resources/OpenSans-Semibold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Xo2eGokIPNGwhJmT/i86zJqjO39Dn36GFocvaJfzBoQ=
			</data>
		</dict>
		<key>Resources/appicon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			PuksySzAJl/9rD6oi1A7fdpthEHAQvhJHwFvLytRMxo=
			</data>
		</dict>
		<key>Resources/dotnet_bot.png</key>
		<dict>
			<key>hash2</key>
			<data>
			p7zGimT/tpOpt6JMjvqTXeLvr7gO+6owAxCI06IQS70=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			WxfuPXP2HzlHzcT6HPpxjHyIjhUetCXyRrIRo7hyF2Q=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			a/Og/prhaP+ksiVrPdm6zyz46YfBdrD0oFdkAveKI18=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
