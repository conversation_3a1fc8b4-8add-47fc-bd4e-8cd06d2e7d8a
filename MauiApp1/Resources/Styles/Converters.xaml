<?xml version="1.0" encoding="UTF-8" ?>
<ResourceDictionary xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
                    xmlns:converters="clr-namespace:MauiApp1.Converters">

    <converters:InvertedBoolConverter x:Key="InvertedBoolConverter" />
    <converters:TaxCalculatorConverter x:Key="TaxCalculatorConverter" />
    <converters:TotalWithTaxConverter x:Key="TotalWithTaxConverter" />
    <converters:PaymentMethodIconConverter x:Key="PaymentMethodIconConverter" />
    <converters:PaymentMethodToCashVisibilityConverter x:Key="PaymentMethodToCashVisibilityConverter" />
    <converters:GreaterThanZeroConverter x:Key="GreaterThanZeroConverter" />

</ResourceDictionary>
