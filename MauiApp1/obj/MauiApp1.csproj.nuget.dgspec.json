{"format": 1, "restore": {"/Users/<USER>/Desktop/maui apps/MauiApp1/MauiApp1.csproj": {}}, "projects": {"/Users/<USER>/Desktop/maui apps/MauiApp1/MauiApp1.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/maui apps/MauiApp1/MauiApp1.csproj", "projectName": "MauiApp1", "projectPath": "/Users/<USER>/Desktop/maui apps/MauiApp1/MauiApp1.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/maui apps/MauiApp1/obj/", "projectStyle": "PackageReference", "crossTargeting": true, "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0-maccatalyst"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-maccatalyst18.5": {"targetAlias": "net9.0-maccatalyst", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-maccatalyst18.5": {"targetAlias": "net9.0-maccatalyst", "dependencies": {"Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.82, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.8, )", "autoReferenced": true}}, "imports": ["xamarinios10", "net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.MacCatalyst": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.304/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"maccatalyst-arm64": {"#import": []}}}}}