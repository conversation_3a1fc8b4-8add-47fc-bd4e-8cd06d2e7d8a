is_global = true
build_property.EnableAotAnalyzer = 
build_property.EnableSingleFileAnalyzer = 
build_property.EnableTrimAnalyzer = false
build_property.IncludeAllContentForSelfExtract = 
build_property.TargetFramework = net9.0-maccatalyst
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = false
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = MauiApp1
build_property.ProjectDir = /Users/<USER>/Desktop/maui apps/MauiApp1/
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[/Users/<USER>/Desktop/maui apps/MauiApp1/App.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MauiApp1.App.xaml
build_metadata.AdditionalFiles.TargetPath = App.xaml
build_metadata.AdditionalFiles.RelativePath = App.xaml

[/Users/<USER>/Desktop/maui apps/MauiApp1/AppShell.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MauiApp1.AppShell.xaml
build_metadata.AdditionalFiles.TargetPath = AppShell.xaml
build_metadata.AdditionalFiles.RelativePath = AppShell.xaml

[/Users/<USER>/Desktop/maui apps/MauiApp1/MainPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MauiApp1.MainPage.xaml
build_metadata.AdditionalFiles.TargetPath = MainPage.xaml
build_metadata.AdditionalFiles.RelativePath = MainPage.xaml

[/Users/<USER>/Desktop/maui apps/MauiApp1/Resources/Styles/Colors.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MauiApp1.Resources.Styles.Colors.xaml
build_metadata.AdditionalFiles.TargetPath = Resources/Styles/Colors.xaml
build_metadata.AdditionalFiles.RelativePath = Resources/Styles/Colors.xaml

[/Users/<USER>/Desktop/maui apps/MauiApp1/Resources/Styles/Converters.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MauiApp1.Resources.Styles.Converters.xaml
build_metadata.AdditionalFiles.TargetPath = Resources/Styles/Converters.xaml
build_metadata.AdditionalFiles.RelativePath = Resources/Styles/Converters.xaml

[/Users/<USER>/Desktop/maui apps/MauiApp1/Resources/Styles/Styles.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MauiApp1.Resources.Styles.Styles.xaml
build_metadata.AdditionalFiles.TargetPath = Resources/Styles/Styles.xaml
build_metadata.AdditionalFiles.RelativePath = Resources/Styles/Styles.xaml

[/Users/<USER>/Desktop/maui apps/MauiApp1/Views/CartPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MauiApp1.Views.CartPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/CartPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/CartPage.xaml

[/Users/<USER>/Desktop/maui apps/MauiApp1/Views/PaymentPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MauiApp1.Views.PaymentPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/PaymentPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/PaymentPage.xaml
