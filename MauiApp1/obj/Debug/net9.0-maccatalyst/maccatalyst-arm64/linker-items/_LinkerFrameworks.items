﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <_LinkerFrameworks Include="Accelerate">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="Accessibility">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="Accounts">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="AddressBook">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="AdServices">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="AdSupport">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="AppClip">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="AppKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="AppTrackingTransparency">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="AudioToolbox">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="AuthenticationServices">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="AutomaticAssessmentConfiguration">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="AVFoundation">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="AVKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="BackgroundTasks">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="BusinessChat">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CallKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CFNetwork">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="ClassKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CloudKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="Contacts">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="ContactsUI">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreAudio">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreAudioKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreBluetooth">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreData">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreFoundation">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreGraphics">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreHaptics">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreImage">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreLocation">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreLocationUI">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreMedia">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreMIDI">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreML">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreMotion">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreServices">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreSpotlight">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreTelephony">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreText">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreVideo">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreWLAN">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CryptoTokenKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="DataDetection">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="DeviceCheck">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="EventKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="EventKitUI">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="ExternalAccessory">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="FileProvider">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="Foundation">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="GameController">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="GameKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="GameplayKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="HealthKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="HealthKitUI">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="HomeKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="IdentityLookup">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="IdentityLookupUI">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="ImageIO">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="Intents">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="IntentsUI">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="IOSurface">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="JavaScriptCore">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="LinkPresentation">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="LocalAuthentication">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="MapKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="MediaAccessibility">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="MediaPlayer">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="MediaToolbox">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="Messages">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="MessageUI">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="Metal">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="MetalKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="MetalPerformanceShaders">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="MetalPerformanceShadersGraph">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="MetricKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="MLCompute">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="MobileCoreServices">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="ModelIO">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="MultipeerConnectivity">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="NaturalLanguage">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="NearbyInteraction">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="Network">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="NetworkExtension">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="OSLog">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="PassKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="PDFKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="PencilKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="PHASE">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="Photos">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="PhotosUI">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="PushKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="QuartzCore">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="QuickLook">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="QuickLookThumbnailing">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="ReplayKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="SafariServices">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="SceneKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="ScreenTime">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="Security">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="ShazamKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="Social">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="SoundAnalysis">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="Speech">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="SpriteKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="StoreKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="SystemConfiguration">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="UIKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="UniformTypeIdentifiers">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="UserNotifications">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="UserNotificationsUI">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="VideoToolbox">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="Vision">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="VisionKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="WebKit">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="AVRouting">
      <IsWeak>true</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="BackgroundAssets">
      <IsWeak>true</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="CoreNFC">
      <IsWeak>true</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="DeviceDiscoveryExtension">
      <IsWeak>true</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="ExecutionPolicy">
      <IsWeak>true</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="ScreenCaptureKit">
      <IsWeak>true</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="SecurityUI">
      <IsWeak>true</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="SensitiveContentAnalysis">
      <IsWeak>true</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="SensorKit">
      <IsWeak>true</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="ServiceManagement">
      <IsWeak>true</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="SharedWithYou">
      <IsWeak>true</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="SharedWithYouCore">
      <IsWeak>true</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="Symbols">
      <IsWeak>true</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="ThreadNetwork">
      <IsWeak>true</IsWeak>
    </_LinkerFrameworks>
    <_LinkerFrameworks Include="GSS">
      <IsWeak>false</IsWeak>
    </_LinkerFrameworks>
  </ItemGroup>
</Project>