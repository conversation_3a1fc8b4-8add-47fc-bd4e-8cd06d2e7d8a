package crc64338477404e88479c;


public class ModalNavigationManager_ModalFragment_CustomComponentDialog
	extends androidx.activity.ComponentDialog
	implements
		mono.android.IGCUserPeer
{
/** @hide */
	public static final String __md_methods;
	static {
		__md_methods = 
			"";
		mono.android.Runtime.register ("Microsoft.Maui.Controls.Platform.ModalNavigationManager+ModalFragment+CustomComponentDialog, Microsoft.Maui.Controls", ModalNavigationManager_ModalFragment_CustomComponentDialog.class, __md_methods);
	}

	public ModalNavigationManager_ModalFragment_CustomComponentDialog (android.content.Context p0)
	{
		super (p0);
		if (getClass () == ModalNavigationManager_ModalFragment_CustomComponentDialog.class) {
			mono.android.TypeManager.Activate ("Microsoft.Maui.Controls.Platform.ModalNavigationManager+ModalFragment+CustomComponentDialog, Microsoft.Maui.Controls", "Android.Content.Context, Mono.Android", this, new java.lang.Object[] { p0 });
		}
	}

	public ModalNavigationManager_ModalFragment_CustomComponentDialog (android.content.Context p0, int p1)
	{
		super (p0, p1);
		if (getClass () == ModalNavigationManager_ModalFragment_CustomComponentDialog.class) {
			mono.android.TypeManager.Activate ("Microsoft.Maui.Controls.Platform.ModalNavigationManager+ModalFragment+CustomComponentDialog, Microsoft.Maui.Controls", "Android.Content.Context, Mono.Android:System.Int32, System.Private.CoreLib", this, new java.lang.Object[] { p0, p1 });
		}
	}

	private java.util.ArrayList refList;
	public void monodroidAddReference (java.lang.Object obj)
	{
		if (refList == null)
			refList = new java.util.ArrayList ();
		refList.add (obj);
	}

	public void monodroidClearReferences ()
	{
		if (refList != null)
			refList.clear ();
	}
}
