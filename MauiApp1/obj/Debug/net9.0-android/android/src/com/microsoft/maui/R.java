/* AUTO-GENERATED FILE. DO NOT MODIFY.
 *
 * This class was automatically generated by
 * .NET for Android from the resource data it found.
 * It should not be modified by hand.
 */
package com.microsoft.maui;

public final class R {
	public static final class anim {
		public static final int abc_fade_in = 0x7f010000;
		public static final int abc_fade_out = 0x7f010001;
		public static final int abc_grow_fade_in_from_bottom = 0x7f010002;
		public static final int abc_popup_enter = 0x7f010003;
		public static final int abc_popup_exit = 0x7f010004;
		public static final int abc_shrink_fade_out_from_bottom = 0x7f010005;
		public static final int abc_slide_in_bottom = 0x7f010006;
		public static final int abc_slide_in_top = 0x7f010007;
		public static final int abc_slide_out_bottom = 0x7f010008;
		public static final int abc_slide_out_top = 0x7f010009;
		public static final int abc_tooltip_enter = 0x7f01000a;
		public static final int abc_tooltip_exit = 0x7f01000b;
		public static final int btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c;
		public static final int btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d;
		public static final int btn_checkbox_to_checked_icon_null_animation = 0x7f01000e;
		public static final int btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f;
		public static final int btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010;
		public static final int btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011;
		public static final int btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012;
		public static final int btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013;
		public static final int btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014;
		public static final int btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015;
		public static final int btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016;
		public static final int btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017;
		public static final int design_bottom_sheet_slide_in = 0x7f010018;
		public static final int design_bottom_sheet_slide_out = 0x7f010019;
		public static final int design_snackbar_in = 0x7f01001a;
		public static final int design_snackbar_out = 0x7f01001b;
		public static final int fragment_fast_out_extra_slow_in = 0x7f010020;
		public static final int linear_indeterminate_line1_head_interpolator = 0x7f010021;
		public static final int linear_indeterminate_line1_tail_interpolator = 0x7f010022;
		public static final int linear_indeterminate_line2_head_interpolator = 0x7f010023;
		public static final int linear_indeterminate_line2_tail_interpolator = 0x7f010024;
		public static final int m3_bottom_sheet_slide_in = 0x7f010025;
		public static final int m3_bottom_sheet_slide_out = 0x7f010026;
		public static final int m3_motion_fade_enter = 0x7f010027;
		public static final int m3_motion_fade_exit = 0x7f010028;
		public static final int m3_side_sheet_enter_from_left = 0x7f010029;
		public static final int m3_side_sheet_enter_from_right = 0x7f01002a;
		public static final int m3_side_sheet_exit_to_left = 0x7f01002b;
		public static final int m3_side_sheet_exit_to_right = 0x7f01002c;
		public static final int mtrl_bottom_sheet_slide_in = 0x7f01002d;
		public static final int mtrl_bottom_sheet_slide_out = 0x7f01002e;
		public static final int mtrl_card_lowers_interpolator = 0x7f01002f;
	}
	public static final class animator {
		public static final int design_appbar_state_list_animator = 0x7f020000;
		public static final int design_fab_hide_motion_spec = 0x7f020001;
		public static final int design_fab_show_motion_spec = 0x7f020002;
		public static final int fragment_close_enter = 0x7f020003;
		public static final int fragment_close_exit = 0x7f020004;
		public static final int fragment_fade_enter = 0x7f020005;
		public static final int fragment_fade_exit = 0x7f020006;
		public static final int fragment_open_enter = 0x7f020007;
		public static final int fragment_open_exit = 0x7f020008;
		public static final int m3_appbar_state_list_animator = 0x7f020009;
		public static final int m3_btn_elevated_btn_state_list_anim = 0x7f02000a;
		public static final int m3_btn_state_list_anim = 0x7f02000b;
		public static final int m3_card_elevated_state_list_anim = 0x7f02000c;
		public static final int m3_card_state_list_anim = 0x7f02000d;
		public static final int m3_chip_state_list_anim = 0x7f02000e;
		public static final int m3_elevated_chip_state_list_anim = 0x7f02000f;
		public static final int m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010;
		public static final int m3_extended_fab_change_size_expand_motion_spec = 0x7f020011;
		public static final int m3_extended_fab_hide_motion_spec = 0x7f020012;
		public static final int m3_extended_fab_show_motion_spec = 0x7f020013;
		public static final int m3_extended_fab_state_list_animator = 0x7f020014;
		public static final int mtrl_btn_state_list_anim = 0x7f020015;
		public static final int mtrl_btn_unelevated_state_list_anim = 0x7f020016;
		public static final int mtrl_card_state_list_anim = 0x7f020017;
		public static final int mtrl_chip_state_list_anim = 0x7f020018;
		public static final int mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019;
		public static final int mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a;
		public static final int mtrl_extended_fab_hide_motion_spec = 0x7f02001b;
		public static final int mtrl_extended_fab_show_motion_spec = 0x7f02001c;
		public static final int mtrl_extended_fab_state_list_animator = 0x7f02001d;
		public static final int mtrl_fab_hide_motion_spec = 0x7f02001e;
		public static final int mtrl_fab_show_motion_spec = 0x7f02001f;
		public static final int mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020;
		public static final int mtrl_fab_transformation_sheet_expand_spec = 0x7f020021;
	}
	public static final class attr {
		public static final int actionBarDivider = 0x7f030003;
		public static final int actionBarItemBackground = 0x7f030004;
		public static final int actionBarPopupTheme = 0x7f030005;
		public static final int actionBarSize = 0x7f030006;
		public static final int actionBarSplitStyle = 0x7f030007;
		public static final int actionBarStyle = 0x7f030008;
		public static final int actionBarTabBarStyle = 0x7f030009;
		public static final int actionBarTabStyle = 0x7f03000a;
		public static final int actionBarTabTextStyle = 0x7f03000b;
		public static final int actionBarTheme = 0x7f03000c;
		public static final int actionBarWidgetTheme = 0x7f03000d;
		public static final int actionButtonStyle = 0x7f03000e;
		public static final int actionDropDownStyle = 0x7f03000f;
		public static final int actionLayout = 0x7f030010;
		public static final int actionMenuTextAppearance = 0x7f030011;
		public static final int actionMenuTextColor = 0x7f030012;
		public static final int actionModeBackground = 0x7f030013;
		public static final int actionModeCloseButtonStyle = 0x7f030014;
		public static final int actionModeCloseContentDescription = 0x7f030015;
		public static final int actionModeCloseDrawable = 0x7f030016;
		public static final int actionModeCopyDrawable = 0x7f030017;
		public static final int actionModeCutDrawable = 0x7f030018;
		public static final int actionModeFindDrawable = 0x7f030019;
		public static final int actionModePasteDrawable = 0x7f03001a;
		public static final int actionModePopupWindowStyle = 0x7f03001b;
		public static final int actionModeSelectAllDrawable = 0x7f03001c;
		public static final int actionModeShareDrawable = 0x7f03001d;
		public static final int actionModeSplitBackground = 0x7f03001e;
		public static final int actionModeStyle = 0x7f03001f;
		public static final int actionModeTheme = 0x7f030020;
		public static final int actionModeWebSearchDrawable = 0x7f030021;
		public static final int actionOverflowButtonStyle = 0x7f030022;
		public static final int actionOverflowMenuStyle = 0x7f030023;
		public static final int actionProviderClass = 0x7f030024;
		public static final int actionTextColorAlpha = 0x7f030025;
		public static final int actionViewClass = 0x7f030026;
		public static final int activeIndicatorLabelPadding = 0x7f030027;
		public static final int activityAction = 0x7f030028;
		public static final int activityChooserViewStyle = 0x7f030029;
		public static final int activityName = 0x7f03002a;
		public static final int addElevationShadow = 0x7f03002b;
		public static final int alertDialogButtonGroupStyle = 0x7f03002c;
		public static final int alertDialogCenterButtons = 0x7f03002d;
		public static final int alertDialogStyle = 0x7f03002e;
		public static final int alertDialogTheme = 0x7f03002f;
		public static final int allowStacking = 0x7f030030;
		public static final int alpha = 0x7f030031;
		public static final int alphabeticModifiers = 0x7f030032;
		public static final int altSrc = 0x7f030033;
		public static final int alwaysExpand = 0x7f030034;
		public static final int animateMenuItems = 0x7f030036;
		public static final int animateNavigationIcon = 0x7f030037;
		public static final int animationBackgroundColor = 0x7f030039;
		public static final int animationMode = 0x7f03003a;
		public static final int appBarLayoutStyle = 0x7f03003b;
		public static final int applyMotionScene = 0x7f03003c;
		public static final int arcMode = 0x7f03003d;
		public static final int arrowHeadLength = 0x7f03003f;
		public static final int arrowShaftLength = 0x7f030040;
		public static final int attributeName = 0x7f030041;
		public static final int autoAdjustToWithinGrandparentBounds = 0x7f030042;
		public static final int autoCompleteTextViewStyle = 0x7f030044;
		public static final int autoShowKeyboard = 0x7f030045;
		public static final int autoSizeMaxTextSize = 0x7f030046;
		public static final int autoSizeMinTextSize = 0x7f030047;
		public static final int autoSizePresetSizes = 0x7f030048;
		public static final int autoSizeStepGranularity = 0x7f030049;
		public static final int autoSizeTextType = 0x7f03004a;
		public static final int autoTransition = 0x7f03004b;
		public static final int backHandlingEnabled = 0x7f03004c;
		public static final int background = 0x7f03004d;
		public static final int backgroundColor = 0x7f03004e;
		public static final int backgroundInsetBottom = 0x7f03004f;
		public static final int backgroundInsetEnd = 0x7f030050;
		public static final int backgroundInsetStart = 0x7f030051;
		public static final int backgroundInsetTop = 0x7f030052;
		public static final int backgroundOverlayColorAlpha = 0x7f030053;
		public static final int backgroundSplit = 0x7f030054;
		public static final int backgroundStacked = 0x7f030055;
		public static final int backgroundTint = 0x7f030056;
		public static final int backgroundTintMode = 0x7f030057;
		public static final int badgeGravity = 0x7f030058;
		public static final int badgeHeight = 0x7f030059;
		public static final int badgeRadius = 0x7f03005a;
		public static final int badgeShapeAppearance = 0x7f03005b;
		public static final int badgeShapeAppearanceOverlay = 0x7f03005c;
		public static final int badgeStyle = 0x7f03005d;
		public static final int badgeText = 0x7f03005e;
		public static final int badgeTextAppearance = 0x7f03005f;
		public static final int badgeTextColor = 0x7f030060;
		public static final int badgeVerticalPadding = 0x7f030061;
		public static final int badgeWidePadding = 0x7f030062;
		public static final int badgeWidth = 0x7f030063;
		public static final int badgeWithTextHeight = 0x7f030064;
		public static final int badgeWithTextRadius = 0x7f030065;
		public static final int badgeWithTextShapeAppearance = 0x7f030066;
		public static final int badgeWithTextShapeAppearanceOverlay = 0x7f030067;
		public static final int badgeWithTextWidth = 0x7f030068;
		public static final int barLength = 0x7f030069;
		public static final int barrierAllowsGoneWidgets = 0x7f03006a;
		public static final int barrierDirection = 0x7f03006b;
		public static final int barrierMargin = 0x7f03006c;
		public static final int behavior_autoHide = 0x7f03006d;
		public static final int behavior_autoShrink = 0x7f03006e;
		public static final int behavior_draggable = 0x7f03006f;
		public static final int behavior_expandedOffset = 0x7f030070;
		public static final int behavior_fitToContents = 0x7f030071;
		public static final int behavior_halfExpandedRatio = 0x7f030072;
		public static final int behavior_hideable = 0x7f030073;
		public static final int behavior_overlapTop = 0x7f030074;
		public static final int behavior_peekHeight = 0x7f030075;
		public static final int behavior_saveFlags = 0x7f030076;
		public static final int behavior_significantVelocityThreshold = 0x7f030077;
		public static final int behavior_skipCollapsed = 0x7f030078;
		public static final int borderWidth = 0x7f03007c;
		public static final int borderlessButtonStyle = 0x7f03007d;
		public static final int bottomAppBarStyle = 0x7f03007e;
		public static final int bottomInsetScrimEnabled = 0x7f03007f;
		public static final int bottomNavigationStyle = 0x7f030080;
		public static final int bottomSheetDialogTheme = 0x7f030082;
		public static final int bottomSheetDragHandleStyle = 0x7f030083;
		public static final int bottomSheetStyle = 0x7f030084;
		public static final int boxBackgroundColor = 0x7f030085;
		public static final int boxBackgroundMode = 0x7f030086;
		public static final int boxCollapsedPaddingTop = 0x7f030087;
		public static final int boxCornerRadiusBottomEnd = 0x7f030088;
		public static final int boxCornerRadiusBottomStart = 0x7f030089;
		public static final int boxCornerRadiusTopEnd = 0x7f03008a;
		public static final int boxCornerRadiusTopStart = 0x7f03008b;
		public static final int boxStrokeColor = 0x7f03008c;
		public static final int boxStrokeErrorColor = 0x7f03008d;
		public static final int boxStrokeWidth = 0x7f03008e;
		public static final int boxStrokeWidthFocused = 0x7f03008f;
		public static final int brightness = 0x7f030090;
		public static final int buttonBarButtonStyle = 0x7f030091;
		public static final int buttonBarNegativeButtonStyle = 0x7f030092;
		public static final int buttonBarNeutralButtonStyle = 0x7f030093;
		public static final int buttonBarPositiveButtonStyle = 0x7f030094;
		public static final int buttonBarStyle = 0x7f030095;
		public static final int buttonCompat = 0x7f030096;
		public static final int buttonGravity = 0x7f030097;
		public static final int buttonIcon = 0x7f030098;
		public static final int buttonIconDimen = 0x7f030099;
		public static final int buttonIconTint = 0x7f03009a;
		public static final int buttonIconTintMode = 0x7f03009b;
		public static final int buttonPanelSideLayout = 0x7f03009c;
		public static final int buttonStyle = 0x7f03009d;
		public static final int buttonStyleSmall = 0x7f03009e;
		public static final int buttonTint = 0x7f03009f;
		public static final int buttonTintMode = 0x7f0300a0;
		public static final int cardBackgroundColor = 0x7f0300a1;
		public static final int cardCornerRadius = 0x7f0300a2;
		public static final int cardElevation = 0x7f0300a3;
		public static final int cardForegroundColor = 0x7f0300a4;
		public static final int cardMaxElevation = 0x7f0300a5;
		public static final int cardPreventCornerOverlap = 0x7f0300a6;
		public static final int cardUseCompatPadding = 0x7f0300a7;
		public static final int cardViewStyle = 0x7f0300a8;
		public static final int carousel_alignment = 0x7f0300a9;
		public static final int centerIfNoTextEnabled = 0x7f0300b4;
		public static final int chainUseRtl = 0x7f0300b5;
		public static final int checkMarkCompat = 0x7f0300b6;
		public static final int checkMarkTint = 0x7f0300b7;
		public static final int checkMarkTintMode = 0x7f0300b8;
		public static final int checkboxStyle = 0x7f0300b9;
		public static final int checkedButton = 0x7f0300ba;
		public static final int checkedChip = 0x7f0300bb;
		public static final int checkedIcon = 0x7f0300bc;
		public static final int checkedIconEnabled = 0x7f0300bd;
		public static final int checkedIconGravity = 0x7f0300be;
		public static final int checkedIconMargin = 0x7f0300bf;
		public static final int checkedIconSize = 0x7f0300c0;
		public static final int checkedIconTint = 0x7f0300c1;
		public static final int checkedIconVisible = 0x7f0300c2;
		public static final int checkedState = 0x7f0300c3;
		public static final int checkedTextViewStyle = 0x7f0300c4;
		public static final int chipBackgroundColor = 0x7f0300c5;
		public static final int chipCornerRadius = 0x7f0300c6;
		public static final int chipEndPadding = 0x7f0300c7;
		public static final int chipGroupStyle = 0x7f0300c8;
		public static final int chipIcon = 0x7f0300c9;
		public static final int chipIconEnabled = 0x7f0300ca;
		public static final int chipIconSize = 0x7f0300cb;
		public static final int chipIconTint = 0x7f0300cc;
		public static final int chipIconVisible = 0x7f0300cd;
		public static final int chipMinHeight = 0x7f0300ce;
		public static final int chipMinTouchTargetSize = 0x7f0300cf;
		public static final int chipSpacing = 0x7f0300d0;
		public static final int chipSpacingHorizontal = 0x7f0300d1;
		public static final int chipSpacingVertical = 0x7f0300d2;
		public static final int chipStandaloneStyle = 0x7f0300d3;
		public static final int chipStartPadding = 0x7f0300d4;
		public static final int chipStrokeColor = 0x7f0300d5;
		public static final int chipStrokeWidth = 0x7f0300d6;
		public static final int chipStyle = 0x7f0300d7;
		public static final int chipSurfaceColor = 0x7f0300d8;
		public static final int circleRadius = 0x7f0300d9;
		public static final int circularProgressIndicatorStyle = 0x7f0300da;
		public static final int clearTop = 0x7f0300e0;
		public static final int clickAction = 0x7f0300e2;
		public static final int clockFaceBackgroundColor = 0x7f0300e3;
		public static final int clockHandColor = 0x7f0300e4;
		public static final int clockIcon = 0x7f0300e5;
		public static final int clockNumberTextColor = 0x7f0300e6;
		public static final int closeIcon = 0x7f0300e7;
		public static final int closeIconEnabled = 0x7f0300e8;
		public static final int closeIconEndPadding = 0x7f0300e9;
		public static final int closeIconSize = 0x7f0300ea;
		public static final int closeIconStartPadding = 0x7f0300eb;
		public static final int closeIconTint = 0x7f0300ec;
		public static final int closeIconVisible = 0x7f0300ed;
		public static final int closeItemLayout = 0x7f0300ee;
		public static final int collapseContentDescription = 0x7f0300ef;
		public static final int collapseIcon = 0x7f0300f0;
		public static final int collapsedSize = 0x7f0300f1;
		public static final int collapsedTitleGravity = 0x7f0300f2;
		public static final int collapsedTitleTextAppearance = 0x7f0300f3;
		public static final int collapsedTitleTextColor = 0x7f0300f4;
		public static final int collapsingToolbarLayoutLargeSize = 0x7f0300f5;
		public static final int collapsingToolbarLayoutLargeStyle = 0x7f0300f6;
		public static final int collapsingToolbarLayoutMediumSize = 0x7f0300f7;
		public static final int collapsingToolbarLayoutMediumStyle = 0x7f0300f8;
		public static final int collapsingToolbarLayoutStyle = 0x7f0300f9;
		public static final int color = 0x7f0300fb;
		public static final int colorAccent = 0x7f0300fc;
		public static final int colorBackgroundFloating = 0x7f0300fd;
		public static final int colorButtonNormal = 0x7f0300fe;
		public static final int colorContainer = 0x7f0300ff;
		public static final int colorControlActivated = 0x7f030100;
		public static final int colorControlHighlight = 0x7f030101;
		public static final int colorControlNormal = 0x7f030102;
		public static final int colorError = 0x7f030103;
		public static final int colorErrorContainer = 0x7f030104;
		public static final int colorOnBackground = 0x7f030105;
		public static final int colorOnContainer = 0x7f030106;
		public static final int colorOnContainerUnchecked = 0x7f030107;
		public static final int colorOnError = 0x7f030108;
		public static final int colorOnErrorContainer = 0x7f030109;
		public static final int colorOnPrimary = 0x7f03010a;
		public static final int colorOnPrimaryContainer = 0x7f03010b;
		public static final int colorOnPrimaryFixed = 0x7f03010c;
		public static final int colorOnPrimaryFixedVariant = 0x7f03010d;
		public static final int colorOnPrimarySurface = 0x7f03010e;
		public static final int colorOnSecondary = 0x7f03010f;
		public static final int colorOnSecondaryContainer = 0x7f030110;
		public static final int colorOnSecondaryFixed = 0x7f030111;
		public static final int colorOnSecondaryFixedVariant = 0x7f030112;
		public static final int colorOnSurface = 0x7f030113;
		public static final int colorOnSurfaceInverse = 0x7f030114;
		public static final int colorOnSurfaceVariant = 0x7f030115;
		public static final int colorOnTertiary = 0x7f030116;
		public static final int colorOnTertiaryContainer = 0x7f030117;
		public static final int colorOnTertiaryFixed = 0x7f030118;
		public static final int colorOnTertiaryFixedVariant = 0x7f030119;
		public static final int colorOutline = 0x7f03011a;
		public static final int colorOutlineVariant = 0x7f03011b;
		public static final int colorPrimary = 0x7f03011c;
		public static final int colorPrimaryContainer = 0x7f03011d;
		public static final int colorPrimaryDark = 0x7f03011e;
		public static final int colorPrimaryFixed = 0x7f03011f;
		public static final int colorPrimaryFixedDim = 0x7f030120;
		public static final int colorPrimaryInverse = 0x7f030121;
		public static final int colorPrimarySurface = 0x7f030122;
		public static final int colorPrimaryVariant = 0x7f030123;
		public static final int colorSecondary = 0x7f030124;
		public static final int colorSecondaryContainer = 0x7f030125;
		public static final int colorSecondaryFixed = 0x7f030126;
		public static final int colorSecondaryFixedDim = 0x7f030127;
		public static final int colorSecondaryVariant = 0x7f030128;
		public static final int colorSurface = 0x7f030129;
		public static final int colorSurfaceBright = 0x7f03012a;
		public static final int colorSurfaceContainer = 0x7f03012b;
		public static final int colorSurfaceContainerHigh = 0x7f03012c;
		public static final int colorSurfaceContainerHighest = 0x7f03012d;
		public static final int colorSurfaceContainerLow = 0x7f03012e;
		public static final int colorSurfaceContainerLowest = 0x7f03012f;
		public static final int colorSurfaceDim = 0x7f030130;
		public static final int colorSurfaceInverse = 0x7f030131;
		public static final int colorSurfaceVariant = 0x7f030132;
		public static final int colorSwitchThumbNormal = 0x7f030133;
		public static final int colorTertiary = 0x7f030134;
		public static final int colorTertiaryContainer = 0x7f030135;
		public static final int colorTertiaryFixed = 0x7f030136;
		public static final int colorTertiaryFixedDim = 0x7f030137;
		public static final int commitIcon = 0x7f030138;
		public static final int compatShadowEnabled = 0x7f030139;
		public static final int constraintSet = 0x7f03013b;
		public static final int constraintSetEnd = 0x7f03013c;
		public static final int constraintSetStart = 0x7f03013d;
		public static final int constraint_referenced_ids = 0x7f03013e;
		public static final int constraints = 0x7f030140;
		public static final int content = 0x7f030141;
		public static final int contentDescription = 0x7f030142;
		public static final int contentInsetEnd = 0x7f030143;
		public static final int contentInsetEndWithActions = 0x7f030144;
		public static final int contentInsetLeft = 0x7f030145;
		public static final int contentInsetRight = 0x7f030146;
		public static final int contentInsetStart = 0x7f030147;
		public static final int contentInsetStartWithNavigation = 0x7f030148;
		public static final int contentPadding = 0x7f030149;
		public static final int contentPaddingBottom = 0x7f03014a;
		public static final int contentPaddingEnd = 0x7f03014b;
		public static final int contentPaddingLeft = 0x7f03014c;
		public static final int contentPaddingRight = 0x7f03014d;
		public static final int contentPaddingStart = 0x7f03014e;
		public static final int contentPaddingTop = 0x7f03014f;
		public static final int contentScrim = 0x7f030150;
		public static final int contrast = 0x7f030151;
		public static final int controlBackground = 0x7f030152;
		public static final int coordinatorLayoutStyle = 0x7f030153;
		public static final int coplanarSiblingViewId = 0x7f030154;
		public static final int cornerFamily = 0x7f030155;
		public static final int cornerFamilyBottomLeft = 0x7f030156;
		public static final int cornerFamilyBottomRight = 0x7f030157;
		public static final int cornerFamilyTopLeft = 0x7f030158;
		public static final int cornerFamilyTopRight = 0x7f030159;
		public static final int cornerRadius = 0x7f03015a;
		public static final int cornerSize = 0x7f03015b;
		public static final int cornerSizeBottomLeft = 0x7f03015c;
		public static final int cornerSizeBottomRight = 0x7f03015d;
		public static final int cornerSizeTopLeft = 0x7f03015e;
		public static final int cornerSizeTopRight = 0x7f03015f;
		public static final int counterEnabled = 0x7f030160;
		public static final int counterMaxLength = 0x7f030161;
		public static final int counterOverflowTextAppearance = 0x7f030162;
		public static final int counterOverflowTextColor = 0x7f030163;
		public static final int counterTextAppearance = 0x7f030164;
		public static final int counterTextColor = 0x7f030165;
		public static final int crossfade = 0x7f030166;
		public static final int currentState = 0x7f030167;
		public static final int cursorColor = 0x7f030168;
		public static final int cursorErrorColor = 0x7f030169;
		public static final int curveFit = 0x7f03016a;
		public static final int customBoolean = 0x7f03016b;
		public static final int customColorDrawableValue = 0x7f03016c;
		public static final int customColorValue = 0x7f03016d;
		public static final int customDimension = 0x7f03016e;
		public static final int customFloatValue = 0x7f03016f;
		public static final int customIntegerValue = 0x7f030170;
		public static final int customNavigationLayout = 0x7f030171;
		public static final int customPixelDimension = 0x7f030172;
		public static final int customStringValue = 0x7f030174;
		public static final int dayInvalidStyle = 0x7f030177;
		public static final int daySelectedStyle = 0x7f030178;
		public static final int dayStyle = 0x7f030179;
		public static final int dayTodayStyle = 0x7f03017a;
		public static final int defaultDuration = 0x7f03017b;
		public static final int defaultMarginsEnabled = 0x7f03017c;
		public static final int defaultQueryHint = 0x7f03017e;
		public static final int defaultScrollFlagsEnabled = 0x7f03017f;
		public static final int defaultState = 0x7f030180;
		public static final int deltaPolarAngle = 0x7f030181;
		public static final int deltaPolarRadius = 0x7f030182;
		public static final int deriveConstraintsFrom = 0x7f030183;
		public static final int dialogCornerRadius = 0x7f030185;
		public static final int dialogPreferredPadding = 0x7f030186;
		public static final int dialogTheme = 0x7f030187;
		public static final int displayOptions = 0x7f030188;
		public static final int divider = 0x7f030189;
		public static final int dividerColor = 0x7f03018a;
		public static final int dividerHorizontal = 0x7f03018b;
		public static final int dividerInsetEnd = 0x7f03018c;
		public static final int dividerInsetStart = 0x7f03018d;
		public static final int dividerPadding = 0x7f03018e;
		public static final int dividerThickness = 0x7f03018f;
		public static final int dividerVertical = 0x7f030190;
		public static final int dragDirection = 0x7f030191;
		public static final int dragScale = 0x7f030192;
		public static final int dragThreshold = 0x7f030193;
		public static final int drawPath = 0x7f030194;
		public static final int drawableBottomCompat = 0x7f030195;
		public static final int drawableEndCompat = 0x7f030196;
		public static final int drawableLeftCompat = 0x7f030197;
		public static final int drawableRightCompat = 0x7f030198;
		public static final int drawableSize = 0x7f030199;
		public static final int drawableStartCompat = 0x7f03019a;
		public static final int drawableTint = 0x7f03019b;
		public static final int drawableTintMode = 0x7f03019c;
		public static final int drawableTopCompat = 0x7f03019d;
		public static final int drawerArrowStyle = 0x7f03019e;
		public static final int drawerLayoutCornerSize = 0x7f03019f;
		public static final int drawerLayoutStyle = 0x7f0301a0;
		public static final int dropDownBackgroundTint = 0x7f0301a1;
		public static final int dropDownListViewStyle = 0x7f0301a2;
		public static final int dropdownListPreferredItemHeight = 0x7f0301a3;
		public static final int duration = 0x7f0301a4;
		public static final int dynamicColorThemeOverlay = 0x7f0301a5;
		public static final int editTextBackground = 0x7f0301a6;
		public static final int editTextColor = 0x7f0301a7;
		public static final int editTextStyle = 0x7f0301a8;
		public static final int elevation = 0x7f0301a9;
		public static final int elevationOverlayAccentColor = 0x7f0301aa;
		public static final int elevationOverlayColor = 0x7f0301ab;
		public static final int elevationOverlayEnabled = 0x7f0301ac;
		public static final int emojiCompatEnabled = 0x7f0301ad;
		public static final int enableEdgeToEdge = 0x7f0301ae;
		public static final int endIconCheckable = 0x7f0301af;
		public static final int endIconContentDescription = 0x7f0301b0;
		public static final int endIconDrawable = 0x7f0301b1;
		public static final int endIconMinSize = 0x7f0301b2;
		public static final int endIconMode = 0x7f0301b3;
		public static final int endIconScaleType = 0x7f0301b4;
		public static final int endIconTint = 0x7f0301b5;
		public static final int endIconTintMode = 0x7f0301b6;
		public static final int enforceMaterialTheme = 0x7f0301b7;
		public static final int enforceTextAppearance = 0x7f0301b8;
		public static final int ensureMinTouchTargetSize = 0x7f0301b9;
		public static final int errorAccessibilityLabel = 0x7f0301bb;
		public static final int errorAccessibilityLiveRegion = 0x7f0301bc;
		public static final int errorContentDescription = 0x7f0301bd;
		public static final int errorEnabled = 0x7f0301be;
		public static final int errorIconDrawable = 0x7f0301bf;
		public static final int errorIconTint = 0x7f0301c0;
		public static final int errorIconTintMode = 0x7f0301c1;
		public static final int errorShown = 0x7f0301c2;
		public static final int errorTextAppearance = 0x7f0301c3;
		public static final int errorTextColor = 0x7f0301c4;
		public static final int expandActivityOverflowButtonDrawable = 0x7f0301c6;
		public static final int expanded = 0x7f0301c7;
		public static final int expandedHintEnabled = 0x7f0301c8;
		public static final int expandedTitleGravity = 0x7f0301c9;
		public static final int expandedTitleMargin = 0x7f0301ca;
		public static final int expandedTitleMarginBottom = 0x7f0301cb;
		public static final int expandedTitleMarginEnd = 0x7f0301cc;
		public static final int expandedTitleMarginStart = 0x7f0301cd;
		public static final int expandedTitleMarginTop = 0x7f0301ce;
		public static final int expandedTitleTextAppearance = 0x7f0301cf;
		public static final int expandedTitleTextColor = 0x7f0301d0;
		public static final int extendMotionSpec = 0x7f0301d1;
		public static final int extendStrategy = 0x7f0301d2;
		public static final int extendedFloatingActionButtonPrimaryStyle = 0x7f0301d3;
		public static final int extendedFloatingActionButtonSecondaryStyle = 0x7f0301d4;
		public static final int extendedFloatingActionButtonStyle = 0x7f0301d5;
		public static final int extendedFloatingActionButtonSurfaceStyle = 0x7f0301d6;
		public static final int extendedFloatingActionButtonTertiaryStyle = 0x7f0301d7;
		public static final int extraMultilineHeightEnabled = 0x7f0301d8;
		public static final int fabAlignmentMode = 0x7f0301d9;
		public static final int fabAlignmentModeEndMargin = 0x7f0301da;
		public static final int fabAnchorMode = 0x7f0301db;
		public static final int fabAnimationMode = 0x7f0301dc;
		public static final int fabCradleMargin = 0x7f0301dd;
		public static final int fabCradleRoundedCornerRadius = 0x7f0301de;
		public static final int fabCradleVerticalOffset = 0x7f0301df;
		public static final int fabCustomSize = 0x7f0301e0;
		public static final int fabSize = 0x7f0301e1;
		public static final int fastScrollEnabled = 0x7f0301e2;
		public static final int fastScrollHorizontalThumbDrawable = 0x7f0301e3;
		public static final int fastScrollHorizontalTrackDrawable = 0x7f0301e4;
		public static final int fastScrollVerticalThumbDrawable = 0x7f0301e5;
		public static final int fastScrollVerticalTrackDrawable = 0x7f0301e6;
		public static final int finishPrimaryWithPlaceholder = 0x7f0301e7;
		public static final int finishPrimaryWithSecondary = 0x7f0301e8;
		public static final int finishSecondaryWithPrimary = 0x7f0301e9;
		public static final int firstBaselineToTopHeight = 0x7f0301ea;
		public static final int floatingActionButtonLargePrimaryStyle = 0x7f0301eb;
		public static final int floatingActionButtonLargeSecondaryStyle = 0x7f0301ec;
		public static final int floatingActionButtonLargeStyle = 0x7f0301ed;
		public static final int floatingActionButtonLargeSurfaceStyle = 0x7f0301ee;
		public static final int floatingActionButtonLargeTertiaryStyle = 0x7f0301ef;
		public static final int floatingActionButtonPrimaryStyle = 0x7f0301f0;
		public static final int floatingActionButtonSecondaryStyle = 0x7f0301f1;
		public static final int floatingActionButtonSmallPrimaryStyle = 0x7f0301f2;
		public static final int floatingActionButtonSmallSecondaryStyle = 0x7f0301f3;
		public static final int floatingActionButtonSmallStyle = 0x7f0301f4;
		public static final int floatingActionButtonSmallSurfaceStyle = 0x7f0301f5;
		public static final int floatingActionButtonSmallTertiaryStyle = 0x7f0301f6;
		public static final int floatingActionButtonStyle = 0x7f0301f7;
		public static final int floatingActionButtonSurfaceStyle = 0x7f0301f8;
		public static final int floatingActionButtonTertiaryStyle = 0x7f0301f9;
		public static final int flow_firstHorizontalBias = 0x7f0301fa;
		public static final int flow_firstHorizontalStyle = 0x7f0301fb;
		public static final int flow_firstVerticalBias = 0x7f0301fc;
		public static final int flow_firstVerticalStyle = 0x7f0301fd;
		public static final int flow_horizontalAlign = 0x7f0301fe;
		public static final int flow_horizontalBias = 0x7f0301ff;
		public static final int flow_horizontalGap = 0x7f030200;
		public static final int flow_horizontalStyle = 0x7f030201;
		public static final int flow_lastHorizontalBias = 0x7f030202;
		public static final int flow_lastHorizontalStyle = 0x7f030203;
		public static final int flow_lastVerticalBias = 0x7f030204;
		public static final int flow_lastVerticalStyle = 0x7f030205;
		public static final int flow_maxElementsWrap = 0x7f030206;
		public static final int flow_padding = 0x7f030207;
		public static final int flow_verticalAlign = 0x7f030208;
		public static final int flow_verticalBias = 0x7f030209;
		public static final int flow_verticalGap = 0x7f03020a;
		public static final int flow_verticalStyle = 0x7f03020b;
		public static final int flow_wrapMode = 0x7f03020c;
		public static final int font = 0x7f03020d;
		public static final int fontFamily = 0x7f03020e;
		public static final int fontProviderAuthority = 0x7f03020f;
		public static final int fontProviderCerts = 0x7f030210;
		public static final int fontProviderFetchStrategy = 0x7f030212;
		public static final int fontProviderFetchTimeout = 0x7f030213;
		public static final int fontProviderPackage = 0x7f030214;
		public static final int fontProviderQuery = 0x7f030215;
		public static final int fontProviderSystemFontFamily = 0x7f030216;
		public static final int fontStyle = 0x7f030217;
		public static final int fontVariationSettings = 0x7f030218;
		public static final int fontWeight = 0x7f030219;
		public static final int forceApplySystemWindowInsetTop = 0x7f03021a;
		public static final int forceDefaultNavigationOnClickListener = 0x7f03021b;
		public static final int foregroundInsidePadding = 0x7f03021c;
		public static final int framePosition = 0x7f03021d;
		public static final int gapBetweenBars = 0x7f03021e;
		public static final int gestureInsetBottomIgnored = 0x7f03021f;
		public static final int goIcon = 0x7f030220;
		public static final int haloColor = 0x7f03022e;
		public static final int haloRadius = 0x7f03022f;
		public static final int headerLayout = 0x7f030230;
		public static final int height = 0x7f030231;
		public static final int helperText = 0x7f030232;
		public static final int helperTextEnabled = 0x7f030233;
		public static final int helperTextTextAppearance = 0x7f030234;
		public static final int helperTextTextColor = 0x7f030235;
		public static final int hideAnimationBehavior = 0x7f030236;
		public static final int hideMotionSpec = 0x7f030237;
		public static final int hideNavigationIcon = 0x7f030238;
		public static final int hideOnContentScroll = 0x7f030239;
		public static final int hideOnScroll = 0x7f03023a;
		public static final int hintAnimationEnabled = 0x7f03023b;
		public static final int hintEnabled = 0x7f03023c;
		public static final int hintTextAppearance = 0x7f03023d;
		public static final int hintTextColor = 0x7f03023e;
		public static final int homeAsUpIndicator = 0x7f03023f;
		public static final int homeLayout = 0x7f030240;
		public static final int horizontalOffset = 0x7f030241;
		public static final int horizontalOffsetWithText = 0x7f030242;
		public static final int hoveredFocusedTranslationZ = 0x7f030243;
		public static final int icon = 0x7f030244;
		public static final int iconEndPadding = 0x7f030245;
		public static final int iconGravity = 0x7f030246;
		public static final int iconPadding = 0x7f030247;
		public static final int iconSize = 0x7f030248;
		public static final int iconStartPadding = 0x7f030249;
		public static final int iconTint = 0x7f03024a;
		public static final int iconTintMode = 0x7f03024b;
		public static final int iconifiedByDefault = 0x7f03024c;
		public static final int imageButtonStyle = 0x7f03024f;
		public static final int indeterminateAnimationType = 0x7f030254;
		public static final int indeterminateProgressStyle = 0x7f030255;
		public static final int indicatorColor = 0x7f030256;
		public static final int indicatorDirectionCircular = 0x7f030257;
		public static final int indicatorDirectionLinear = 0x7f030258;
		public static final int indicatorInset = 0x7f030259;
		public static final int indicatorSize = 0x7f03025a;
		public static final int initialActivityCount = 0x7f03025c;
		public static final int insetForeground = 0x7f03025d;
		public static final int isLightTheme = 0x7f03025e;
		public static final int isMaterial3DynamicColorApplied = 0x7f03025f;
		public static final int isMaterial3Theme = 0x7f030260;
		public static final int isMaterialTheme = 0x7f030261;
		public static final int itemActiveIndicatorStyle = 0x7f030262;
		public static final int itemBackground = 0x7f030263;
		public static final int itemFillColor = 0x7f030264;
		public static final int itemHorizontalPadding = 0x7f030265;
		public static final int itemHorizontalTranslationEnabled = 0x7f030266;
		public static final int itemIconPadding = 0x7f030267;
		public static final int itemIconSize = 0x7f030268;
		public static final int itemIconTint = 0x7f030269;
		public static final int itemMaxLines = 0x7f03026a;
		public static final int itemMinHeight = 0x7f03026b;
		public static final int itemPadding = 0x7f03026c;
		public static final int itemPaddingBottom = 0x7f03026d;
		public static final int itemPaddingTop = 0x7f03026e;
		public static final int itemRippleColor = 0x7f03026f;
		public static final int itemShapeAppearance = 0x7f030270;
		public static final int itemShapeAppearanceOverlay = 0x7f030271;
		public static final int itemShapeFillColor = 0x7f030272;
		public static final int itemShapeInsetBottom = 0x7f030273;
		public static final int itemShapeInsetEnd = 0x7f030274;
		public static final int itemShapeInsetStart = 0x7f030275;
		public static final int itemShapeInsetTop = 0x7f030276;
		public static final int itemSpacing = 0x7f030277;
		public static final int itemStrokeColor = 0x7f030278;
		public static final int itemStrokeWidth = 0x7f030279;
		public static final int itemTextAppearance = 0x7f03027a;
		public static final int itemTextAppearanceActive = 0x7f03027b;
		public static final int itemTextAppearanceActiveBoldEnabled = 0x7f03027c;
		public static final int itemTextAppearanceInactive = 0x7f03027d;
		public static final int itemTextColor = 0x7f03027e;
		public static final int itemVerticalPadding = 0x7f03027f;
		public static final int keyPositionType = 0x7f030280;
		public static final int keyboardIcon = 0x7f030281;
		public static final int keylines = 0x7f030282;
		public static final int lStar = 0x7f030283;
		public static final int labelBehavior = 0x7f030284;
		public static final int labelStyle = 0x7f030285;
		public static final int labelVisibilityMode = 0x7f030286;
		public static final int largeFontVerticalOffsetAdjustment = 0x7f030287;
		public static final int lastBaselineToBottomHeight = 0x7f030288;
		public static final int lastItemDecorated = 0x7f030289;
		public static final int layout = 0x7f03028b;
		public static final int layoutDescription = 0x7f03028c;
		public static final int layoutDuringTransition = 0x7f03028d;
		public static final int layoutManager = 0x7f03028e;
		public static final int layout_anchor = 0x7f03028f;
		public static final int layout_anchorGravity = 0x7f030290;
		public static final int layout_behavior = 0x7f030291;
		public static final int layout_collapseMode = 0x7f030292;
		public static final int layout_collapseParallaxMultiplier = 0x7f030293;
		public static final int layout_constrainedHeight = 0x7f030294;
		public static final int layout_constrainedWidth = 0x7f030295;
		public static final int layout_constraintBaseline_creator = 0x7f030296;
		public static final int layout_constraintBaseline_toBaselineOf = 0x7f030297;
		public static final int layout_constraintBottom_creator = 0x7f03029a;
		public static final int layout_constraintBottom_toBottomOf = 0x7f03029b;
		public static final int layout_constraintBottom_toTopOf = 0x7f03029c;
		public static final int layout_constraintCircle = 0x7f03029d;
		public static final int layout_constraintCircleAngle = 0x7f03029e;
		public static final int layout_constraintCircleRadius = 0x7f03029f;
		public static final int layout_constraintDimensionRatio = 0x7f0302a0;
		public static final int layout_constraintEnd_toEndOf = 0x7f0302a1;
		public static final int layout_constraintEnd_toStartOf = 0x7f0302a2;
		public static final int layout_constraintGuide_begin = 0x7f0302a3;
		public static final int layout_constraintGuide_end = 0x7f0302a4;
		public static final int layout_constraintGuide_percent = 0x7f0302a5;
		public static final int layout_constraintHeight_default = 0x7f0302a7;
		public static final int layout_constraintHeight_max = 0x7f0302a8;
		public static final int layout_constraintHeight_min = 0x7f0302a9;
		public static final int layout_constraintHeight_percent = 0x7f0302aa;
		public static final int layout_constraintHorizontal_bias = 0x7f0302ab;
		public static final int layout_constraintHorizontal_chainStyle = 0x7f0302ac;
		public static final int layout_constraintHorizontal_weight = 0x7f0302ad;
		public static final int layout_constraintLeft_creator = 0x7f0302ae;
		public static final int layout_constraintLeft_toLeftOf = 0x7f0302af;
		public static final int layout_constraintLeft_toRightOf = 0x7f0302b0;
		public static final int layout_constraintRight_creator = 0x7f0302b1;
		public static final int layout_constraintRight_toLeftOf = 0x7f0302b2;
		public static final int layout_constraintRight_toRightOf = 0x7f0302b3;
		public static final int layout_constraintStart_toEndOf = 0x7f0302b4;
		public static final int layout_constraintStart_toStartOf = 0x7f0302b5;
		public static final int layout_constraintTag = 0x7f0302b6;
		public static final int layout_constraintTop_creator = 0x7f0302b7;
		public static final int layout_constraintTop_toBottomOf = 0x7f0302b8;
		public static final int layout_constraintTop_toTopOf = 0x7f0302b9;
		public static final int layout_constraintVertical_bias = 0x7f0302ba;
		public static final int layout_constraintVertical_chainStyle = 0x7f0302bb;
		public static final int layout_constraintVertical_weight = 0x7f0302bc;
		public static final int layout_constraintWidth_default = 0x7f0302be;
		public static final int layout_constraintWidth_max = 0x7f0302bf;
		public static final int layout_constraintWidth_min = 0x7f0302c0;
		public static final int layout_constraintWidth_percent = 0x7f0302c1;
		public static final int layout_dodgeInsetEdges = 0x7f0302c2;
		public static final int layout_editor_absoluteX = 0x7f0302c3;
		public static final int layout_editor_absoluteY = 0x7f0302c4;
		public static final int layout_goneMarginBottom = 0x7f0302c6;
		public static final int layout_goneMarginEnd = 0x7f0302c7;
		public static final int layout_goneMarginLeft = 0x7f0302c8;
		public static final int layout_goneMarginRight = 0x7f0302c9;
		public static final int layout_goneMarginStart = 0x7f0302ca;
		public static final int layout_goneMarginTop = 0x7f0302cb;
		public static final int layout_insetEdge = 0x7f0302cc;
		public static final int layout_keyline = 0x7f0302cd;
		public static final int layout_optimizationLevel = 0x7f0302cf;
		public static final int layout_scrollEffect = 0x7f0302d0;
		public static final int layout_scrollFlags = 0x7f0302d1;
		public static final int layout_scrollInterpolator = 0x7f0302d2;
		public static final int liftOnScroll = 0x7f0302d4;
		public static final int liftOnScrollColor = 0x7f0302d5;
		public static final int liftOnScrollTargetViewId = 0x7f0302d6;
		public static final int limitBoundsTo = 0x7f0302d7;
		public static final int lineHeight = 0x7f0302d8;
		public static final int lineSpacing = 0x7f0302d9;
		public static final int linearProgressIndicatorStyle = 0x7f0302da;
		public static final int listChoiceBackgroundIndicator = 0x7f0302db;
		public static final int listChoiceIndicatorMultipleAnimated = 0x7f0302dc;
		public static final int listChoiceIndicatorSingleAnimated = 0x7f0302dd;
		public static final int listDividerAlertDialog = 0x7f0302de;
		public static final int listItemLayout = 0x7f0302df;
		public static final int listLayout = 0x7f0302e0;
		public static final int listMenuViewStyle = 0x7f0302e1;
		public static final int listPopupWindowStyle = 0x7f0302e2;
		public static final int listPreferredItemHeight = 0x7f0302e3;
		public static final int listPreferredItemHeightLarge = 0x7f0302e4;
		public static final int listPreferredItemHeightSmall = 0x7f0302e5;
		public static final int listPreferredItemPaddingEnd = 0x7f0302e6;
		public static final int listPreferredItemPaddingLeft = 0x7f0302e7;
		public static final int listPreferredItemPaddingRight = 0x7f0302e8;
		public static final int listPreferredItemPaddingStart = 0x7f0302e9;
		public static final int logo = 0x7f0302ea;
		public static final int logoAdjustViewBounds = 0x7f0302eb;
		public static final int logoDescription = 0x7f0302ec;
		public static final int logoScaleType = 0x7f0302ed;
		public static final int marginHorizontal = 0x7f0302ee;
		public static final int marginLeftSystemWindowInsets = 0x7f0302ef;
		public static final int marginRightSystemWindowInsets = 0x7f0302f0;
		public static final int marginTopSystemWindowInsets = 0x7f0302f1;
		public static final int materialAlertDialogBodyTextStyle = 0x7f0302f2;
		public static final int materialAlertDialogButtonSpacerVisibility = 0x7f0302f3;
		public static final int materialAlertDialogTheme = 0x7f0302f4;
		public static final int materialAlertDialogTitleIconStyle = 0x7f0302f5;
		public static final int materialAlertDialogTitlePanelStyle = 0x7f0302f6;
		public static final int materialAlertDialogTitleTextStyle = 0x7f0302f7;
		public static final int materialButtonOutlinedStyle = 0x7f0302f8;
		public static final int materialButtonStyle = 0x7f0302f9;
		public static final int materialButtonToggleGroupStyle = 0x7f0302fa;
		public static final int materialCalendarDay = 0x7f0302fb;
		public static final int materialCalendarDayOfWeekLabel = 0x7f0302fc;
		public static final int materialCalendarFullscreenTheme = 0x7f0302fd;
		public static final int materialCalendarHeaderCancelButton = 0x7f0302fe;
		public static final int materialCalendarHeaderConfirmButton = 0x7f0302ff;
		public static final int materialCalendarHeaderDivider = 0x7f030300;
		public static final int materialCalendarHeaderLayout = 0x7f030301;
		public static final int materialCalendarHeaderSelection = 0x7f030302;
		public static final int materialCalendarHeaderTitle = 0x7f030303;
		public static final int materialCalendarHeaderToggleButton = 0x7f030304;
		public static final int materialCalendarMonth = 0x7f030305;
		public static final int materialCalendarMonthNavigationButton = 0x7f030306;
		public static final int materialCalendarStyle = 0x7f030307;
		public static final int materialCalendarTheme = 0x7f030308;
		public static final int materialCalendarYearNavigationButton = 0x7f030309;
		public static final int materialCardViewElevatedStyle = 0x7f03030a;
		public static final int materialCardViewFilledStyle = 0x7f03030b;
		public static final int materialCardViewOutlinedStyle = 0x7f03030c;
		public static final int materialCardViewStyle = 0x7f03030d;
		public static final int materialCircleRadius = 0x7f03030e;
		public static final int materialClockStyle = 0x7f03030f;
		public static final int materialDisplayDividerStyle = 0x7f030310;
		public static final int materialDividerHeavyStyle = 0x7f030311;
		public static final int materialDividerStyle = 0x7f030312;
		public static final int materialIconButtonFilledStyle = 0x7f030313;
		public static final int materialIconButtonFilledTonalStyle = 0x7f030314;
		public static final int materialIconButtonOutlinedStyle = 0x7f030315;
		public static final int materialIconButtonStyle = 0x7f030316;
		public static final int materialSearchBarStyle = 0x7f030317;
		public static final int materialSearchViewPrefixStyle = 0x7f030318;
		public static final int materialSearchViewStyle = 0x7f030319;
		public static final int materialSearchViewToolbarHeight = 0x7f03031a;
		public static final int materialSearchViewToolbarStyle = 0x7f03031b;
		public static final int materialSwitchStyle = 0x7f03031c;
		public static final int materialThemeOverlay = 0x7f03031d;
		public static final int materialTimePickerStyle = 0x7f03031e;
		public static final int materialTimePickerTheme = 0x7f03031f;
		public static final int materialTimePickerTitleStyle = 0x7f030320;
		public static final int maxAcceleration = 0x7f030323;
		public static final int maxActionInlineWidth = 0x7f030324;
		public static final int maxButtonHeight = 0x7f030325;
		public static final int maxCharacterCount = 0x7f030326;
		public static final int maxHeight = 0x7f030327;
		public static final int maxImageSize = 0x7f030328;
		public static final int maxLines = 0x7f030329;
		public static final int maxNumber = 0x7f03032a;
		public static final int maxVelocity = 0x7f03032b;
		public static final int maxWidth = 0x7f03032c;
		public static final int measureWithLargestChild = 0x7f03032d;
		public static final int menu = 0x7f03032e;
		public static final int menuAlignmentMode = 0x7f03032f;
		public static final int menuGravity = 0x7f030330;
		public static final int minHeight = 0x7f030333;
		public static final int minHideDelay = 0x7f030334;
		public static final int minSeparation = 0x7f030335;
		public static final int minTouchTargetSize = 0x7f030336;
		public static final int minWidth = 0x7f030337;
		public static final int mock_diagonalsColor = 0x7f030338;
		public static final int mock_label = 0x7f030339;
		public static final int mock_labelBackgroundColor = 0x7f03033a;
		public static final int mock_labelColor = 0x7f03033b;
		public static final int mock_showDiagonals = 0x7f03033c;
		public static final int mock_showLabel = 0x7f03033d;
		public static final int motionDebug = 0x7f03033e;
		public static final int motionDurationExtraLong1 = 0x7f03033f;
		public static final int motionDurationExtraLong2 = 0x7f030340;
		public static final int motionDurationExtraLong3 = 0x7f030341;
		public static final int motionDurationExtraLong4 = 0x7f030342;
		public static final int motionDurationLong1 = 0x7f030343;
		public static final int motionDurationLong2 = 0x7f030344;
		public static final int motionDurationLong3 = 0x7f030345;
		public static final int motionDurationLong4 = 0x7f030346;
		public static final int motionDurationMedium1 = 0x7f030347;
		public static final int motionDurationMedium2 = 0x7f030348;
		public static final int motionDurationMedium3 = 0x7f030349;
		public static final int motionDurationMedium4 = 0x7f03034a;
		public static final int motionDurationShort1 = 0x7f03034b;
		public static final int motionDurationShort2 = 0x7f03034c;
		public static final int motionDurationShort3 = 0x7f03034d;
		public static final int motionDurationShort4 = 0x7f03034e;
		public static final int motionEasingAccelerated = 0x7f03034f;
		public static final int motionEasingDecelerated = 0x7f030350;
		public static final int motionEasingEmphasized = 0x7f030351;
		public static final int motionEasingEmphasizedAccelerateInterpolator = 0x7f030352;
		public static final int motionEasingEmphasizedDecelerateInterpolator = 0x7f030353;
		public static final int motionEasingEmphasizedInterpolator = 0x7f030354;
		public static final int motionEasingLinear = 0x7f030355;
		public static final int motionEasingLinearInterpolator = 0x7f030356;
		public static final int motionEasingStandard = 0x7f030357;
		public static final int motionEasingStandardAccelerateInterpolator = 0x7f030358;
		public static final int motionEasingStandardDecelerateInterpolator = 0x7f030359;
		public static final int motionEasingStandardInterpolator = 0x7f03035a;
		public static final int motionInterpolator = 0x7f030363;
		public static final int motionPath = 0x7f030364;
		public static final int motionPathRotate = 0x7f030365;
		public static final int motionProgress = 0x7f030366;
		public static final int motionStagger = 0x7f030367;
		public static final int motionTarget = 0x7f030368;
		public static final int motion_postLayoutCollision = 0x7f030369;
		public static final int motion_triggerOnCollision = 0x7f03036a;
		public static final int moveWhenScrollAtTop = 0x7f03036b;
		public static final int multiChoiceItemLayout = 0x7f03036c;
		public static final int navigationContentDescription = 0x7f03036e;
		public static final int navigationIcon = 0x7f03036f;
		public static final int navigationIconTint = 0x7f030370;
		public static final int navigationMode = 0x7f030371;
		public static final int navigationRailStyle = 0x7f030372;
		public static final int navigationViewStyle = 0x7f030373;
		public static final int nestedScrollFlags = 0x7f030374;
		public static final int nestedScrollViewStyle = 0x7f030375;
		public static final int nestedScrollable = 0x7f030376;
		public static final int number = 0x7f030378;
		public static final int numericModifiers = 0x7f030379;
		public static final int offsetAlignmentMode = 0x7f03037a;
		public static final int onCross = 0x7f03037b;
		public static final int onHide = 0x7f03037c;
		public static final int onNegativeCross = 0x7f03037d;
		public static final int onPositiveCross = 0x7f03037e;
		public static final int onShow = 0x7f03037f;
		public static final int onTouchUp = 0x7f030381;
		public static final int overlapAnchor = 0x7f030382;
		public static final int overlay = 0x7f030383;
		public static final int paddingBottomNoButtons = 0x7f030384;
		public static final int paddingBottomSystemWindowInsets = 0x7f030385;
		public static final int paddingEnd = 0x7f030386;
		public static final int paddingLeftSystemWindowInsets = 0x7f030387;
		public static final int paddingRightSystemWindowInsets = 0x7f030388;
		public static final int paddingStart = 0x7f030389;
		public static final int paddingStartSystemWindowInsets = 0x7f03038a;
		public static final int paddingTopNoTitle = 0x7f03038b;
		public static final int paddingTopSystemWindowInsets = 0x7f03038c;
		public static final int panelBackground = 0x7f03038d;
		public static final int panelMenuListTheme = 0x7f03038e;
		public static final int panelMenuListWidth = 0x7f03038f;
		public static final int passwordToggleContentDescription = 0x7f030390;
		public static final int passwordToggleDrawable = 0x7f030391;
		public static final int passwordToggleEnabled = 0x7f030392;
		public static final int passwordToggleTint = 0x7f030393;
		public static final int passwordToggleTintMode = 0x7f030394;
		public static final int pathMotionArc = 0x7f030395;
		public static final int path_percent = 0x7f030396;
		public static final int percentHeight = 0x7f030397;
		public static final int percentWidth = 0x7f030398;
		public static final int percentX = 0x7f030399;
		public static final int percentY = 0x7f03039a;
		public static final int perpendicularPath_percent = 0x7f03039b;
		public static final int pivotAnchor = 0x7f03039c;
		public static final int placeholderActivityName = 0x7f03039d;
		public static final int placeholderText = 0x7f03039e;
		public static final int placeholderTextAppearance = 0x7f03039f;
		public static final int placeholderTextColor = 0x7f0303a0;
		public static final int placeholder_emptyVisibility = 0x7f0303a1;
		public static final int popupMenuBackground = 0x7f0303a8;
		public static final int popupMenuStyle = 0x7f0303a9;
		public static final int popupTheme = 0x7f0303aa;
		public static final int popupWindowStyle = 0x7f0303ab;
		public static final int prefixText = 0x7f0303ac;
		public static final int prefixTextAppearance = 0x7f0303ad;
		public static final int prefixTextColor = 0x7f0303ae;
		public static final int preserveIconSpacing = 0x7f0303af;
		public static final int pressedTranslationZ = 0x7f0303b0;
		public static final int primaryActivityName = 0x7f0303b1;
		public static final int progressBarPadding = 0x7f0303b2;
		public static final int progressBarStyle = 0x7f0303b3;
		public static final int queryBackground = 0x7f0303b7;
		public static final int queryHint = 0x7f0303b8;
		public static final int queryPatterns = 0x7f0303b9;
		public static final int radioButtonStyle = 0x7f0303ba;
		public static final int rangeFillColor = 0x7f0303bb;
		public static final int ratingBarStyle = 0x7f0303bc;
		public static final int ratingBarStyleIndicator = 0x7f0303bd;
		public static final int ratingBarStyleSmall = 0x7f0303be;
		public static final int recyclerViewStyle = 0x7f0303c3;
		public static final int region_heightLessThan = 0x7f0303c4;
		public static final int region_heightMoreThan = 0x7f0303c5;
		public static final int region_widthLessThan = 0x7f0303c6;
		public static final int region_widthMoreThan = 0x7f0303c7;
		public static final int removeEmbeddedFabElevation = 0x7f0303c8;
		public static final int reverseLayout = 0x7f0303ca;
		public static final int rippleColor = 0x7f0303cb;
		public static final int round = 0x7f0303cd;
		public static final int roundPercent = 0x7f0303ce;
		public static final int saturation = 0x7f0303d0;
		public static final int scrimAnimationDuration = 0x7f0303d2;
		public static final int scrimBackground = 0x7f0303d3;
		public static final int scrimVisibleHeightTrigger = 0x7f0303d4;
		public static final int searchHintIcon = 0x7f0303d6;
		public static final int searchIcon = 0x7f0303d7;
		public static final int searchPrefixText = 0x7f0303d8;
		public static final int searchViewStyle = 0x7f0303d9;
		public static final int secondaryActivityAction = 0x7f0303da;
		public static final int secondaryActivityName = 0x7f0303db;
		public static final int seekBarStyle = 0x7f0303dc;
		public static final int selectableItemBackground = 0x7f0303dd;
		public static final int selectableItemBackgroundBorderless = 0x7f0303de;
		public static final int selectionRequired = 0x7f0303df;
		public static final int selectorSize = 0x7f0303e0;
		public static final int shapeAppearance = 0x7f0303e2;
		public static final int shapeAppearanceCornerExtraLarge = 0x7f0303e3;
		public static final int shapeAppearanceCornerExtraSmall = 0x7f0303e4;
		public static final int shapeAppearanceCornerLarge = 0x7f0303e5;
		public static final int shapeAppearanceCornerMedium = 0x7f0303e6;
		public static final int shapeAppearanceCornerSmall = 0x7f0303e7;
		public static final int shapeAppearanceLargeComponent = 0x7f0303e8;
		public static final int shapeAppearanceMediumComponent = 0x7f0303e9;
		public static final int shapeAppearanceOverlay = 0x7f0303ea;
		public static final int shapeAppearanceSmallComponent = 0x7f0303eb;
		public static final int shapeCornerFamily = 0x7f0303ec;
		public static final int shortcutMatchRequired = 0x7f0303ed;
		public static final int shouldRemoveExpandedCorners = 0x7f0303ee;
		public static final int showAnimationBehavior = 0x7f0303ef;
		public static final int showAsAction = 0x7f0303f0;
		public static final int showDelay = 0x7f0303f1;
		public static final int showDividers = 0x7f0303f2;
		public static final int showMotionSpec = 0x7f0303f4;
		public static final int showPaths = 0x7f0303f5;
		public static final int showText = 0x7f0303f6;
		public static final int showTitle = 0x7f0303f7;
		public static final int shrinkMotionSpec = 0x7f0303f8;
		public static final int sideSheetDialogTheme = 0x7f0303f9;
		public static final int sideSheetModalStyle = 0x7f0303fa;
		public static final int simpleItemLayout = 0x7f0303fb;
		public static final int simpleItemSelectedColor = 0x7f0303fc;
		public static final int simpleItemSelectedRippleColor = 0x7f0303fd;
		public static final int simpleItems = 0x7f0303fe;
		public static final int singleChoiceItemLayout = 0x7f0303ff;
		public static final int singleLine = 0x7f030400;
		public static final int singleSelection = 0x7f030401;
		public static final int sizePercent = 0x7f030402;
		public static final int sliderStyle = 0x7f030403;
		public static final int snackbarButtonStyle = 0x7f030404;
		public static final int snackbarStyle = 0x7f030405;
		public static final int snackbarTextViewStyle = 0x7f030406;
		public static final int spanCount = 0x7f030407;
		public static final int spinBars = 0x7f030408;
		public static final int spinnerDropDownItemStyle = 0x7f030409;
		public static final int spinnerStyle = 0x7f03040a;
		public static final int splitLayoutDirection = 0x7f03040b;
		public static final int splitMaxAspectRatioInLandscape = 0x7f03040c;
		public static final int splitMaxAspectRatioInPortrait = 0x7f03040d;
		public static final int splitMinHeightDp = 0x7f03040e;
		public static final int splitMinSmallestWidthDp = 0x7f03040f;
		public static final int splitMinWidthDp = 0x7f030410;
		public static final int splitRatio = 0x7f030411;
		public static final int splitTrack = 0x7f030412;
		public static final int srcCompat = 0x7f030418;
		public static final int stackFromEnd = 0x7f030419;
		public static final int staggered = 0x7f03041a;
		public static final int startIconCheckable = 0x7f03041c;
		public static final int startIconContentDescription = 0x7f03041d;
		public static final int startIconDrawable = 0x7f03041e;
		public static final int startIconMinSize = 0x7f03041f;
		public static final int startIconScaleType = 0x7f030420;
		public static final int startIconTint = 0x7f030421;
		public static final int startIconTintMode = 0x7f030422;
		public static final int state_above_anchor = 0x7f030424;
		public static final int state_collapsed = 0x7f030425;
		public static final int state_collapsible = 0x7f030426;
		public static final int state_dragged = 0x7f030427;
		public static final int state_error = 0x7f030428;
		public static final int state_indeterminate = 0x7f030429;
		public static final int state_liftable = 0x7f03042a;
		public static final int state_lifted = 0x7f03042b;
		public static final int state_with_icon = 0x7f03042c;
		public static final int statusBarBackground = 0x7f03042d;
		public static final int statusBarForeground = 0x7f03042e;
		public static final int statusBarScrim = 0x7f03042f;
		public static final int stickyPlaceholder = 0x7f030430;
		public static final int strokeColor = 0x7f030431;
		public static final int strokeWidth = 0x7f030432;
		public static final int subMenuArrow = 0x7f030433;
		public static final int subheaderColor = 0x7f030434;
		public static final int subheaderInsetEnd = 0x7f030435;
		public static final int subheaderInsetStart = 0x7f030436;
		public static final int subheaderTextAppearance = 0x7f030437;
		public static final int submitBackground = 0x7f030438;
		public static final int subtitle = 0x7f030439;
		public static final int subtitleCentered = 0x7f03043a;
		public static final int subtitleTextAppearance = 0x7f03043b;
		public static final int subtitleTextColor = 0x7f03043c;
		public static final int subtitleTextStyle = 0x7f03043d;
		public static final int suffixText = 0x7f03043e;
		public static final int suffixTextAppearance = 0x7f03043f;
		public static final int suffixTextColor = 0x7f030440;
		public static final int suggestionRowLayout = 0x7f030441;
		public static final int switchMinWidth = 0x7f030443;
		public static final int switchPadding = 0x7f030444;
		public static final int switchStyle = 0x7f030445;
		public static final int switchTextAppearance = 0x7f030446;
		public static final int tabBackground = 0x7f030447;
		public static final int tabContentStart = 0x7f030448;
		public static final int tabGravity = 0x7f030449;
		public static final int tabIconTint = 0x7f03044a;
		public static final int tabIconTintMode = 0x7f03044b;
		public static final int tabIndicator = 0x7f03044c;
		public static final int tabIndicatorAnimationDuration = 0x7f03044d;
		public static final int tabIndicatorAnimationMode = 0x7f03044e;
		public static final int tabIndicatorColor = 0x7f03044f;
		public static final int tabIndicatorFullWidth = 0x7f030450;
		public static final int tabIndicatorGravity = 0x7f030451;
		public static final int tabIndicatorHeight = 0x7f030452;
		public static final int tabInlineLabel = 0x7f030453;
		public static final int tabMaxWidth = 0x7f030454;
		public static final int tabMinWidth = 0x7f030455;
		public static final int tabMode = 0x7f030456;
		public static final int tabPadding = 0x7f030457;
		public static final int tabPaddingBottom = 0x7f030458;
		public static final int tabPaddingEnd = 0x7f030459;
		public static final int tabPaddingStart = 0x7f03045a;
		public static final int tabPaddingTop = 0x7f03045b;
		public static final int tabRippleColor = 0x7f03045c;
		public static final int tabSecondaryStyle = 0x7f03045d;
		public static final int tabSelectedTextAppearance = 0x7f03045e;
		public static final int tabSelectedTextColor = 0x7f03045f;
		public static final int tabStyle = 0x7f030460;
		public static final int tabTextAppearance = 0x7f030461;
		public static final int tabTextColor = 0x7f030462;
		public static final int tabUnboundedRipple = 0x7f030463;
		public static final int tag = 0x7f030464;
		public static final int targetId = 0x7f030465;
		public static final int telltales_tailColor = 0x7f030467;
		public static final int telltales_tailScale = 0x7f030468;
		public static final int telltales_velocityMode = 0x7f030469;
		public static final int textAllCaps = 0x7f03046a;
		public static final int textAppearanceBody1 = 0x7f03046b;
		public static final int textAppearanceBody2 = 0x7f03046c;
		public static final int textAppearanceBodyLarge = 0x7f03046d;
		public static final int textAppearanceBodyMedium = 0x7f03046e;
		public static final int textAppearanceBodySmall = 0x7f03046f;
		public static final int textAppearanceButton = 0x7f030470;
		public static final int textAppearanceCaption = 0x7f030471;
		public static final int textAppearanceDisplayLarge = 0x7f030472;
		public static final int textAppearanceDisplayMedium = 0x7f030473;
		public static final int textAppearanceDisplaySmall = 0x7f030474;
		public static final int textAppearanceHeadline1 = 0x7f030475;
		public static final int textAppearanceHeadline2 = 0x7f030476;
		public static final int textAppearanceHeadline3 = 0x7f030477;
		public static final int textAppearanceHeadline4 = 0x7f030478;
		public static final int textAppearanceHeadline5 = 0x7f030479;
		public static final int textAppearanceHeadline6 = 0x7f03047a;
		public static final int textAppearanceHeadlineLarge = 0x7f03047b;
		public static final int textAppearanceHeadlineMedium = 0x7f03047c;
		public static final int textAppearanceHeadlineSmall = 0x7f03047d;
		public static final int textAppearanceLabelLarge = 0x7f03047e;
		public static final int textAppearanceLabelMedium = 0x7f03047f;
		public static final int textAppearanceLabelSmall = 0x7f030480;
		public static final int textAppearanceLargePopupMenu = 0x7f030481;
		public static final int textAppearanceLineHeightEnabled = 0x7f030482;
		public static final int textAppearanceListItem = 0x7f030483;
		public static final int textAppearanceListItemSecondary = 0x7f030484;
		public static final int textAppearanceListItemSmall = 0x7f030485;
		public static final int textAppearanceOverline = 0x7f030486;
		public static final int textAppearancePopupMenuHeader = 0x7f030487;
		public static final int textAppearanceSearchResultSubtitle = 0x7f030488;
		public static final int textAppearanceSearchResultTitle = 0x7f030489;
		public static final int textAppearanceSmallPopupMenu = 0x7f03048a;
		public static final int textAppearanceSubtitle1 = 0x7f03048b;
		public static final int textAppearanceSubtitle2 = 0x7f03048c;
		public static final int textAppearanceTitleLarge = 0x7f03048d;
		public static final int textAppearanceTitleMedium = 0x7f03048e;
		public static final int textAppearanceTitleSmall = 0x7f03048f;
		public static final int textColorAlertDialogListItem = 0x7f030495;
		public static final int textColorSearchUrl = 0x7f030496;
		public static final int textEndPadding = 0x7f030497;
		public static final int textInputFilledDenseStyle = 0x7f030499;
		public static final int textInputFilledExposedDropdownMenuStyle = 0x7f03049a;
		public static final int textInputFilledStyle = 0x7f03049b;
		public static final int textInputLayoutFocusedRectEnabled = 0x7f03049c;
		public static final int textInputOutlinedDenseStyle = 0x7f03049d;
		public static final int textInputOutlinedExposedDropdownMenuStyle = 0x7f03049e;
		public static final int textInputOutlinedStyle = 0x7f03049f;
		public static final int textInputStyle = 0x7f0304a0;
		public static final int textLocale = 0x7f0304a1;
		public static final int textStartPadding = 0x7f0304a6;
		public static final int theme = 0x7f0304ab;
		public static final int thickness = 0x7f0304ac;
		public static final int thumbColor = 0x7f0304ad;
		public static final int thumbElevation = 0x7f0304ae;
		public static final int thumbIcon = 0x7f0304b0;
		public static final int thumbIconSize = 0x7f0304b1;
		public static final int thumbIconTint = 0x7f0304b2;
		public static final int thumbIconTintMode = 0x7f0304b3;
		public static final int thumbRadius = 0x7f0304b4;
		public static final int thumbStrokeColor = 0x7f0304b5;
		public static final int thumbStrokeWidth = 0x7f0304b6;
		public static final int thumbTextPadding = 0x7f0304b7;
		public static final int thumbTint = 0x7f0304b8;
		public static final int thumbTintMode = 0x7f0304b9;
		public static final int tickColor = 0x7f0304bc;
		public static final int tickColorActive = 0x7f0304bd;
		public static final int tickColorInactive = 0x7f0304be;
		public static final int tickMark = 0x7f0304bf;
		public static final int tickMarkTint = 0x7f0304c0;
		public static final int tickMarkTintMode = 0x7f0304c1;
		public static final int tickRadiusActive = 0x7f0304c2;
		public static final int tickRadiusInactive = 0x7f0304c3;
		public static final int tickVisible = 0x7f0304c4;
		public static final int tint = 0x7f0304c5;
		public static final int tintMode = 0x7f0304c6;
		public static final int tintNavigationIcon = 0x7f0304c7;
		public static final int title = 0x7f0304c8;
		public static final int titleCentered = 0x7f0304c9;
		public static final int titleCollapseMode = 0x7f0304ca;
		public static final int titleEnabled = 0x7f0304cb;
		public static final int titleMargin = 0x7f0304cc;
		public static final int titleMarginBottom = 0x7f0304cd;
		public static final int titleMarginEnd = 0x7f0304ce;
		public static final int titleMarginStart = 0x7f0304cf;
		public static final int titleMarginTop = 0x7f0304d0;
		public static final int titleMargins = 0x7f0304d1;
		public static final int titlePositionInterpolator = 0x7f0304d2;
		public static final int titleTextAppearance = 0x7f0304d3;
		public static final int titleTextColor = 0x7f0304d4;
		public static final int titleTextEllipsize = 0x7f0304d5;
		public static final int titleTextStyle = 0x7f0304d6;
		public static final int toggleCheckedStateOnClick = 0x7f0304d7;
		public static final int toolbarId = 0x7f0304d8;
		public static final int toolbarNavigationButtonStyle = 0x7f0304d9;
		public static final int toolbarStyle = 0x7f0304da;
		public static final int toolbarSurfaceStyle = 0x7f0304db;
		public static final int tooltipForegroundColor = 0x7f0304dc;
		public static final int tooltipFrameBackground = 0x7f0304dd;
		public static final int tooltipStyle = 0x7f0304de;
		public static final int tooltipText = 0x7f0304df;
		public static final int topInsetScrimEnabled = 0x7f0304e0;
		public static final int touchAnchorId = 0x7f0304e1;
		public static final int touchAnchorSide = 0x7f0304e2;
		public static final int touchRegionId = 0x7f0304e3;
		public static final int track = 0x7f0304e4;
		public static final int trackColor = 0x7f0304e5;
		public static final int trackColorActive = 0x7f0304e6;
		public static final int trackColorInactive = 0x7f0304e7;
		public static final int trackCornerRadius = 0x7f0304e8;
		public static final int trackDecoration = 0x7f0304e9;
		public static final int trackDecorationTint = 0x7f0304ea;
		public static final int trackDecorationTintMode = 0x7f0304eb;
		public static final int trackHeight = 0x7f0304ec;
		public static final int trackThickness = 0x7f0304ef;
		public static final int trackTint = 0x7f0304f0;
		public static final int trackTintMode = 0x7f0304f1;
		public static final int transitionDisable = 0x7f0304f3;
		public static final int transitionEasing = 0x7f0304f4;
		public static final int transitionFlags = 0x7f0304f5;
		public static final int transitionPathRotate = 0x7f0304f6;
		public static final int transitionShapeAppearance = 0x7f0304f7;
		public static final int triggerId = 0x7f0304f8;
		public static final int triggerReceiver = 0x7f0304f9;
		public static final int triggerSlack = 0x7f0304fa;
		public static final int ttcIndex = 0x7f0304fb;
		public static final int useCompatPadding = 0x7f0304fe;
		public static final int useDrawerArrowDrawable = 0x7f0304ff;
		public static final int useMaterialThemeColors = 0x7f030500;
		public static final int values = 0x7f030501;
		public static final int verticalOffset = 0x7f030502;
		public static final int verticalOffsetWithText = 0x7f030503;
		public static final int viewInflaterClass = 0x7f030504;
		public static final int visibilityMode = 0x7f030509;
		public static final int voiceIcon = 0x7f03050a;
		public static final int warmth = 0x7f03050b;
		public static final int waveDecay = 0x7f03050c;
		public static final int waveOffset = 0x7f03050d;
		public static final int wavePeriod = 0x7f03050e;
		public static final int waveShape = 0x7f030510;
		public static final int waveVariesBy = 0x7f030511;
		public static final int windowActionBar = 0x7f030512;
		public static final int windowActionBarOverlay = 0x7f030513;
		public static final int windowActionModeOverlay = 0x7f030514;
		public static final int windowFixedHeightMajor = 0x7f030515;
		public static final int windowFixedHeightMinor = 0x7f030516;
		public static final int windowFixedWidthMajor = 0x7f030517;
		public static final int windowFixedWidthMinor = 0x7f030518;
		public static final int windowMinWidthMajor = 0x7f030519;
		public static final int windowMinWidthMinor = 0x7f03051a;
		public static final int windowNoTitle = 0x7f03051b;
		public static final int yearSelectedStyle = 0x7f03051c;
		public static final int yearStyle = 0x7f03051d;
		public static final int yearTodayStyle = 0x7f03051e;
	}
	public static final class bool {
		public static final int abc_action_bar_embed_tabs = 0x7f040000;
		public static final int abc_config_actionMenuItemAllCaps = 0x7f040001;
		public static final int mtrl_btn_textappearance_all_caps = 0x7f040002;
	}
	public static final class color {
		public static final int abc_background_cache_hint_selector_material_dark = 0x7f050000;
		public static final int abc_background_cache_hint_selector_material_light = 0x7f050001;
		public static final int abc_btn_colored_borderless_text_material = 0x7f050002;
		public static final int abc_btn_colored_text_material = 0x7f050003;
		public static final int abc_color_highlight_material = 0x7f050004;
		public static final int abc_decor_view_status_guard = 0x7f050005;
		public static final int abc_decor_view_status_guard_light = 0x7f050006;
		public static final int abc_hint_foreground_material_dark = 0x7f050007;
		public static final int abc_hint_foreground_material_light = 0x7f050008;
		public static final int abc_primary_text_disable_only_material_dark = 0x7f050009;
		public static final int abc_primary_text_disable_only_material_light = 0x7f05000a;
		public static final int abc_primary_text_material_dark = 0x7f05000b;
		public static final int abc_primary_text_material_light = 0x7f05000c;
		public static final int abc_search_url_text = 0x7f05000d;
		public static final int abc_search_url_text_normal = 0x7f05000e;
		public static final int abc_search_url_text_pressed = 0x7f05000f;
		public static final int abc_search_url_text_selected = 0x7f050010;
		public static final int abc_secondary_text_material_dark = 0x7f050011;
		public static final int abc_secondary_text_material_light = 0x7f050012;
		public static final int abc_tint_btn_checkable = 0x7f050013;
		public static final int abc_tint_default = 0x7f050014;
		public static final int abc_tint_edittext = 0x7f050015;
		public static final int abc_tint_seek_thumb = 0x7f050016;
		public static final int abc_tint_spinner = 0x7f050017;
		public static final int abc_tint_switch_track = 0x7f050018;
		public static final int accent_material_dark = 0x7f050019;
		public static final int accent_material_light = 0x7f05001a;
		public static final int androidx_core_ripple_material_light = 0x7f05001b;
		public static final int androidx_core_secondary_text_default_material_light = 0x7f05001c;
		public static final int background_floating_material_dark = 0x7f05001d;
		public static final int background_floating_material_light = 0x7f05001e;
		public static final int background_material_dark = 0x7f05001f;
		public static final int background_material_light = 0x7f050020;
		public static final int bright_foreground_disabled_material_dark = 0x7f050021;
		public static final int bright_foreground_disabled_material_light = 0x7f050022;
		public static final int bright_foreground_inverse_material_dark = 0x7f050023;
		public static final int bright_foreground_inverse_material_light = 0x7f050024;
		public static final int bright_foreground_material_dark = 0x7f050025;
		public static final int bright_foreground_material_light = 0x7f050026;
		public static final int button_material_dark = 0x7f05002b;
		public static final int button_material_light = 0x7f05002c;
		public static final int call_notification_answer_color = 0x7f05002d;
		public static final int call_notification_decline_color = 0x7f05002e;
		public static final int cardview_dark_background = 0x7f05002f;
		public static final int cardview_light_background = 0x7f050030;
		public static final int cardview_shadow_end_color = 0x7f050031;
		public static final int cardview_shadow_start_color = 0x7f050032;
		public static final int design_bottom_navigation_shadow_color = 0x7f050037;
		public static final int design_box_stroke_color = 0x7f050038;
		public static final int design_dark_default_color_background = 0x7f050039;
		public static final int design_dark_default_color_error = 0x7f05003a;
		public static final int design_dark_default_color_on_background = 0x7f05003b;
		public static final int design_dark_default_color_on_error = 0x7f05003c;
		public static final int design_dark_default_color_on_primary = 0x7f05003d;
		public static final int design_dark_default_color_on_secondary = 0x7f05003e;
		public static final int design_dark_default_color_on_surface = 0x7f05003f;
		public static final int design_dark_default_color_primary = 0x7f050040;
		public static final int design_dark_default_color_primary_dark = 0x7f050041;
		public static final int design_dark_default_color_primary_variant = 0x7f050042;
		public static final int design_dark_default_color_secondary = 0x7f050043;
		public static final int design_dark_default_color_secondary_variant = 0x7f050044;
		public static final int design_dark_default_color_surface = 0x7f050045;
		public static final int design_default_color_background = 0x7f050046;
		public static final int design_default_color_error = 0x7f050047;
		public static final int design_default_color_on_background = 0x7f050048;
		public static final int design_default_color_on_error = 0x7f050049;
		public static final int design_default_color_on_primary = 0x7f05004a;
		public static final int design_default_color_on_secondary = 0x7f05004b;
		public static final int design_default_color_on_surface = 0x7f05004c;
		public static final int design_default_color_primary = 0x7f05004d;
		public static final int design_default_color_primary_dark = 0x7f05004e;
		public static final int design_default_color_primary_variant = 0x7f05004f;
		public static final int design_default_color_secondary = 0x7f050050;
		public static final int design_default_color_secondary_variant = 0x7f050051;
		public static final int design_default_color_surface = 0x7f050052;
		public static final int design_error = 0x7f050053;
		public static final int design_fab_shadow_end_color = 0x7f050054;
		public static final int design_fab_shadow_mid_color = 0x7f050055;
		public static final int design_fab_shadow_start_color = 0x7f050056;
		public static final int design_fab_stroke_end_inner_color = 0x7f050057;
		public static final int design_fab_stroke_end_outer_color = 0x7f050058;
		public static final int design_fab_stroke_top_inner_color = 0x7f050059;
		public static final int design_fab_stroke_top_outer_color = 0x7f05005a;
		public static final int design_icon_tint = 0x7f05005b;
		public static final int design_snackbar_background_color = 0x7f05005c;
		public static final int dim_foreground_disabled_material_dark = 0x7f05005d;
		public static final int dim_foreground_disabled_material_light = 0x7f05005e;
		public static final int dim_foreground_material_dark = 0x7f05005f;
		public static final int dim_foreground_material_light = 0x7f050060;
		public static final int error_color_material_dark = 0x7f050061;
		public static final int error_color_material_light = 0x7f050062;
		public static final int foreground_material_dark = 0x7f050063;
		public static final int foreground_material_light = 0x7f050064;
		public static final int highlighted_text_material_dark = 0x7f050065;
		public static final int highlighted_text_material_light = 0x7f050066;
		public static final int m3_appbar_overlay_color = 0x7f050067;
		public static final int m3_assist_chip_icon_tint_color = 0x7f050068;
		public static final int m3_assist_chip_stroke_color = 0x7f050069;
		public static final int m3_bottom_sheet_drag_handle_color = 0x7f05006a;
		public static final int m3_button_background_color_selector = 0x7f05006b;
		public static final int m3_button_foreground_color_selector = 0x7f05006c;
		public static final int m3_button_outline_color_selector = 0x7f05006d;
		public static final int m3_button_ripple_color = 0x7f05006e;
		public static final int m3_button_ripple_color_selector = 0x7f05006f;
		public static final int m3_calendar_item_disabled_text = 0x7f050070;
		public static final int m3_calendar_item_stroke_color = 0x7f050071;
		public static final int m3_card_foreground_color = 0x7f050072;
		public static final int m3_card_ripple_color = 0x7f050073;
		public static final int m3_card_stroke_color = 0x7f050074;
		public static final int m3_checkbox_button_icon_tint = 0x7f050075;
		public static final int m3_checkbox_button_tint = 0x7f050076;
		public static final int m3_chip_assist_text_color = 0x7f050077;
		public static final int m3_chip_background_color = 0x7f050078;
		public static final int m3_chip_ripple_color = 0x7f050079;
		public static final int m3_chip_stroke_color = 0x7f05007a;
		public static final int m3_chip_text_color = 0x7f05007b;
		public static final int m3_dark_default_color_primary_text = 0x7f05007c;
		public static final int m3_dark_default_color_secondary_text = 0x7f05007d;
		public static final int m3_dark_highlighted_text = 0x7f05007e;
		public static final int m3_dark_hint_foreground = 0x7f05007f;
		public static final int m3_dark_primary_text_disable_only = 0x7f050080;
		public static final int m3_default_color_primary_text = 0x7f050081;
		public static final int m3_default_color_secondary_text = 0x7f050082;
		public static final int m3_dynamic_dark_default_color_primary_text = 0x7f050083;
		public static final int m3_dynamic_dark_default_color_secondary_text = 0x7f050084;
		public static final int m3_dynamic_dark_highlighted_text = 0x7f050085;
		public static final int m3_dynamic_dark_hint_foreground = 0x7f050086;
		public static final int m3_dynamic_dark_primary_text_disable_only = 0x7f050087;
		public static final int m3_dynamic_default_color_primary_text = 0x7f050088;
		public static final int m3_dynamic_default_color_secondary_text = 0x7f050089;
		public static final int m3_dynamic_highlighted_text = 0x7f05008a;
		public static final int m3_dynamic_hint_foreground = 0x7f05008b;
		public static final int m3_dynamic_primary_text_disable_only = 0x7f05008c;
		public static final int m3_efab_ripple_color_selector = 0x7f05008d;
		public static final int m3_elevated_chip_background_color = 0x7f05008e;
		public static final int m3_fab_efab_background_color_selector = 0x7f05008f;
		public static final int m3_fab_efab_foreground_color_selector = 0x7f050090;
		public static final int m3_fab_ripple_color_selector = 0x7f050091;
		public static final int m3_filled_icon_button_container_color_selector = 0x7f050092;
		public static final int m3_highlighted_text = 0x7f050093;
		public static final int m3_hint_foreground = 0x7f050094;
		public static final int m3_icon_button_icon_color_selector = 0x7f050095;
		public static final int m3_navigation_bar_item_with_indicator_icon_tint = 0x7f050096;
		public static final int m3_navigation_bar_item_with_indicator_label_tint = 0x7f050097;
		public static final int m3_navigation_bar_ripple_color_selector = 0x7f050098;
		public static final int m3_navigation_item_background_color = 0x7f050099;
		public static final int m3_navigation_item_icon_tint = 0x7f05009a;
		public static final int m3_navigation_item_ripple_color = 0x7f05009b;
		public static final int m3_navigation_item_text_color = 0x7f05009c;
		public static final int m3_navigation_rail_item_with_indicator_icon_tint = 0x7f05009d;
		public static final int m3_navigation_rail_item_with_indicator_label_tint = 0x7f05009e;
		public static final int m3_navigation_rail_ripple_color_selector = 0x7f05009f;
		public static final int m3_popupmenu_overlay_color = 0x7f0500a0;
		public static final int m3_primary_text_disable_only = 0x7f0500a1;
		public static final int m3_radiobutton_button_tint = 0x7f0500a2;
		public static final int m3_radiobutton_ripple_tint = 0x7f0500a3;
		public static final int m3_ref_palette_black = 0x7f0500a4;
		public static final int m3_ref_palette_dynamic_neutral0 = 0x7f0500a5;
		public static final int m3_ref_palette_dynamic_neutral10 = 0x7f0500a6;
		public static final int m3_ref_palette_dynamic_neutral100 = 0x7f0500a7;
		public static final int m3_ref_palette_dynamic_neutral12 = 0x7f0500a8;
		public static final int m3_ref_palette_dynamic_neutral17 = 0x7f0500a9;
		public static final int m3_ref_palette_dynamic_neutral20 = 0x7f0500aa;
		public static final int m3_ref_palette_dynamic_neutral22 = 0x7f0500ab;
		public static final int m3_ref_palette_dynamic_neutral24 = 0x7f0500ac;
		public static final int m3_ref_palette_dynamic_neutral30 = 0x7f0500ad;
		public static final int m3_ref_palette_dynamic_neutral4 = 0x7f0500ae;
		public static final int m3_ref_palette_dynamic_neutral40 = 0x7f0500af;
		public static final int m3_ref_palette_dynamic_neutral50 = 0x7f0500b0;
		public static final int m3_ref_palette_dynamic_neutral6 = 0x7f0500b1;
		public static final int m3_ref_palette_dynamic_neutral60 = 0x7f0500b2;
		public static final int m3_ref_palette_dynamic_neutral70 = 0x7f0500b3;
		public static final int m3_ref_palette_dynamic_neutral80 = 0x7f0500b4;
		public static final int m3_ref_palette_dynamic_neutral87 = 0x7f0500b5;
		public static final int m3_ref_palette_dynamic_neutral90 = 0x7f0500b6;
		public static final int m3_ref_palette_dynamic_neutral92 = 0x7f0500b7;
		public static final int m3_ref_palette_dynamic_neutral94 = 0x7f0500b8;
		public static final int m3_ref_palette_dynamic_neutral95 = 0x7f0500b9;
		public static final int m3_ref_palette_dynamic_neutral96 = 0x7f0500ba;
		public static final int m3_ref_palette_dynamic_neutral98 = 0x7f0500bb;
		public static final int m3_ref_palette_dynamic_neutral99 = 0x7f0500bc;
		public static final int m3_ref_palette_dynamic_neutral_variant0 = 0x7f0500bd;
		public static final int m3_ref_palette_dynamic_neutral_variant10 = 0x7f0500be;
		public static final int m3_ref_palette_dynamic_neutral_variant100 = 0x7f0500bf;
		public static final int m3_ref_palette_dynamic_neutral_variant12 = 0x7f0500c0;
		public static final int m3_ref_palette_dynamic_neutral_variant17 = 0x7f0500c1;
		public static final int m3_ref_palette_dynamic_neutral_variant20 = 0x7f0500c2;
		public static final int m3_ref_palette_dynamic_neutral_variant22 = 0x7f0500c3;
		public static final int m3_ref_palette_dynamic_neutral_variant24 = 0x7f0500c4;
		public static final int m3_ref_palette_dynamic_neutral_variant30 = 0x7f0500c5;
		public static final int m3_ref_palette_dynamic_neutral_variant4 = 0x7f0500c6;
		public static final int m3_ref_palette_dynamic_neutral_variant40 = 0x7f0500c7;
		public static final int m3_ref_palette_dynamic_neutral_variant50 = 0x7f0500c8;
		public static final int m3_ref_palette_dynamic_neutral_variant6 = 0x7f0500c9;
		public static final int m3_ref_palette_dynamic_neutral_variant60 = 0x7f0500ca;
		public static final int m3_ref_palette_dynamic_neutral_variant70 = 0x7f0500cb;
		public static final int m3_ref_palette_dynamic_neutral_variant80 = 0x7f0500cc;
		public static final int m3_ref_palette_dynamic_neutral_variant87 = 0x7f0500cd;
		public static final int m3_ref_palette_dynamic_neutral_variant90 = 0x7f0500ce;
		public static final int m3_ref_palette_dynamic_neutral_variant92 = 0x7f0500cf;
		public static final int m3_ref_palette_dynamic_neutral_variant94 = 0x7f0500d0;
		public static final int m3_ref_palette_dynamic_neutral_variant95 = 0x7f0500d1;
		public static final int m3_ref_palette_dynamic_neutral_variant96 = 0x7f0500d2;
		public static final int m3_ref_palette_dynamic_neutral_variant98 = 0x7f0500d3;
		public static final int m3_ref_palette_dynamic_neutral_variant99 = 0x7f0500d4;
		public static final int m3_ref_palette_dynamic_primary0 = 0x7f0500d5;
		public static final int m3_ref_palette_dynamic_primary10 = 0x7f0500d6;
		public static final int m3_ref_palette_dynamic_primary100 = 0x7f0500d7;
		public static final int m3_ref_palette_dynamic_primary20 = 0x7f0500d8;
		public static final int m3_ref_palette_dynamic_primary30 = 0x7f0500d9;
		public static final int m3_ref_palette_dynamic_primary40 = 0x7f0500da;
		public static final int m3_ref_palette_dynamic_primary50 = 0x7f0500db;
		public static final int m3_ref_palette_dynamic_primary60 = 0x7f0500dc;
		public static final int m3_ref_palette_dynamic_primary70 = 0x7f0500dd;
		public static final int m3_ref_palette_dynamic_primary80 = 0x7f0500de;
		public static final int m3_ref_palette_dynamic_primary90 = 0x7f0500df;
		public static final int m3_ref_palette_dynamic_primary95 = 0x7f0500e0;
		public static final int m3_ref_palette_dynamic_primary99 = 0x7f0500e1;
		public static final int m3_ref_palette_dynamic_secondary0 = 0x7f0500e2;
		public static final int m3_ref_palette_dynamic_secondary10 = 0x7f0500e3;
		public static final int m3_ref_palette_dynamic_secondary100 = 0x7f0500e4;
		public static final int m3_ref_palette_dynamic_secondary20 = 0x7f0500e5;
		public static final int m3_ref_palette_dynamic_secondary30 = 0x7f0500e6;
		public static final int m3_ref_palette_dynamic_secondary40 = 0x7f0500e7;
		public static final int m3_ref_palette_dynamic_secondary50 = 0x7f0500e8;
		public static final int m3_ref_palette_dynamic_secondary60 = 0x7f0500e9;
		public static final int m3_ref_palette_dynamic_secondary70 = 0x7f0500ea;
		public static final int m3_ref_palette_dynamic_secondary80 = 0x7f0500eb;
		public static final int m3_ref_palette_dynamic_secondary90 = 0x7f0500ec;
		public static final int m3_ref_palette_dynamic_secondary95 = 0x7f0500ed;
		public static final int m3_ref_palette_dynamic_secondary99 = 0x7f0500ee;
		public static final int m3_ref_palette_dynamic_tertiary0 = 0x7f0500ef;
		public static final int m3_ref_palette_dynamic_tertiary10 = 0x7f0500f0;
		public static final int m3_ref_palette_dynamic_tertiary100 = 0x7f0500f1;
		public static final int m3_ref_palette_dynamic_tertiary20 = 0x7f0500f2;
		public static final int m3_ref_palette_dynamic_tertiary30 = 0x7f0500f3;
		public static final int m3_ref_palette_dynamic_tertiary40 = 0x7f0500f4;
		public static final int m3_ref_palette_dynamic_tertiary50 = 0x7f0500f5;
		public static final int m3_ref_palette_dynamic_tertiary60 = 0x7f0500f6;
		public static final int m3_ref_palette_dynamic_tertiary70 = 0x7f0500f7;
		public static final int m3_ref_palette_dynamic_tertiary80 = 0x7f0500f8;
		public static final int m3_ref_palette_dynamic_tertiary90 = 0x7f0500f9;
		public static final int m3_ref_palette_dynamic_tertiary95 = 0x7f0500fa;
		public static final int m3_ref_palette_dynamic_tertiary99 = 0x7f0500fb;
		public static final int m3_ref_palette_error0 = 0x7f0500fc;
		public static final int m3_ref_palette_error10 = 0x7f0500fd;
		public static final int m3_ref_palette_error100 = 0x7f0500fe;
		public static final int m3_ref_palette_error20 = 0x7f0500ff;
		public static final int m3_ref_palette_error30 = 0x7f050100;
		public static final int m3_ref_palette_error40 = 0x7f050101;
		public static final int m3_ref_palette_error50 = 0x7f050102;
		public static final int m3_ref_palette_error60 = 0x7f050103;
		public static final int m3_ref_palette_error70 = 0x7f050104;
		public static final int m3_ref_palette_error80 = 0x7f050105;
		public static final int m3_ref_palette_error90 = 0x7f050106;
		public static final int m3_ref_palette_error95 = 0x7f050107;
		public static final int m3_ref_palette_error99 = 0x7f050108;
		public static final int m3_ref_palette_neutral0 = 0x7f050109;
		public static final int m3_ref_palette_neutral10 = 0x7f05010a;
		public static final int m3_ref_palette_neutral100 = 0x7f05010b;
		public static final int m3_ref_palette_neutral12 = 0x7f05010c;
		public static final int m3_ref_palette_neutral17 = 0x7f05010d;
		public static final int m3_ref_palette_neutral20 = 0x7f05010e;
		public static final int m3_ref_palette_neutral22 = 0x7f05010f;
		public static final int m3_ref_palette_neutral24 = 0x7f050110;
		public static final int m3_ref_palette_neutral30 = 0x7f050111;
		public static final int m3_ref_palette_neutral4 = 0x7f050112;
		public static final int m3_ref_palette_neutral40 = 0x7f050113;
		public static final int m3_ref_palette_neutral50 = 0x7f050114;
		public static final int m3_ref_palette_neutral6 = 0x7f050115;
		public static final int m3_ref_palette_neutral60 = 0x7f050116;
		public static final int m3_ref_palette_neutral70 = 0x7f050117;
		public static final int m3_ref_palette_neutral80 = 0x7f050118;
		public static final int m3_ref_palette_neutral87 = 0x7f050119;
		public static final int m3_ref_palette_neutral90 = 0x7f05011a;
		public static final int m3_ref_palette_neutral92 = 0x7f05011b;
		public static final int m3_ref_palette_neutral94 = 0x7f05011c;
		public static final int m3_ref_palette_neutral95 = 0x7f05011d;
		public static final int m3_ref_palette_neutral96 = 0x7f05011e;
		public static final int m3_ref_palette_neutral98 = 0x7f05011f;
		public static final int m3_ref_palette_neutral99 = 0x7f050120;
		public static final int m3_ref_palette_neutral_variant0 = 0x7f050121;
		public static final int m3_ref_palette_neutral_variant10 = 0x7f050122;
		public static final int m3_ref_palette_neutral_variant100 = 0x7f050123;
		public static final int m3_ref_palette_neutral_variant20 = 0x7f050124;
		public static final int m3_ref_palette_neutral_variant30 = 0x7f050125;
		public static final int m3_ref_palette_neutral_variant40 = 0x7f050126;
		public static final int m3_ref_palette_neutral_variant50 = 0x7f050127;
		public static final int m3_ref_palette_neutral_variant60 = 0x7f050128;
		public static final int m3_ref_palette_neutral_variant70 = 0x7f050129;
		public static final int m3_ref_palette_neutral_variant80 = 0x7f05012a;
		public static final int m3_ref_palette_neutral_variant90 = 0x7f05012b;
		public static final int m3_ref_palette_neutral_variant95 = 0x7f05012c;
		public static final int m3_ref_palette_neutral_variant99 = 0x7f05012d;
		public static final int m3_ref_palette_primary0 = 0x7f05012e;
		public static final int m3_ref_palette_primary10 = 0x7f05012f;
		public static final int m3_ref_palette_primary100 = 0x7f050130;
		public static final int m3_ref_palette_primary20 = 0x7f050131;
		public static final int m3_ref_palette_primary30 = 0x7f050132;
		public static final int m3_ref_palette_primary40 = 0x7f050133;
		public static final int m3_ref_palette_primary50 = 0x7f050134;
		public static final int m3_ref_palette_primary60 = 0x7f050135;
		public static final int m3_ref_palette_primary70 = 0x7f050136;
		public static final int m3_ref_palette_primary80 = 0x7f050137;
		public static final int m3_ref_palette_primary90 = 0x7f050138;
		public static final int m3_ref_palette_primary95 = 0x7f050139;
		public static final int m3_ref_palette_primary99 = 0x7f05013a;
		public static final int m3_ref_palette_secondary0 = 0x7f05013b;
		public static final int m3_ref_palette_secondary10 = 0x7f05013c;
		public static final int m3_ref_palette_secondary100 = 0x7f05013d;
		public static final int m3_ref_palette_secondary20 = 0x7f05013e;
		public static final int m3_ref_palette_secondary30 = 0x7f05013f;
		public static final int m3_ref_palette_secondary40 = 0x7f050140;
		public static final int m3_ref_palette_secondary50 = 0x7f050141;
		public static final int m3_ref_palette_secondary60 = 0x7f050142;
		public static final int m3_ref_palette_secondary70 = 0x7f050143;
		public static final int m3_ref_palette_secondary80 = 0x7f050144;
		public static final int m3_ref_palette_secondary90 = 0x7f050145;
		public static final int m3_ref_palette_secondary95 = 0x7f050146;
		public static final int m3_ref_palette_secondary99 = 0x7f050147;
		public static final int m3_ref_palette_tertiary0 = 0x7f050148;
		public static final int m3_ref_palette_tertiary10 = 0x7f050149;
		public static final int m3_ref_palette_tertiary100 = 0x7f05014a;
		public static final int m3_ref_palette_tertiary20 = 0x7f05014b;
		public static final int m3_ref_palette_tertiary30 = 0x7f05014c;
		public static final int m3_ref_palette_tertiary40 = 0x7f05014d;
		public static final int m3_ref_palette_tertiary50 = 0x7f05014e;
		public static final int m3_ref_palette_tertiary60 = 0x7f05014f;
		public static final int m3_ref_palette_tertiary70 = 0x7f050150;
		public static final int m3_ref_palette_tertiary80 = 0x7f050151;
		public static final int m3_ref_palette_tertiary90 = 0x7f050152;
		public static final int m3_ref_palette_tertiary95 = 0x7f050153;
		public static final int m3_ref_palette_tertiary99 = 0x7f050154;
		public static final int m3_ref_palette_white = 0x7f050155;
		public static final int m3_selection_control_ripple_color_selector = 0x7f050156;
		public static final int m3_simple_item_ripple_color = 0x7f050157;
		public static final int m3_slider_active_track_color = 0x7f050158;
		public static final int m3_slider_inactive_track_color = 0x7f05015b;
		public static final int m3_slider_thumb_color = 0x7f05015d;
		public static final int m3_switch_thumb_tint = 0x7f05015f;
		public static final int m3_switch_track_tint = 0x7f050160;
		public static final int m3_sys_color_dark_background = 0x7f050161;
		public static final int m3_sys_color_dark_error = 0x7f050162;
		public static final int m3_sys_color_dark_error_container = 0x7f050163;
		public static final int m3_sys_color_dark_inverse_on_surface = 0x7f050164;
		public static final int m3_sys_color_dark_inverse_primary = 0x7f050165;
		public static final int m3_sys_color_dark_inverse_surface = 0x7f050166;
		public static final int m3_sys_color_dark_on_background = 0x7f050167;
		public static final int m3_sys_color_dark_on_error = 0x7f050168;
		public static final int m3_sys_color_dark_on_error_container = 0x7f050169;
		public static final int m3_sys_color_dark_on_primary = 0x7f05016a;
		public static final int m3_sys_color_dark_on_primary_container = 0x7f05016b;
		public static final int m3_sys_color_dark_on_secondary = 0x7f05016c;
		public static final int m3_sys_color_dark_on_secondary_container = 0x7f05016d;
		public static final int m3_sys_color_dark_on_surface = 0x7f05016e;
		public static final int m3_sys_color_dark_on_surface_variant = 0x7f05016f;
		public static final int m3_sys_color_dark_on_tertiary = 0x7f050170;
		public static final int m3_sys_color_dark_on_tertiary_container = 0x7f050171;
		public static final int m3_sys_color_dark_outline = 0x7f050172;
		public static final int m3_sys_color_dark_outline_variant = 0x7f050173;
		public static final int m3_sys_color_dark_primary = 0x7f050174;
		public static final int m3_sys_color_dark_primary_container = 0x7f050175;
		public static final int m3_sys_color_dark_secondary = 0x7f050176;
		public static final int m3_sys_color_dark_secondary_container = 0x7f050177;
		public static final int m3_sys_color_dark_surface = 0x7f050178;
		public static final int m3_sys_color_dark_surface_bright = 0x7f050179;
		public static final int m3_sys_color_dark_surface_container = 0x7f05017a;
		public static final int m3_sys_color_dark_surface_container_high = 0x7f05017b;
		public static final int m3_sys_color_dark_surface_container_highest = 0x7f05017c;
		public static final int m3_sys_color_dark_surface_container_low = 0x7f05017d;
		public static final int m3_sys_color_dark_surface_container_lowest = 0x7f05017e;
		public static final int m3_sys_color_dark_surface_dim = 0x7f05017f;
		public static final int m3_sys_color_dark_surface_variant = 0x7f050180;
		public static final int m3_sys_color_dark_tertiary = 0x7f050181;
		public static final int m3_sys_color_dark_tertiary_container = 0x7f050182;
		public static final int m3_sys_color_dynamic_dark_background = 0x7f050183;
		public static final int m3_sys_color_dynamic_dark_error = 0x7f050184;
		public static final int m3_sys_color_dynamic_dark_error_container = 0x7f050185;
		public static final int m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f050186;
		public static final int m3_sys_color_dynamic_dark_inverse_primary = 0x7f050187;
		public static final int m3_sys_color_dynamic_dark_inverse_surface = 0x7f050188;
		public static final int m3_sys_color_dynamic_dark_on_background = 0x7f050189;
		public static final int m3_sys_color_dynamic_dark_on_error = 0x7f05018a;
		public static final int m3_sys_color_dynamic_dark_on_error_container = 0x7f05018b;
		public static final int m3_sys_color_dynamic_dark_on_primary = 0x7f05018c;
		public static final int m3_sys_color_dynamic_dark_on_primary_container = 0x7f05018d;
		public static final int m3_sys_color_dynamic_dark_on_secondary = 0x7f05018e;
		public static final int m3_sys_color_dynamic_dark_on_secondary_container = 0x7f05018f;
		public static final int m3_sys_color_dynamic_dark_on_surface = 0x7f050190;
		public static final int m3_sys_color_dynamic_dark_on_surface_variant = 0x7f050191;
		public static final int m3_sys_color_dynamic_dark_on_tertiary = 0x7f050192;
		public static final int m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f050193;
		public static final int m3_sys_color_dynamic_dark_outline = 0x7f050194;
		public static final int m3_sys_color_dynamic_dark_outline_variant = 0x7f050195;
		public static final int m3_sys_color_dynamic_dark_primary = 0x7f050196;
		public static final int m3_sys_color_dynamic_dark_primary_container = 0x7f050197;
		public static final int m3_sys_color_dynamic_dark_secondary = 0x7f050198;
		public static final int m3_sys_color_dynamic_dark_secondary_container = 0x7f050199;
		public static final int m3_sys_color_dynamic_dark_surface = 0x7f05019a;
		public static final int m3_sys_color_dynamic_dark_surface_bright = 0x7f05019b;
		public static final int m3_sys_color_dynamic_dark_surface_container = 0x7f05019c;
		public static final int m3_sys_color_dynamic_dark_surface_container_high = 0x7f05019d;
		public static final int m3_sys_color_dynamic_dark_surface_container_highest = 0x7f05019e;
		public static final int m3_sys_color_dynamic_dark_surface_container_low = 0x7f05019f;
		public static final int m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f0501a0;
		public static final int m3_sys_color_dynamic_dark_surface_dim = 0x7f0501a1;
		public static final int m3_sys_color_dynamic_dark_surface_variant = 0x7f0501a2;
		public static final int m3_sys_color_dynamic_dark_tertiary = 0x7f0501a3;
		public static final int m3_sys_color_dynamic_dark_tertiary_container = 0x7f0501a4;
		public static final int m3_sys_color_dynamic_light_background = 0x7f0501a5;
		public static final int m3_sys_color_dynamic_light_error = 0x7f0501a6;
		public static final int m3_sys_color_dynamic_light_error_container = 0x7f0501a7;
		public static final int m3_sys_color_dynamic_light_inverse_on_surface = 0x7f0501a8;
		public static final int m3_sys_color_dynamic_light_inverse_primary = 0x7f0501a9;
		public static final int m3_sys_color_dynamic_light_inverse_surface = 0x7f0501aa;
		public static final int m3_sys_color_dynamic_light_on_background = 0x7f0501ab;
		public static final int m3_sys_color_dynamic_light_on_error = 0x7f0501ac;
		public static final int m3_sys_color_dynamic_light_on_error_container = 0x7f0501ad;
		public static final int m3_sys_color_dynamic_light_on_primary = 0x7f0501ae;
		public static final int m3_sys_color_dynamic_light_on_primary_container = 0x7f0501af;
		public static final int m3_sys_color_dynamic_light_on_secondary = 0x7f0501b0;
		public static final int m3_sys_color_dynamic_light_on_secondary_container = 0x7f0501b1;
		public static final int m3_sys_color_dynamic_light_on_surface = 0x7f0501b2;
		public static final int m3_sys_color_dynamic_light_on_surface_variant = 0x7f0501b3;
		public static final int m3_sys_color_dynamic_light_on_tertiary = 0x7f0501b4;
		public static final int m3_sys_color_dynamic_light_on_tertiary_container = 0x7f0501b5;
		public static final int m3_sys_color_dynamic_light_outline = 0x7f0501b6;
		public static final int m3_sys_color_dynamic_light_outline_variant = 0x7f0501b7;
		public static final int m3_sys_color_dynamic_light_primary = 0x7f0501b8;
		public static final int m3_sys_color_dynamic_light_primary_container = 0x7f0501b9;
		public static final int m3_sys_color_dynamic_light_secondary = 0x7f0501ba;
		public static final int m3_sys_color_dynamic_light_secondary_container = 0x7f0501bb;
		public static final int m3_sys_color_dynamic_light_surface = 0x7f0501bc;
		public static final int m3_sys_color_dynamic_light_surface_bright = 0x7f0501bd;
		public static final int m3_sys_color_dynamic_light_surface_container = 0x7f0501be;
		public static final int m3_sys_color_dynamic_light_surface_container_high = 0x7f0501bf;
		public static final int m3_sys_color_dynamic_light_surface_container_highest = 0x7f0501c0;
		public static final int m3_sys_color_dynamic_light_surface_container_low = 0x7f0501c1;
		public static final int m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0501c2;
		public static final int m3_sys_color_dynamic_light_surface_dim = 0x7f0501c3;
		public static final int m3_sys_color_dynamic_light_surface_variant = 0x7f0501c4;
		public static final int m3_sys_color_dynamic_light_tertiary = 0x7f0501c5;
		public static final int m3_sys_color_dynamic_light_tertiary_container = 0x7f0501c6;
		public static final int m3_sys_color_dynamic_on_primary_fixed = 0x7f0501c7;
		public static final int m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0501c8;
		public static final int m3_sys_color_dynamic_on_secondary_fixed = 0x7f0501c9;
		public static final int m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0501ca;
		public static final int m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0501cb;
		public static final int m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0501cc;
		public static final int m3_sys_color_dynamic_primary_fixed = 0x7f0501cd;
		public static final int m3_sys_color_dynamic_primary_fixed_dim = 0x7f0501ce;
		public static final int m3_sys_color_dynamic_secondary_fixed = 0x7f0501cf;
		public static final int m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0501d0;
		public static final int m3_sys_color_dynamic_tertiary_fixed = 0x7f0501d1;
		public static final int m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f0501d2;
		public static final int m3_sys_color_light_background = 0x7f0501d3;
		public static final int m3_sys_color_light_error = 0x7f0501d4;
		public static final int m3_sys_color_light_error_container = 0x7f0501d5;
		public static final int m3_sys_color_light_inverse_on_surface = 0x7f0501d6;
		public static final int m3_sys_color_light_inverse_primary = 0x7f0501d7;
		public static final int m3_sys_color_light_inverse_surface = 0x7f0501d8;
		public static final int m3_sys_color_light_on_background = 0x7f0501d9;
		public static final int m3_sys_color_light_on_error = 0x7f0501da;
		public static final int m3_sys_color_light_on_error_container = 0x7f0501db;
		public static final int m3_sys_color_light_on_primary = 0x7f0501dc;
		public static final int m3_sys_color_light_on_primary_container = 0x7f0501dd;
		public static final int m3_sys_color_light_on_secondary = 0x7f0501de;
		public static final int m3_sys_color_light_on_secondary_container = 0x7f0501df;
		public static final int m3_sys_color_light_on_surface = 0x7f0501e0;
		public static final int m3_sys_color_light_on_surface_variant = 0x7f0501e1;
		public static final int m3_sys_color_light_on_tertiary = 0x7f0501e2;
		public static final int m3_sys_color_light_on_tertiary_container = 0x7f0501e3;
		public static final int m3_sys_color_light_outline = 0x7f0501e4;
		public static final int m3_sys_color_light_outline_variant = 0x7f0501e5;
		public static final int m3_sys_color_light_primary = 0x7f0501e6;
		public static final int m3_sys_color_light_primary_container = 0x7f0501e7;
		public static final int m3_sys_color_light_secondary = 0x7f0501e8;
		public static final int m3_sys_color_light_secondary_container = 0x7f0501e9;
		public static final int m3_sys_color_light_surface = 0x7f0501ea;
		public static final int m3_sys_color_light_surface_bright = 0x7f0501eb;
		public static final int m3_sys_color_light_surface_container = 0x7f0501ec;
		public static final int m3_sys_color_light_surface_container_high = 0x7f0501ed;
		public static final int m3_sys_color_light_surface_container_highest = 0x7f0501ee;
		public static final int m3_sys_color_light_surface_container_low = 0x7f0501ef;
		public static final int m3_sys_color_light_surface_container_lowest = 0x7f0501f0;
		public static final int m3_sys_color_light_surface_dim = 0x7f0501f1;
		public static final int m3_sys_color_light_surface_variant = 0x7f0501f2;
		public static final int m3_sys_color_light_tertiary = 0x7f0501f3;
		public static final int m3_sys_color_light_tertiary_container = 0x7f0501f4;
		public static final int m3_sys_color_on_primary_fixed = 0x7f0501f5;
		public static final int m3_sys_color_on_primary_fixed_variant = 0x7f0501f6;
		public static final int m3_sys_color_on_secondary_fixed = 0x7f0501f7;
		public static final int m3_sys_color_on_secondary_fixed_variant = 0x7f0501f8;
		public static final int m3_sys_color_on_tertiary_fixed = 0x7f0501f9;
		public static final int m3_sys_color_on_tertiary_fixed_variant = 0x7f0501fa;
		public static final int m3_sys_color_primary_fixed = 0x7f0501fb;
		public static final int m3_sys_color_primary_fixed_dim = 0x7f0501fc;
		public static final int m3_sys_color_secondary_fixed = 0x7f0501fd;
		public static final int m3_sys_color_secondary_fixed_dim = 0x7f0501fe;
		public static final int m3_sys_color_tertiary_fixed = 0x7f0501ff;
		public static final int m3_sys_color_tertiary_fixed_dim = 0x7f050200;
		public static final int m3_tabs_icon_color = 0x7f050201;
		public static final int m3_tabs_icon_color_secondary = 0x7f050202;
		public static final int m3_tabs_ripple_color = 0x7f050203;
		public static final int m3_tabs_ripple_color_secondary = 0x7f050204;
		public static final int m3_tabs_text_color = 0x7f050205;
		public static final int m3_tabs_text_color_secondary = 0x7f050206;
		public static final int m3_text_button_background_color_selector = 0x7f050207;
		public static final int m3_text_button_foreground_color_selector = 0x7f050208;
		public static final int m3_text_button_ripple_color_selector = 0x7f050209;
		public static final int m3_textfield_filled_background_color = 0x7f05020a;
		public static final int m3_textfield_indicator_text_color = 0x7f05020b;
		public static final int m3_textfield_input_text_color = 0x7f05020c;
		public static final int m3_textfield_label_color = 0x7f05020d;
		public static final int m3_textfield_stroke_color = 0x7f05020e;
		public static final int m3_timepicker_button_background_color = 0x7f05020f;
		public static final int m3_timepicker_button_ripple_color = 0x7f050210;
		public static final int m3_timepicker_button_text_color = 0x7f050211;
		public static final int m3_timepicker_clock_text_color = 0x7f050212;
		public static final int m3_timepicker_display_background_color = 0x7f050213;
		public static final int m3_timepicker_display_ripple_color = 0x7f050214;
		public static final int m3_timepicker_display_text_color = 0x7f050215;
		public static final int m3_timepicker_secondary_text_button_ripple_color = 0x7f050216;
		public static final int m3_timepicker_secondary_text_button_text_color = 0x7f050217;
		public static final int m3_timepicker_time_input_stroke_color = 0x7f050218;
		public static final int m3_tonal_button_ripple_color_selector = 0x7f050219;
		public static final int material_blue_grey_800 = 0x7f05021a;
		public static final int material_blue_grey_900 = 0x7f05021b;
		public static final int material_blue_grey_950 = 0x7f05021c;
		public static final int material_cursor_color = 0x7f05021d;
		public static final int material_deep_teal_200 = 0x7f05021e;
		public static final int material_deep_teal_500 = 0x7f05021f;
		public static final int material_divider_color = 0x7f050220;
		public static final int material_dynamic_color_dark_error = 0x7f050221;
		public static final int material_dynamic_color_dark_error_container = 0x7f050222;
		public static final int material_dynamic_color_dark_on_error = 0x7f050223;
		public static final int material_dynamic_color_dark_on_error_container = 0x7f050224;
		public static final int material_dynamic_color_light_error = 0x7f050225;
		public static final int material_dynamic_color_light_error_container = 0x7f050226;
		public static final int material_dynamic_color_light_on_error = 0x7f050227;
		public static final int material_dynamic_color_light_on_error_container = 0x7f050228;
		public static final int material_dynamic_neutral0 = 0x7f050229;
		public static final int material_dynamic_neutral10 = 0x7f05022a;
		public static final int material_dynamic_neutral100 = 0x7f05022b;
		public static final int material_dynamic_neutral20 = 0x7f05022c;
		public static final int material_dynamic_neutral30 = 0x7f05022d;
		public static final int material_dynamic_neutral40 = 0x7f05022e;
		public static final int material_dynamic_neutral50 = 0x7f05022f;
		public static final int material_dynamic_neutral60 = 0x7f050230;
		public static final int material_dynamic_neutral70 = 0x7f050231;
		public static final int material_dynamic_neutral80 = 0x7f050232;
		public static final int material_dynamic_neutral90 = 0x7f050233;
		public static final int material_dynamic_neutral95 = 0x7f050234;
		public static final int material_dynamic_neutral99 = 0x7f050235;
		public static final int material_dynamic_neutral_variant0 = 0x7f050236;
		public static final int material_dynamic_neutral_variant10 = 0x7f050237;
		public static final int material_dynamic_neutral_variant100 = 0x7f050238;
		public static final int material_dynamic_neutral_variant20 = 0x7f050239;
		public static final int material_dynamic_neutral_variant30 = 0x7f05023a;
		public static final int material_dynamic_neutral_variant40 = 0x7f05023b;
		public static final int material_dynamic_neutral_variant50 = 0x7f05023c;
		public static final int material_dynamic_neutral_variant60 = 0x7f05023d;
		public static final int material_dynamic_neutral_variant70 = 0x7f05023e;
		public static final int material_dynamic_neutral_variant80 = 0x7f05023f;
		public static final int material_dynamic_neutral_variant90 = 0x7f050240;
		public static final int material_dynamic_neutral_variant95 = 0x7f050241;
		public static final int material_dynamic_neutral_variant99 = 0x7f050242;
		public static final int material_dynamic_primary0 = 0x7f050243;
		public static final int material_dynamic_primary10 = 0x7f050244;
		public static final int material_dynamic_primary100 = 0x7f050245;
		public static final int material_dynamic_primary20 = 0x7f050246;
		public static final int material_dynamic_primary30 = 0x7f050247;
		public static final int material_dynamic_primary40 = 0x7f050248;
		public static final int material_dynamic_primary50 = 0x7f050249;
		public static final int material_dynamic_primary60 = 0x7f05024a;
		public static final int material_dynamic_primary70 = 0x7f05024b;
		public static final int material_dynamic_primary80 = 0x7f05024c;
		public static final int material_dynamic_primary90 = 0x7f05024d;
		public static final int material_dynamic_primary95 = 0x7f05024e;
		public static final int material_dynamic_primary99 = 0x7f05024f;
		public static final int material_dynamic_secondary0 = 0x7f050250;
		public static final int material_dynamic_secondary10 = 0x7f050251;
		public static final int material_dynamic_secondary100 = 0x7f050252;
		public static final int material_dynamic_secondary20 = 0x7f050253;
		public static final int material_dynamic_secondary30 = 0x7f050254;
		public static final int material_dynamic_secondary40 = 0x7f050255;
		public static final int material_dynamic_secondary50 = 0x7f050256;
		public static final int material_dynamic_secondary60 = 0x7f050257;
		public static final int material_dynamic_secondary70 = 0x7f050258;
		public static final int material_dynamic_secondary80 = 0x7f050259;
		public static final int material_dynamic_secondary90 = 0x7f05025a;
		public static final int material_dynamic_secondary95 = 0x7f05025b;
		public static final int material_dynamic_secondary99 = 0x7f05025c;
		public static final int material_dynamic_tertiary0 = 0x7f05025d;
		public static final int material_dynamic_tertiary10 = 0x7f05025e;
		public static final int material_dynamic_tertiary100 = 0x7f05025f;
		public static final int material_dynamic_tertiary20 = 0x7f050260;
		public static final int material_dynamic_tertiary30 = 0x7f050261;
		public static final int material_dynamic_tertiary40 = 0x7f050262;
		public static final int material_dynamic_tertiary50 = 0x7f050263;
		public static final int material_dynamic_tertiary60 = 0x7f050264;
		public static final int material_dynamic_tertiary70 = 0x7f050265;
		public static final int material_dynamic_tertiary80 = 0x7f050266;
		public static final int material_dynamic_tertiary90 = 0x7f050267;
		public static final int material_dynamic_tertiary95 = 0x7f050268;
		public static final int material_dynamic_tertiary99 = 0x7f050269;
		public static final int material_grey_100 = 0x7f05026a;
		public static final int material_grey_300 = 0x7f05026b;
		public static final int material_grey_50 = 0x7f05026c;
		public static final int material_grey_600 = 0x7f05026d;
		public static final int material_grey_800 = 0x7f05026e;
		public static final int material_grey_850 = 0x7f05026f;
		public static final int material_grey_900 = 0x7f050270;
		public static final int material_harmonized_color_error = 0x7f050271;
		public static final int material_harmonized_color_error_container = 0x7f050272;
		public static final int material_harmonized_color_on_error = 0x7f050273;
		public static final int material_harmonized_color_on_error_container = 0x7f050274;
		public static final int material_on_background_disabled = 0x7f050275;
		public static final int material_on_background_emphasis_high_type = 0x7f050276;
		public static final int material_on_background_emphasis_medium = 0x7f050277;
		public static final int material_on_primary_disabled = 0x7f050278;
		public static final int material_on_primary_emphasis_high_type = 0x7f050279;
		public static final int material_on_primary_emphasis_medium = 0x7f05027a;
		public static final int material_on_surface_disabled = 0x7f05027b;
		public static final int material_on_surface_emphasis_high_type = 0x7f05027c;
		public static final int material_on_surface_emphasis_medium = 0x7f05027d;
		public static final int material_on_surface_stroke = 0x7f05027e;
		public static final int material_personalized__highlighted_text = 0x7f05027f;
		public static final int material_personalized__highlighted_text_inverse = 0x7f050280;
		public static final int material_personalized_color_background = 0x7f050281;
		public static final int material_personalized_color_control_activated = 0x7f050282;
		public static final int material_personalized_color_control_highlight = 0x7f050283;
		public static final int material_personalized_color_control_normal = 0x7f050284;
		public static final int material_personalized_color_error = 0x7f050285;
		public static final int material_personalized_color_error_container = 0x7f050286;
		public static final int material_personalized_color_on_background = 0x7f050287;
		public static final int material_personalized_color_on_error = 0x7f050288;
		public static final int material_personalized_color_on_error_container = 0x7f050289;
		public static final int material_personalized_color_on_primary = 0x7f05028a;
		public static final int material_personalized_color_on_primary_container = 0x7f05028b;
		public static final int material_personalized_color_on_secondary = 0x7f05028c;
		public static final int material_personalized_color_on_secondary_container = 0x7f05028d;
		public static final int material_personalized_color_on_surface = 0x7f05028e;
		public static final int material_personalized_color_on_surface_inverse = 0x7f05028f;
		public static final int material_personalized_color_on_surface_variant = 0x7f050290;
		public static final int material_personalized_color_on_tertiary = 0x7f050291;
		public static final int material_personalized_color_on_tertiary_container = 0x7f050292;
		public static final int material_personalized_color_outline = 0x7f050293;
		public static final int material_personalized_color_outline_variant = 0x7f050294;
		public static final int material_personalized_color_primary = 0x7f050295;
		public static final int material_personalized_color_primary_container = 0x7f050296;
		public static final int material_personalized_color_primary_inverse = 0x7f050297;
		public static final int material_personalized_color_primary_text = 0x7f050298;
		public static final int material_personalized_color_primary_text_inverse = 0x7f050299;
		public static final int material_personalized_color_secondary = 0x7f05029a;
		public static final int material_personalized_color_secondary_container = 0x7f05029b;
		public static final int material_personalized_color_secondary_text = 0x7f05029c;
		public static final int material_personalized_color_secondary_text_inverse = 0x7f05029d;
		public static final int material_personalized_color_surface = 0x7f05029e;
		public static final int material_personalized_color_surface_bright = 0x7f05029f;
		public static final int material_personalized_color_surface_container = 0x7f0502a0;
		public static final int material_personalized_color_surface_container_high = 0x7f0502a1;
		public static final int material_personalized_color_surface_container_highest = 0x7f0502a2;
		public static final int material_personalized_color_surface_container_low = 0x7f0502a3;
		public static final int material_personalized_color_surface_container_lowest = 0x7f0502a4;
		public static final int material_personalized_color_surface_dim = 0x7f0502a5;
		public static final int material_personalized_color_surface_inverse = 0x7f0502a6;
		public static final int material_personalized_color_surface_variant = 0x7f0502a7;
		public static final int material_personalized_color_tertiary = 0x7f0502a8;
		public static final int material_personalized_color_tertiary_container = 0x7f0502a9;
		public static final int material_personalized_color_text_hint_foreground_inverse = 0x7f0502aa;
		public static final int material_personalized_color_text_primary_inverse = 0x7f0502ab;
		public static final int material_personalized_color_text_primary_inverse_disable_only = 0x7f0502ac;
		public static final int material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f0502ad;
		public static final int material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f0502ae;
		public static final int material_personalized_hint_foreground = 0x7f0502af;
		public static final int material_personalized_hint_foreground_inverse = 0x7f0502b0;
		public static final int material_personalized_primary_inverse_text_disable_only = 0x7f0502b1;
		public static final int material_personalized_primary_text_disable_only = 0x7f0502b2;
		public static final int material_slider_active_tick_marks_color = 0x7f0502b3;
		public static final int material_slider_active_track_color = 0x7f0502b4;
		public static final int material_slider_halo_color = 0x7f0502b5;
		public static final int material_slider_inactive_tick_marks_color = 0x7f0502b6;
		public static final int material_slider_inactive_track_color = 0x7f0502b7;
		public static final int material_slider_thumb_color = 0x7f0502b8;
		public static final int material_timepicker_button_background = 0x7f0502b9;
		public static final int material_timepicker_button_stroke = 0x7f0502ba;
		public static final int material_timepicker_clock_text_color = 0x7f0502bb;
		public static final int material_timepicker_clockface = 0x7f0502bc;
		public static final int material_timepicker_modebutton_tint = 0x7f0502bd;
		public static final int mtrl_btn_bg_color_selector = 0x7f0502bf;
		public static final int mtrl_btn_ripple_color = 0x7f0502c0;
		public static final int mtrl_btn_stroke_color_selector = 0x7f0502c1;
		public static final int mtrl_btn_text_btn_bg_color_selector = 0x7f0502c2;
		public static final int mtrl_btn_text_btn_ripple_color = 0x7f0502c3;
		public static final int mtrl_btn_text_color_disabled = 0x7f0502c4;
		public static final int mtrl_btn_text_color_selector = 0x7f0502c5;
		public static final int mtrl_btn_transparent_bg_color = 0x7f0502c6;
		public static final int mtrl_calendar_item_stroke_color = 0x7f0502c7;
		public static final int mtrl_calendar_selected_range = 0x7f0502c8;
		public static final int mtrl_card_view_foreground = 0x7f0502c9;
		public static final int mtrl_card_view_ripple = 0x7f0502ca;
		public static final int mtrl_chip_background_color = 0x7f0502cb;
		public static final int mtrl_chip_close_icon_tint = 0x7f0502cc;
		public static final int mtrl_chip_surface_color = 0x7f0502cd;
		public static final int mtrl_chip_text_color = 0x7f0502ce;
		public static final int mtrl_choice_chip_background_color = 0x7f0502cf;
		public static final int mtrl_choice_chip_ripple_color = 0x7f0502d0;
		public static final int mtrl_choice_chip_text_color = 0x7f0502d1;
		public static final int mtrl_error = 0x7f0502d2;
		public static final int mtrl_fab_bg_color_selector = 0x7f0502d3;
		public static final int mtrl_fab_icon_text_color_selector = 0x7f0502d4;
		public static final int mtrl_fab_ripple_color = 0x7f0502d5;
		public static final int mtrl_filled_background_color = 0x7f0502d6;
		public static final int mtrl_filled_icon_tint = 0x7f0502d7;
		public static final int mtrl_filled_stroke_color = 0x7f0502d8;
		public static final int mtrl_indicator_text_color = 0x7f0502d9;
		public static final int mtrl_navigation_bar_colored_item_tint = 0x7f0502da;
		public static final int mtrl_navigation_bar_colored_ripple_color = 0x7f0502db;
		public static final int mtrl_navigation_bar_item_tint = 0x7f0502dc;
		public static final int mtrl_navigation_bar_ripple_color = 0x7f0502dd;
		public static final int mtrl_navigation_item_background_color = 0x7f0502de;
		public static final int mtrl_navigation_item_icon_tint = 0x7f0502df;
		public static final int mtrl_navigation_item_text_color = 0x7f0502e0;
		public static final int mtrl_on_primary_text_btn_text_color_selector = 0x7f0502e1;
		public static final int mtrl_on_surface_ripple_color = 0x7f0502e2;
		public static final int mtrl_outlined_icon_tint = 0x7f0502e3;
		public static final int mtrl_outlined_stroke_color = 0x7f0502e4;
		public static final int mtrl_popupmenu_overlay_color = 0x7f0502e5;
		public static final int mtrl_scrim_color = 0x7f0502e6;
		public static final int mtrl_switch_thumb_icon_tint = 0x7f0502e7;
		public static final int mtrl_switch_thumb_tint = 0x7f0502e8;
		public static final int mtrl_switch_track_decoration_tint = 0x7f0502e9;
		public static final int mtrl_switch_track_tint = 0x7f0502ea;
		public static final int mtrl_tabs_colored_ripple_color = 0x7f0502eb;
		public static final int mtrl_tabs_icon_color_selector = 0x7f0502ec;
		public static final int mtrl_tabs_icon_color_selector_colored = 0x7f0502ed;
		public static final int mtrl_tabs_legacy_text_color_selector = 0x7f0502ee;
		public static final int mtrl_tabs_ripple_color = 0x7f0502ef;
		public static final int mtrl_text_btn_text_color_selector = 0x7f0502f0;
		public static final int mtrl_textinput_default_box_stroke_color = 0x7f0502f1;
		public static final int mtrl_textinput_disabled_color = 0x7f0502f2;
		public static final int mtrl_textinput_filled_box_default_background_color = 0x7f0502f3;
		public static final int mtrl_textinput_focused_box_stroke_color = 0x7f0502f4;
		public static final int mtrl_textinput_hovered_box_stroke_color = 0x7f0502f5;
		public static final int notification_action_color_filter = 0x7f0502f6;
		public static final int notification_icon_bg_color = 0x7f0502f7;
		public static final int primary_dark_material_dark = 0x7f0502f8;
		public static final int primary_dark_material_light = 0x7f0502f9;
		public static final int primary_material_dark = 0x7f0502fa;
		public static final int primary_material_light = 0x7f0502fb;
		public static final int primary_text_default_material_dark = 0x7f0502fc;
		public static final int primary_text_default_material_light = 0x7f0502fd;
		public static final int primary_text_disabled_material_dark = 0x7f0502fe;
		public static final int primary_text_disabled_material_light = 0x7f0502ff;
		public static final int ripple_material_dark = 0x7f050300;
		public static final int ripple_material_light = 0x7f050301;
		public static final int secondary_text_default_material_dark = 0x7f050302;
		public static final int secondary_text_default_material_light = 0x7f050303;
		public static final int secondary_text_disabled_material_dark = 0x7f050304;
		public static final int secondary_text_disabled_material_light = 0x7f050305;
		public static final int switch_thumb_disabled_material_dark = 0x7f050306;
		public static final int switch_thumb_disabled_material_light = 0x7f050307;
		public static final int switch_thumb_material_dark = 0x7f050308;
		public static final int switch_thumb_material_light = 0x7f050309;
		public static final int switch_thumb_normal_material_dark = 0x7f05030a;
		public static final int switch_thumb_normal_material_light = 0x7f05030b;
		public static final int tooltip_background_dark = 0x7f05030c;
		public static final int tooltip_background_light = 0x7f05030d;
	}
	public static final class dimen {
		public static final int abc_action_bar_content_inset_material = 0x7f060000;
		public static final int abc_action_bar_content_inset_with_nav = 0x7f060001;
		public static final int abc_action_bar_default_height_material = 0x7f060002;
		public static final int abc_action_bar_default_padding_end_material = 0x7f060003;
		public static final int abc_action_bar_default_padding_start_material = 0x7f060004;
		public static final int abc_action_bar_elevation_material = 0x7f060005;
		public static final int abc_action_bar_icon_vertical_padding_material = 0x7f060006;
		public static final int abc_action_bar_overflow_padding_end_material = 0x7f060007;
		public static final int abc_action_bar_overflow_padding_start_material = 0x7f060008;
		public static final int abc_action_bar_stacked_max_height = 0x7f060009;
		public static final int abc_action_bar_stacked_tab_max_width = 0x7f06000a;
		public static final int abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b;
		public static final int abc_action_bar_subtitle_top_margin_material = 0x7f06000c;
		public static final int abc_action_button_min_height_material = 0x7f06000d;
		public static final int abc_action_button_min_width_material = 0x7f06000e;
		public static final int abc_action_button_min_width_overflow_material = 0x7f06000f;
		public static final int abc_alert_dialog_button_bar_height = 0x7f060010;
		public static final int abc_alert_dialog_button_dimen = 0x7f060011;
		public static final int abc_button_inset_horizontal_material = 0x7f060012;
		public static final int abc_button_inset_vertical_material = 0x7f060013;
		public static final int abc_button_padding_horizontal_material = 0x7f060014;
		public static final int abc_button_padding_vertical_material = 0x7f060015;
		public static final int abc_cascading_menus_min_smallest_width = 0x7f060016;
		public static final int abc_config_prefDialogWidth = 0x7f060017;
		public static final int abc_control_corner_material = 0x7f060018;
		public static final int abc_control_inset_material = 0x7f060019;
		public static final int abc_control_padding_material = 0x7f06001a;
		public static final int abc_dialog_corner_radius_material = 0x7f06001b;
		public static final int abc_dialog_fixed_height_major = 0x7f06001c;
		public static final int abc_dialog_fixed_height_minor = 0x7f06001d;
		public static final int abc_dialog_fixed_width_major = 0x7f06001e;
		public static final int abc_dialog_fixed_width_minor = 0x7f06001f;
		public static final int abc_dialog_list_padding_bottom_no_buttons = 0x7f060020;
		public static final int abc_dialog_list_padding_top_no_title = 0x7f060021;
		public static final int abc_dialog_min_width_major = 0x7f060022;
		public static final int abc_dialog_min_width_minor = 0x7f060023;
		public static final int abc_dialog_padding_material = 0x7f060024;
		public static final int abc_dialog_padding_top_material = 0x7f060025;
		public static final int abc_dialog_title_divider_material = 0x7f060026;
		public static final int abc_disabled_alpha_material_dark = 0x7f060027;
		public static final int abc_disabled_alpha_material_light = 0x7f060028;
		public static final int abc_dropdownitem_icon_width = 0x7f060029;
		public static final int abc_dropdownitem_text_padding_left = 0x7f06002a;
		public static final int abc_dropdownitem_text_padding_right = 0x7f06002b;
		public static final int abc_edit_text_inset_bottom_material = 0x7f06002c;
		public static final int abc_edit_text_inset_horizontal_material = 0x7f06002d;
		public static final int abc_edit_text_inset_top_material = 0x7f06002e;
		public static final int abc_floating_window_z = 0x7f06002f;
		public static final int abc_list_item_height_large_material = 0x7f060030;
		public static final int abc_list_item_height_material = 0x7f060031;
		public static final int abc_list_item_height_small_material = 0x7f060032;
		public static final int abc_list_item_padding_horizontal_material = 0x7f060033;
		public static final int abc_panel_menu_list_width = 0x7f060034;
		public static final int abc_progress_bar_height_material = 0x7f060035;
		public static final int abc_search_view_preferred_height = 0x7f060036;
		public static final int abc_search_view_preferred_width = 0x7f060037;
		public static final int abc_seekbar_track_background_height_material = 0x7f060038;
		public static final int abc_seekbar_track_progress_height_material = 0x7f060039;
		public static final int abc_select_dialog_padding_start_material = 0x7f06003a;
		public static final int abc_star_big = 0x7f06003b;
		public static final int abc_star_medium = 0x7f06003c;
		public static final int abc_star_small = 0x7f06003d;
		public static final int abc_switch_padding = 0x7f06003e;
		public static final int abc_text_size_body_1_material = 0x7f06003f;
		public static final int abc_text_size_body_2_material = 0x7f060040;
		public static final int abc_text_size_button_material = 0x7f060041;
		public static final int abc_text_size_caption_material = 0x7f060042;
		public static final int abc_text_size_display_1_material = 0x7f060043;
		public static final int abc_text_size_display_2_material = 0x7f060044;
		public static final int abc_text_size_display_3_material = 0x7f060045;
		public static final int abc_text_size_display_4_material = 0x7f060046;
		public static final int abc_text_size_headline_material = 0x7f060047;
		public static final int abc_text_size_large_material = 0x7f060048;
		public static final int abc_text_size_medium_material = 0x7f060049;
		public static final int abc_text_size_menu_header_material = 0x7f06004a;
		public static final int abc_text_size_menu_material = 0x7f06004b;
		public static final int abc_text_size_small_material = 0x7f06004c;
		public static final int abc_text_size_subhead_material = 0x7f06004d;
		public static final int abc_text_size_subtitle_material_toolbar = 0x7f06004e;
		public static final int abc_text_size_title_material = 0x7f06004f;
		public static final int abc_text_size_title_material_toolbar = 0x7f060050;
		public static final int appcompat_dialog_background_inset = 0x7f060051;
		public static final int cardview_compat_inset_shadow = 0x7f060054;
		public static final int cardview_default_elevation = 0x7f060055;
		public static final int cardview_default_radius = 0x7f060056;
		public static final int clock_face_margin_start = 0x7f060057;
		public static final int compat_button_inset_horizontal_material = 0x7f060058;
		public static final int compat_button_inset_vertical_material = 0x7f060059;
		public static final int compat_button_padding_horizontal_material = 0x7f06005a;
		public static final int compat_button_padding_vertical_material = 0x7f06005b;
		public static final int compat_control_corner_material = 0x7f06005c;
		public static final int compat_notification_large_icon_max_height = 0x7f06005d;
		public static final int compat_notification_large_icon_max_width = 0x7f06005e;
		public static final int def_drawer_elevation = 0x7f06005f;
		public static final int design_appbar_elevation = 0x7f060060;
		public static final int design_bottom_navigation_active_item_max_width = 0x7f060061;
		public static final int design_bottom_navigation_active_item_min_width = 0x7f060062;
		public static final int design_bottom_navigation_active_text_size = 0x7f060063;
		public static final int design_bottom_navigation_elevation = 0x7f060064;
		public static final int design_bottom_navigation_height = 0x7f060065;
		public static final int design_bottom_navigation_icon_size = 0x7f060066;
		public static final int design_bottom_navigation_item_max_width = 0x7f060067;
		public static final int design_bottom_navigation_item_min_width = 0x7f060068;
		public static final int design_bottom_navigation_label_padding = 0x7f060069;
		public static final int design_bottom_navigation_margin = 0x7f06006a;
		public static final int design_bottom_navigation_shadow_height = 0x7f06006b;
		public static final int design_bottom_navigation_text_size = 0x7f06006c;
		public static final int design_bottom_sheet_elevation = 0x7f06006d;
		public static final int design_bottom_sheet_modal_elevation = 0x7f06006e;
		public static final int design_bottom_sheet_peek_height_min = 0x7f06006f;
		public static final int design_fab_border_width = 0x7f060070;
		public static final int design_fab_elevation = 0x7f060071;
		public static final int design_fab_image_size = 0x7f060072;
		public static final int design_fab_size_mini = 0x7f060073;
		public static final int design_fab_size_normal = 0x7f060074;
		public static final int design_fab_translation_z_hovered_focused = 0x7f060075;
		public static final int design_fab_translation_z_pressed = 0x7f060076;
		public static final int design_navigation_elevation = 0x7f060077;
		public static final int design_navigation_icon_padding = 0x7f060078;
		public static final int design_navigation_icon_size = 0x7f060079;
		public static final int design_navigation_item_horizontal_padding = 0x7f06007a;
		public static final int design_navigation_item_icon_padding = 0x7f06007b;
		public static final int design_navigation_item_vertical_padding = 0x7f06007c;
		public static final int design_navigation_max_width = 0x7f06007d;
		public static final int design_navigation_padding_bottom = 0x7f06007e;
		public static final int design_navigation_separator_vertical_padding = 0x7f06007f;
		public static final int design_snackbar_action_inline_max_width = 0x7f060080;
		public static final int design_snackbar_action_text_color_alpha = 0x7f060081;
		public static final int design_snackbar_background_corner_radius = 0x7f060082;
		public static final int design_snackbar_elevation = 0x7f060083;
		public static final int design_snackbar_extra_spacing_horizontal = 0x7f060084;
		public static final int design_snackbar_max_width = 0x7f060085;
		public static final int design_snackbar_min_width = 0x7f060086;
		public static final int design_snackbar_padding_horizontal = 0x7f060087;
		public static final int design_snackbar_padding_vertical = 0x7f060088;
		public static final int design_snackbar_padding_vertical_2lines = 0x7f060089;
		public static final int design_snackbar_text_size = 0x7f06008a;
		public static final int design_tab_max_width = 0x7f06008b;
		public static final int design_tab_scrollable_min_width = 0x7f06008c;
		public static final int design_tab_text_size = 0x7f06008d;
		public static final int design_tab_text_size_2line = 0x7f06008e;
		public static final int design_textinput_caption_translate_y = 0x7f06008f;
		public static final int disabled_alpha_material_dark = 0x7f060090;
		public static final int disabled_alpha_material_light = 0x7f060091;
		public static final int fastscroll_default_thickness = 0x7f060092;
		public static final int fastscroll_margin = 0x7f060093;
		public static final int fastscroll_minimum_range = 0x7f060094;
		public static final int highlight_alpha_material_colored = 0x7f060095;
		public static final int highlight_alpha_material_dark = 0x7f060096;
		public static final int highlight_alpha_material_light = 0x7f060097;
		public static final int hint_alpha_material_dark = 0x7f060098;
		public static final int hint_alpha_material_light = 0x7f060099;
		public static final int hint_pressed_alpha_material_dark = 0x7f06009a;
		public static final int hint_pressed_alpha_material_light = 0x7f06009b;
		public static final int item_touch_helper_max_drag_scroll_per_frame = 0x7f06009c;
		public static final int item_touch_helper_swipe_escape_max_velocity = 0x7f06009d;
		public static final int item_touch_helper_swipe_escape_velocity = 0x7f06009e;
		public static final int m3_alert_dialog_action_bottom_padding = 0x7f06009f;
		public static final int m3_alert_dialog_action_top_padding = 0x7f0600a0;
		public static final int m3_alert_dialog_corner_size = 0x7f0600a1;
		public static final int m3_alert_dialog_elevation = 0x7f0600a2;
		public static final int m3_alert_dialog_icon_margin = 0x7f0600a3;
		public static final int m3_alert_dialog_icon_size = 0x7f0600a4;
		public static final int m3_alert_dialog_title_bottom_margin = 0x7f0600a5;
		public static final int m3_appbar_expanded_title_margin_bottom = 0x7f0600a6;
		public static final int m3_appbar_expanded_title_margin_horizontal = 0x7f0600a7;
		public static final int m3_appbar_scrim_height_trigger = 0x7f0600a8;
		public static final int m3_appbar_scrim_height_trigger_large = 0x7f0600a9;
		public static final int m3_appbar_scrim_height_trigger_medium = 0x7f0600aa;
		public static final int m3_appbar_size_compact = 0x7f0600ab;
		public static final int m3_appbar_size_large = 0x7f0600ac;
		public static final int m3_appbar_size_medium = 0x7f0600ad;
		public static final int m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0600ae;
		public static final int m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0600af;
		public static final int m3_back_progress_main_container_max_translation_y = 0x7f0600b0;
		public static final int m3_back_progress_main_container_min_edge_gap = 0x7f0600b1;
		public static final int m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0600b2;
		public static final int m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0600b3;
		public static final int m3_back_progress_side_container_max_scale_y_distance = 0x7f0600b4;
		public static final int m3_badge_horizontal_offset = 0x7f0600b5;
		public static final int m3_badge_offset = 0x7f0600b6;
		public static final int m3_badge_size = 0x7f0600b7;
		public static final int m3_badge_vertical_offset = 0x7f0600b8;
		public static final int m3_badge_with_text_horizontal_offset = 0x7f0600b9;
		public static final int m3_badge_with_text_offset = 0x7f0600ba;
		public static final int m3_badge_with_text_size = 0x7f0600bb;
		public static final int m3_badge_with_text_vertical_offset = 0x7f0600bc;
		public static final int m3_badge_with_text_vertical_padding = 0x7f0600bd;
		public static final int m3_bottom_nav_item_active_indicator_height = 0x7f0600be;
		public static final int m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0600bf;
		public static final int m3_bottom_nav_item_active_indicator_width = 0x7f0600c0;
		public static final int m3_bottom_nav_item_padding_bottom = 0x7f0600c1;
		public static final int m3_bottom_nav_item_padding_top = 0x7f0600c2;
		public static final int m3_bottom_nav_min_height = 0x7f0600c3;
		public static final int m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0600c4;
		public static final int m3_bottom_sheet_elevation = 0x7f0600c5;
		public static final int m3_bottom_sheet_modal_elevation = 0x7f0600c6;
		public static final int m3_bottomappbar_fab_cradle_margin = 0x7f0600c7;
		public static final int m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0600c8;
		public static final int m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0600c9;
		public static final int m3_bottomappbar_fab_end_margin = 0x7f0600ca;
		public static final int m3_bottomappbar_height = 0x7f0600cb;
		public static final int m3_bottomappbar_horizontal_padding = 0x7f0600cc;
		public static final int m3_btn_dialog_btn_min_width = 0x7f0600cd;
		public static final int m3_btn_dialog_btn_spacing = 0x7f0600ce;
		public static final int m3_btn_disabled_elevation = 0x7f0600cf;
		public static final int m3_btn_disabled_translation_z = 0x7f0600d0;
		public static final int m3_btn_elevated_btn_elevation = 0x7f0600d1;
		public static final int m3_btn_elevation = 0x7f0600d2;
		public static final int m3_btn_icon_btn_padding_left = 0x7f0600d3;
		public static final int m3_btn_icon_btn_padding_right = 0x7f0600d4;
		public static final int m3_btn_icon_only_default_padding = 0x7f0600d5;
		public static final int m3_btn_icon_only_default_size = 0x7f0600d6;
		public static final int m3_btn_icon_only_icon_padding = 0x7f0600d7;
		public static final int m3_btn_icon_only_min_width = 0x7f0600d8;
		public static final int m3_btn_inset = 0x7f0600d9;
		public static final int m3_btn_max_width = 0x7f0600da;
		public static final int m3_btn_padding_bottom = 0x7f0600db;
		public static final int m3_btn_padding_left = 0x7f0600dc;
		public static final int m3_btn_padding_right = 0x7f0600dd;
		public static final int m3_btn_padding_top = 0x7f0600de;
		public static final int m3_btn_stroke_size = 0x7f0600df;
		public static final int m3_btn_text_btn_icon_padding_left = 0x7f0600e0;
		public static final int m3_btn_text_btn_icon_padding_right = 0x7f0600e1;
		public static final int m3_btn_text_btn_padding_left = 0x7f0600e2;
		public static final int m3_btn_text_btn_padding_right = 0x7f0600e3;
		public static final int m3_btn_translation_z_base = 0x7f0600e4;
		public static final int m3_btn_translation_z_hovered = 0x7f0600e5;
		public static final int m3_card_disabled_z = 0x7f0600e6;
		public static final int m3_card_dragged_z = 0x7f0600e7;
		public static final int m3_card_elevated_disabled_z = 0x7f0600e8;
		public static final int m3_card_elevated_dragged_z = 0x7f0600e9;
		public static final int m3_card_elevated_elevation = 0x7f0600ea;
		public static final int m3_card_elevated_hovered_z = 0x7f0600eb;
		public static final int m3_card_elevation = 0x7f0600ec;
		public static final int m3_card_hovered_z = 0x7f0600ed;
		public static final int m3_card_stroke_width = 0x7f0600ee;
		public static final int m3_carousel_debug_keyline_width = 0x7f0600ef;
		public static final int m3_carousel_extra_small_item_size = 0x7f0600f0;
		public static final int m3_carousel_gone_size = 0x7f0600f1;
		public static final int m3_carousel_small_item_default_corner_size = 0x7f0600f2;
		public static final int m3_carousel_small_item_size_max = 0x7f0600f3;
		public static final int m3_carousel_small_item_size_min = 0x7f0600f4;
		public static final int m3_chip_checked_hovered_translation_z = 0x7f0600f5;
		public static final int m3_chip_corner_size = 0x7f0600f6;
		public static final int m3_chip_disabled_translation_z = 0x7f0600f7;
		public static final int m3_chip_dragged_translation_z = 0x7f0600f8;
		public static final int m3_chip_elevated_elevation = 0x7f0600f9;
		public static final int m3_chip_hovered_translation_z = 0x7f0600fa;
		public static final int m3_chip_icon_size = 0x7f0600fb;
		public static final int m3_comp_assist_chip_container_height = 0x7f0600fc;
		public static final int m3_comp_assist_chip_elevated_container_elevation = 0x7f0600fd;
		public static final int m3_comp_assist_chip_flat_container_elevation = 0x7f0600fe;
		public static final int m3_comp_assist_chip_flat_outline_width = 0x7f0600ff;
		public static final int m3_comp_assist_chip_with_icon_icon_size = 0x7f060100;
		public static final int m3_comp_badge_large_size = 0x7f060101;
		public static final int m3_comp_badge_size = 0x7f060102;
		public static final int m3_comp_bottom_app_bar_container_elevation = 0x7f060103;
		public static final int m3_comp_bottom_app_bar_container_height = 0x7f060104;
		public static final int m3_comp_checkbox_selected_disabled_container_opacity = 0x7f060105;
		public static final int m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f060106;
		public static final int m3_comp_date_picker_modal_header_container_height = 0x7f060107;
		public static final int m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f060108;
		public static final int m3_comp_divider_thickness = 0x7f060109;
		public static final int m3_comp_elevated_button_container_elevation = 0x7f06010a;
		public static final int m3_comp_elevated_button_disabled_container_elevation = 0x7f06010b;
		public static final int m3_comp_elevated_card_container_elevation = 0x7f06010c;
		public static final int m3_comp_elevated_card_icon_size = 0x7f06010d;
		public static final int m3_comp_extended_fab_primary_container_elevation = 0x7f06010e;
		public static final int m3_comp_extended_fab_primary_container_height = 0x7f06010f;
		public static final int m3_comp_extended_fab_primary_focus_container_elevation = 0x7f060110;
		public static final int m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f060111;
		public static final int m3_comp_extended_fab_primary_hover_container_elevation = 0x7f060112;
		public static final int m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f060113;
		public static final int m3_comp_extended_fab_primary_icon_size = 0x7f060114;
		public static final int m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f060115;
		public static final int m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f060116;
		public static final int m3_comp_fab_primary_container_elevation = 0x7f060117;
		public static final int m3_comp_fab_primary_container_height = 0x7f060118;
		public static final int m3_comp_fab_primary_focus_state_layer_opacity = 0x7f060119;
		public static final int m3_comp_fab_primary_hover_container_elevation = 0x7f06011a;
		public static final int m3_comp_fab_primary_hover_state_layer_opacity = 0x7f06011b;
		public static final int m3_comp_fab_primary_icon_size = 0x7f06011c;
		public static final int m3_comp_fab_primary_large_container_height = 0x7f06011d;
		public static final int m3_comp_fab_primary_large_icon_size = 0x7f06011e;
		public static final int m3_comp_fab_primary_pressed_container_elevation = 0x7f06011f;
		public static final int m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f060120;
		public static final int m3_comp_fab_primary_small_container_height = 0x7f060121;
		public static final int m3_comp_fab_primary_small_icon_size = 0x7f060122;
		public static final int m3_comp_filled_autocomplete_menu_container_elevation = 0x7f060123;
		public static final int m3_comp_filled_button_container_elevation = 0x7f060124;
		public static final int m3_comp_filled_button_with_icon_icon_size = 0x7f060125;
		public static final int m3_comp_filled_card_container_elevation = 0x7f060126;
		public static final int m3_comp_filled_card_dragged_state_layer_opacity = 0x7f060127;
		public static final int m3_comp_filled_card_focus_state_layer_opacity = 0x7f060128;
		public static final int m3_comp_filled_card_hover_state_layer_opacity = 0x7f060129;
		public static final int m3_comp_filled_card_icon_size = 0x7f06012a;
		public static final int m3_comp_filled_card_pressed_state_layer_opacity = 0x7f06012b;
		public static final int m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f06012c;
		public static final int m3_comp_filter_chip_container_height = 0x7f06012d;
		public static final int m3_comp_filter_chip_elevated_container_elevation = 0x7f06012e;
		public static final int m3_comp_filter_chip_flat_container_elevation = 0x7f06012f;
		public static final int m3_comp_filter_chip_flat_unselected_outline_width = 0x7f060130;
		public static final int m3_comp_filter_chip_with_icon_icon_size = 0x7f060131;
		public static final int m3_comp_input_chip_container_elevation = 0x7f060132;
		public static final int m3_comp_input_chip_container_height = 0x7f060133;
		public static final int m3_comp_input_chip_unselected_outline_width = 0x7f060134;
		public static final int m3_comp_input_chip_with_avatar_avatar_size = 0x7f060135;
		public static final int m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f060136;
		public static final int m3_comp_menu_container_elevation = 0x7f060137;
		public static final int m3_comp_navigation_bar_active_indicator_height = 0x7f060138;
		public static final int m3_comp_navigation_bar_active_indicator_width = 0x7f060139;
		public static final int m3_comp_navigation_bar_container_elevation = 0x7f06013a;
		public static final int m3_comp_navigation_bar_container_height = 0x7f06013b;
		public static final int m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f06013c;
		public static final int m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f06013d;
		public static final int m3_comp_navigation_bar_icon_size = 0x7f06013e;
		public static final int m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f06013f;
		public static final int m3_comp_navigation_drawer_container_width = 0x7f060140;
		public static final int m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f060141;
		public static final int m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f060142;
		public static final int m3_comp_navigation_drawer_icon_size = 0x7f060143;
		public static final int m3_comp_navigation_drawer_modal_container_elevation = 0x7f060144;
		public static final int m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f060145;
		public static final int m3_comp_navigation_drawer_standard_container_elevation = 0x7f060146;
		public static final int m3_comp_navigation_rail_active_indicator_height = 0x7f060147;
		public static final int m3_comp_navigation_rail_active_indicator_width = 0x7f060148;
		public static final int m3_comp_navigation_rail_container_elevation = 0x7f060149;
		public static final int m3_comp_navigation_rail_container_width = 0x7f06014a;
		public static final int m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f06014b;
		public static final int m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f06014c;
		public static final int m3_comp_navigation_rail_icon_size = 0x7f06014d;
		public static final int m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f06014e;
		public static final int m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f06014f;
		public static final int m3_comp_outlined_button_disabled_outline_opacity = 0x7f060150;
		public static final int m3_comp_outlined_button_outline_width = 0x7f060151;
		public static final int m3_comp_outlined_card_container_elevation = 0x7f060152;
		public static final int m3_comp_outlined_card_disabled_outline_opacity = 0x7f060153;
		public static final int m3_comp_outlined_card_icon_size = 0x7f060154;
		public static final int m3_comp_outlined_card_outline_width = 0x7f060155;
		public static final int m3_comp_outlined_icon_button_unselected_outline_width = 0x7f060156;
		public static final int m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f060157;
		public static final int m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f060158;
		public static final int m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f060159;
		public static final int m3_comp_outlined_text_field_focus_outline_width = 0x7f06015a;
		public static final int m3_comp_outlined_text_field_outline_width = 0x7f06015b;
		public static final int m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f06015c;
		public static final int m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f06015d;
		public static final int m3_comp_primary_navigation_tab_active_indicator_height = 0x7f06015e;
		public static final int m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f06015f;
		public static final int m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f060160;
		public static final int m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f060161;
		public static final int m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f060162;
		public static final int m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f060163;
		public static final int m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f060167;
		public static final int m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f060168;
		public static final int m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f060169;
		public static final int m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f06016a;
		public static final int m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f06016b;
		public static final int m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f06016c;
		public static final int m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f06016d;
		public static final int m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f06016e;
		public static final int m3_comp_search_bar_avatar_size = 0x7f060170;
		public static final int m3_comp_search_bar_container_elevation = 0x7f060171;
		public static final int m3_comp_search_bar_container_height = 0x7f060172;
		public static final int m3_comp_search_bar_hover_state_layer_opacity = 0x7f060173;
		public static final int m3_comp_search_bar_pressed_state_layer_opacity = 0x7f060174;
		public static final int m3_comp_search_view_container_elevation = 0x7f060175;
		public static final int m3_comp_search_view_docked_header_container_height = 0x7f060176;
		public static final int m3_comp_search_view_full_screen_header_container_height = 0x7f060177;
		public static final int m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f060178;
		public static final int m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f060179;
		public static final int m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f06017a;
		public static final int m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f06017b;
		public static final int m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f06017c;
		public static final int m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f06017d;
		public static final int m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f06017e;
		public static final int m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f06017f;
		public static final int m3_comp_sheet_side_docked_container_width = 0x7f060180;
		public static final int m3_comp_sheet_side_docked_modal_container_elevation = 0x7f060181;
		public static final int m3_comp_sheet_side_docked_standard_container_elevation = 0x7f060182;
		public static final int m3_comp_slider_disabled_active_track_opacity = 0x7f060186;
		public static final int m3_comp_slider_disabled_handle_opacity = 0x7f060187;
		public static final int m3_comp_slider_disabled_inactive_track_opacity = 0x7f060188;
		public static final int m3_comp_slider_inactive_track_height = 0x7f060189;
		public static final int m3_comp_snackbar_container_elevation = 0x7f06018b;
		public static final int m3_comp_suggestion_chip_container_height = 0x7f06018c;
		public static final int m3_comp_suggestion_chip_elevated_container_elevation = 0x7f06018d;
		public static final int m3_comp_suggestion_chip_flat_container_elevation = 0x7f06018e;
		public static final int m3_comp_suggestion_chip_flat_outline_width = 0x7f06018f;
		public static final int m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f060190;
		public static final int m3_comp_switch_disabled_selected_handle_opacity = 0x7f060191;
		public static final int m3_comp_switch_disabled_selected_icon_opacity = 0x7f060192;
		public static final int m3_comp_switch_disabled_track_opacity = 0x7f060193;
		public static final int m3_comp_switch_disabled_unselected_handle_opacity = 0x7f060194;
		public static final int m3_comp_switch_disabled_unselected_icon_opacity = 0x7f060195;
		public static final int m3_comp_switch_selected_focus_state_layer_opacity = 0x7f060196;
		public static final int m3_comp_switch_selected_hover_state_layer_opacity = 0x7f060197;
		public static final int m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f060198;
		public static final int m3_comp_switch_track_height = 0x7f060199;
		public static final int m3_comp_switch_track_width = 0x7f06019a;
		public static final int m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f06019b;
		public static final int m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f06019c;
		public static final int m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f06019d;
		public static final int m3_comp_text_button_focus_state_layer_opacity = 0x7f06019e;
		public static final int m3_comp_text_button_hover_state_layer_opacity = 0x7f06019f;
		public static final int m3_comp_text_button_pressed_state_layer_opacity = 0x7f0601a0;
		public static final int m3_comp_time_input_time_input_field_focus_outline_width = 0x7f0601a1;
		public static final int m3_comp_time_picker_container_elevation = 0x7f0601a2;
		public static final int m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f0601a3;
		public static final int m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f0601a4;
		public static final int m3_comp_time_picker_period_selector_outline_width = 0x7f0601a5;
		public static final int m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f0601a6;
		public static final int m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f0601a7;
		public static final int m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0601a8;
		public static final int m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0601a9;
		public static final int m3_comp_top_app_bar_large_container_height = 0x7f0601aa;
		public static final int m3_comp_top_app_bar_medium_container_height = 0x7f0601ab;
		public static final int m3_comp_top_app_bar_small_container_elevation = 0x7f0601ac;
		public static final int m3_comp_top_app_bar_small_container_height = 0x7f0601ad;
		public static final int m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0601ae;
		public static final int m3_datepicker_elevation = 0x7f0601af;
		public static final int m3_divider_heavy_thickness = 0x7f0601b0;
		public static final int m3_extended_fab_bottom_padding = 0x7f0601b1;
		public static final int m3_extended_fab_end_padding = 0x7f0601b2;
		public static final int m3_extended_fab_icon_padding = 0x7f0601b3;
		public static final int m3_extended_fab_min_height = 0x7f0601b4;
		public static final int m3_extended_fab_start_padding = 0x7f0601b5;
		public static final int m3_extended_fab_top_padding = 0x7f0601b6;
		public static final int m3_fab_border_width = 0x7f0601b7;
		public static final int m3_fab_corner_size = 0x7f0601b8;
		public static final int m3_fab_translation_z_hovered_focused = 0x7f0601b9;
		public static final int m3_fab_translation_z_pressed = 0x7f0601ba;
		public static final int m3_large_fab_max_image_size = 0x7f0601bb;
		public static final int m3_large_fab_size = 0x7f0601bc;
		public static final int m3_large_text_vertical_offset_adjustment = 0x7f0601bd;
		public static final int m3_menu_elevation = 0x7f0601be;
		public static final int m3_nav_badge_with_text_vertical_offset = 0x7f0601bf;
		public static final int m3_navigation_drawer_layout_corner_size = 0x7f0601c0;
		public static final int m3_navigation_item_active_indicator_label_padding = 0x7f0601c1;
		public static final int m3_navigation_item_horizontal_padding = 0x7f0601c2;
		public static final int m3_navigation_item_icon_padding = 0x7f0601c3;
		public static final int m3_navigation_item_shape_inset_bottom = 0x7f0601c4;
		public static final int m3_navigation_item_shape_inset_end = 0x7f0601c5;
		public static final int m3_navigation_item_shape_inset_start = 0x7f0601c6;
		public static final int m3_navigation_item_shape_inset_top = 0x7f0601c7;
		public static final int m3_navigation_item_vertical_padding = 0x7f0601c8;
		public static final int m3_navigation_menu_divider_horizontal_padding = 0x7f0601c9;
		public static final int m3_navigation_menu_headline_horizontal_padding = 0x7f0601ca;
		public static final int m3_navigation_rail_default_width = 0x7f0601cb;
		public static final int m3_navigation_rail_elevation = 0x7f0601cc;
		public static final int m3_navigation_rail_icon_size = 0x7f0601cd;
		public static final int m3_navigation_rail_item_active_indicator_height = 0x7f0601ce;
		public static final int m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0601cf;
		public static final int m3_navigation_rail_item_active_indicator_width = 0x7f0601d0;
		public static final int m3_navigation_rail_item_min_height = 0x7f0601d1;
		public static final int m3_navigation_rail_item_padding_bottom = 0x7f0601d2;
		public static final int m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0601d3;
		public static final int m3_navigation_rail_item_padding_top = 0x7f0601d4;
		public static final int m3_navigation_rail_item_padding_top_with_large_font = 0x7f0601d5;
		public static final int m3_ripple_default_alpha = 0x7f0601d7;
		public static final int m3_ripple_focused_alpha = 0x7f0601d8;
		public static final int m3_ripple_hovered_alpha = 0x7f0601d9;
		public static final int m3_ripple_pressed_alpha = 0x7f0601da;
		public static final int m3_ripple_selectable_pressed_alpha = 0x7f0601db;
		public static final int m3_searchbar_elevation = 0x7f0601dc;
		public static final int m3_searchbar_height = 0x7f0601dd;
		public static final int m3_searchbar_margin_horizontal = 0x7f0601de;
		public static final int m3_searchbar_margin_vertical = 0x7f0601df;
		public static final int m3_searchbar_outlined_stroke_width = 0x7f0601e0;
		public static final int m3_searchbar_padding_start = 0x7f0601e1;
		public static final int m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0601e2;
		public static final int m3_searchbar_text_size = 0x7f0601e3;
		public static final int m3_searchview_divider_size = 0x7f0601e4;
		public static final int m3_searchview_elevation = 0x7f0601e5;
		public static final int m3_searchview_height = 0x7f0601e6;
		public static final int m3_side_sheet_margin_detached = 0x7f0601e7;
		public static final int m3_side_sheet_modal_elevation = 0x7f0601e8;
		public static final int m3_side_sheet_standard_elevation = 0x7f0601e9;
		public static final int m3_side_sheet_width = 0x7f0601ea;
		public static final int m3_simple_item_color_hovered_alpha = 0x7f0601eb;
		public static final int m3_simple_item_color_selected_alpha = 0x7f0601ec;
		public static final int m3_slider_thumb_elevation = 0x7f0601ed;
		public static final int m3_small_fab_max_image_size = 0x7f0601ee;
		public static final int m3_small_fab_size = 0x7f0601ef;
		public static final int m3_snackbar_action_text_color_alpha = 0x7f0601f0;
		public static final int m3_snackbar_margin = 0x7f0601f1;
		public static final int m3_sys_elevation_level0 = 0x7f0601f2;
		public static final int m3_sys_elevation_level1 = 0x7f0601f3;
		public static final int m3_sys_elevation_level2 = 0x7f0601f4;
		public static final int m3_sys_elevation_level3 = 0x7f0601f5;
		public static final int m3_sys_elevation_level4 = 0x7f0601f6;
		public static final int m3_sys_elevation_level5 = 0x7f0601f7;
		public static final int m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f0601f8;
		public static final int m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f0601f9;
		public static final int m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f0601fa;
		public static final int m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f0601fb;
		public static final int m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f0601fc;
		public static final int m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f0601fd;
		public static final int m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f0601fe;
		public static final int m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f0601ff;
		public static final int m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f060200;
		public static final int m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f060201;
		public static final int m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f060202;
		public static final int m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f060203;
		public static final int m3_sys_motion_easing_legacy_control_x1 = 0x7f060204;
		public static final int m3_sys_motion_easing_legacy_control_x2 = 0x7f060205;
		public static final int m3_sys_motion_easing_legacy_control_y1 = 0x7f060206;
		public static final int m3_sys_motion_easing_legacy_control_y2 = 0x7f060207;
		public static final int m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f060208;
		public static final int m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f060209;
		public static final int m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f06020a;
		public static final int m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f06020b;
		public static final int m3_sys_motion_easing_linear_control_x1 = 0x7f06020c;
		public static final int m3_sys_motion_easing_linear_control_x2 = 0x7f06020d;
		public static final int m3_sys_motion_easing_linear_control_y1 = 0x7f06020e;
		public static final int m3_sys_motion_easing_linear_control_y2 = 0x7f06020f;
		public static final int m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f060210;
		public static final int m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f060211;
		public static final int m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f060212;
		public static final int m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f060213;
		public static final int m3_sys_motion_easing_standard_control_x1 = 0x7f060214;
		public static final int m3_sys_motion_easing_standard_control_x2 = 0x7f060215;
		public static final int m3_sys_motion_easing_standard_control_y1 = 0x7f060216;
		public static final int m3_sys_motion_easing_standard_control_y2 = 0x7f060217;
		public static final int m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f060218;
		public static final int m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f060219;
		public static final int m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f06021a;
		public static final int m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f06021b;
		public static final int m3_sys_state_dragged_state_layer_opacity = 0x7f06021c;
		public static final int m3_sys_state_focus_state_layer_opacity = 0x7f06021d;
		public static final int m3_sys_state_hover_state_layer_opacity = 0x7f06021e;
		public static final int m3_sys_state_pressed_state_layer_opacity = 0x7f06021f;
		public static final int m3_timepicker_display_stroke_width = 0x7f060220;
		public static final int m3_timepicker_window_elevation = 0x7f060221;
		public static final int m3_toolbar_text_size_title = 0x7f060222;
		public static final int material_bottom_sheet_max_width = 0x7f060223;
		public static final int material_clock_display_height = 0x7f060224;
		public static final int material_clock_display_padding = 0x7f060225;
		public static final int material_clock_display_width = 0x7f060226;
		public static final int material_clock_face_margin_bottom = 0x7f060227;
		public static final int material_clock_face_margin_top = 0x7f060228;
		public static final int material_clock_hand_center_dot_radius = 0x7f060229;
		public static final int material_clock_hand_padding = 0x7f06022a;
		public static final int material_clock_hand_stroke_width = 0x7f06022b;
		public static final int material_clock_number_text_size = 0x7f06022c;
		public static final int material_clock_period_toggle_height = 0x7f06022d;
		public static final int material_clock_period_toggle_horizontal_gap = 0x7f06022e;
		public static final int material_clock_period_toggle_vertical_gap = 0x7f06022f;
		public static final int material_clock_period_toggle_width = 0x7f060230;
		public static final int material_clock_size = 0x7f060231;
		public static final int material_cursor_inset = 0x7f060232;
		public static final int material_cursor_width = 0x7f060233;
		public static final int material_divider_thickness = 0x7f060234;
		public static final int material_emphasis_disabled = 0x7f060235;
		public static final int material_emphasis_disabled_background = 0x7f060236;
		public static final int material_emphasis_high_type = 0x7f060237;
		public static final int material_emphasis_medium = 0x7f060238;
		public static final int material_filled_edittext_font_1_3_padding_bottom = 0x7f060239;
		public static final int material_filled_edittext_font_1_3_padding_top = 0x7f06023a;
		public static final int material_filled_edittext_font_2_0_padding_bottom = 0x7f06023b;
		public static final int material_filled_edittext_font_2_0_padding_top = 0x7f06023c;
		public static final int material_font_1_3_box_collapsed_padding_top = 0x7f06023d;
		public static final int material_font_2_0_box_collapsed_padding_top = 0x7f06023e;
		public static final int material_helper_text_default_padding_top = 0x7f06023f;
		public static final int material_helper_text_font_1_3_padding_horizontal = 0x7f060240;
		public static final int material_helper_text_font_1_3_padding_top = 0x7f060241;
		public static final int material_input_text_to_prefix_suffix_padding = 0x7f060242;
		public static final int material_textinput_default_width = 0x7f060243;
		public static final int material_textinput_max_width = 0x7f060244;
		public static final int material_textinput_min_width = 0x7f060245;
		public static final int material_time_picker_minimum_screen_height = 0x7f060246;
		public static final int material_time_picker_minimum_screen_width = 0x7f060247;
		public static final int mtrl_alert_dialog_background_inset_bottom = 0x7f060248;
		public static final int mtrl_alert_dialog_background_inset_end = 0x7f060249;
		public static final int mtrl_alert_dialog_background_inset_start = 0x7f06024a;
		public static final int mtrl_alert_dialog_background_inset_top = 0x7f06024b;
		public static final int mtrl_alert_dialog_picker_background_inset = 0x7f06024c;
		public static final int mtrl_badge_horizontal_edge_offset = 0x7f06024d;
		public static final int mtrl_badge_long_text_horizontal_padding = 0x7f06024e;
		public static final int mtrl_badge_size = 0x7f06024f;
		public static final int mtrl_badge_text_horizontal_edge_offset = 0x7f060250;
		public static final int mtrl_badge_text_size = 0x7f060251;
		public static final int mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f060252;
		public static final int mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f060253;
		public static final int mtrl_badge_with_text_size = 0x7f060254;
		public static final int mtrl_bottomappbar_fabOffsetEndMode = 0x7f060255;
		public static final int mtrl_bottomappbar_fab_bottom_margin = 0x7f060256;
		public static final int mtrl_bottomappbar_fab_cradle_margin = 0x7f060257;
		public static final int mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f060258;
		public static final int mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f060259;
		public static final int mtrl_bottomappbar_height = 0x7f06025a;
		public static final int mtrl_btn_corner_radius = 0x7f06025b;
		public static final int mtrl_btn_dialog_btn_min_width = 0x7f06025c;
		public static final int mtrl_btn_disabled_elevation = 0x7f06025d;
		public static final int mtrl_btn_disabled_z = 0x7f06025e;
		public static final int mtrl_btn_elevation = 0x7f06025f;
		public static final int mtrl_btn_focused_z = 0x7f060260;
		public static final int mtrl_btn_hovered_z = 0x7f060261;
		public static final int mtrl_btn_icon_btn_padding_left = 0x7f060262;
		public static final int mtrl_btn_icon_padding = 0x7f060263;
		public static final int mtrl_btn_inset = 0x7f060264;
		public static final int mtrl_btn_letter_spacing = 0x7f060265;
		public static final int mtrl_btn_max_width = 0x7f060266;
		public static final int mtrl_btn_padding_bottom = 0x7f060267;
		public static final int mtrl_btn_padding_left = 0x7f060268;
		public static final int mtrl_btn_padding_right = 0x7f060269;
		public static final int mtrl_btn_padding_top = 0x7f06026a;
		public static final int mtrl_btn_pressed_z = 0x7f06026b;
		public static final int mtrl_btn_snackbar_margin_horizontal = 0x7f06026c;
		public static final int mtrl_btn_stroke_size = 0x7f06026d;
		public static final int mtrl_btn_text_btn_icon_padding = 0x7f06026e;
		public static final int mtrl_btn_text_btn_padding_left = 0x7f06026f;
		public static final int mtrl_btn_text_btn_padding_right = 0x7f060270;
		public static final int mtrl_btn_text_size = 0x7f060271;
		public static final int mtrl_btn_z = 0x7f060272;
		public static final int mtrl_calendar_action_confirm_button_min_width = 0x7f060273;
		public static final int mtrl_calendar_action_height = 0x7f060274;
		public static final int mtrl_calendar_action_padding = 0x7f060275;
		public static final int mtrl_calendar_bottom_padding = 0x7f060276;
		public static final int mtrl_calendar_content_padding = 0x7f060277;
		public static final int mtrl_calendar_day_corner = 0x7f060278;
		public static final int mtrl_calendar_day_height = 0x7f060279;
		public static final int mtrl_calendar_day_horizontal_padding = 0x7f06027a;
		public static final int mtrl_calendar_day_today_stroke = 0x7f06027b;
		public static final int mtrl_calendar_day_vertical_padding = 0x7f06027c;
		public static final int mtrl_calendar_day_width = 0x7f06027d;
		public static final int mtrl_calendar_days_of_week_height = 0x7f06027e;
		public static final int mtrl_calendar_dialog_background_inset = 0x7f06027f;
		public static final int mtrl_calendar_header_content_padding = 0x7f060280;
		public static final int mtrl_calendar_header_content_padding_fullscreen = 0x7f060281;
		public static final int mtrl_calendar_header_divider_thickness = 0x7f060282;
		public static final int mtrl_calendar_header_height = 0x7f060283;
		public static final int mtrl_calendar_header_height_fullscreen = 0x7f060284;
		public static final int mtrl_calendar_header_selection_line_height = 0x7f060285;
		public static final int mtrl_calendar_header_text_padding = 0x7f060286;
		public static final int mtrl_calendar_header_toggle_margin_bottom = 0x7f060287;
		public static final int mtrl_calendar_header_toggle_margin_top = 0x7f060288;
		public static final int mtrl_calendar_landscape_header_width = 0x7f060289;
		public static final int mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f06028a;
		public static final int mtrl_calendar_month_horizontal_padding = 0x7f06028b;
		public static final int mtrl_calendar_month_vertical_padding = 0x7f06028c;
		public static final int mtrl_calendar_navigation_bottom_padding = 0x7f06028d;
		public static final int mtrl_calendar_navigation_height = 0x7f06028e;
		public static final int mtrl_calendar_navigation_top_padding = 0x7f06028f;
		public static final int mtrl_calendar_pre_l_text_clip_padding = 0x7f060290;
		public static final int mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f060291;
		public static final int mtrl_calendar_selection_text_baseline_to_bottom = 0x7f060292;
		public static final int mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f060293;
		public static final int mtrl_calendar_selection_text_baseline_to_top = 0x7f060294;
		public static final int mtrl_calendar_text_input_padding_top = 0x7f060295;
		public static final int mtrl_calendar_title_baseline_to_top = 0x7f060296;
		public static final int mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f060297;
		public static final int mtrl_calendar_year_corner = 0x7f060298;
		public static final int mtrl_calendar_year_height = 0x7f060299;
		public static final int mtrl_calendar_year_horizontal_padding = 0x7f06029a;
		public static final int mtrl_calendar_year_vertical_padding = 0x7f06029b;
		public static final int mtrl_calendar_year_width = 0x7f06029c;
		public static final int mtrl_card_checked_icon_margin = 0x7f06029d;
		public static final int mtrl_card_checked_icon_size = 0x7f06029e;
		public static final int mtrl_card_corner_radius = 0x7f06029f;
		public static final int mtrl_card_dragged_z = 0x7f0602a0;
		public static final int mtrl_card_elevation = 0x7f0602a1;
		public static final int mtrl_card_spacing = 0x7f0602a2;
		public static final int mtrl_chip_pressed_translation_z = 0x7f0602a3;
		public static final int mtrl_chip_text_size = 0x7f0602a4;
		public static final int mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0602a5;
		public static final int mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0602a6;
		public static final int mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0602a7;
		public static final int mtrl_extended_fab_bottom_padding = 0x7f0602a8;
		public static final int mtrl_extended_fab_disabled_elevation = 0x7f0602a9;
		public static final int mtrl_extended_fab_disabled_translation_z = 0x7f0602aa;
		public static final int mtrl_extended_fab_elevation = 0x7f0602ab;
		public static final int mtrl_extended_fab_end_padding = 0x7f0602ac;
		public static final int mtrl_extended_fab_end_padding_icon = 0x7f0602ad;
		public static final int mtrl_extended_fab_icon_size = 0x7f0602ae;
		public static final int mtrl_extended_fab_icon_text_spacing = 0x7f0602af;
		public static final int mtrl_extended_fab_min_height = 0x7f0602b0;
		public static final int mtrl_extended_fab_min_width = 0x7f0602b1;
		public static final int mtrl_extended_fab_start_padding = 0x7f0602b2;
		public static final int mtrl_extended_fab_start_padding_icon = 0x7f0602b3;
		public static final int mtrl_extended_fab_top_padding = 0x7f0602b4;
		public static final int mtrl_extended_fab_translation_z_base = 0x7f0602b5;
		public static final int mtrl_extended_fab_translation_z_hovered_focused = 0x7f0602b6;
		public static final int mtrl_extended_fab_translation_z_pressed = 0x7f0602b7;
		public static final int mtrl_fab_elevation = 0x7f0602b8;
		public static final int mtrl_fab_min_touch_target = 0x7f0602b9;
		public static final int mtrl_fab_translation_z_hovered_focused = 0x7f0602ba;
		public static final int mtrl_fab_translation_z_pressed = 0x7f0602bb;
		public static final int mtrl_high_ripple_default_alpha = 0x7f0602bc;
		public static final int mtrl_high_ripple_focused_alpha = 0x7f0602bd;
		public static final int mtrl_high_ripple_hovered_alpha = 0x7f0602be;
		public static final int mtrl_high_ripple_pressed_alpha = 0x7f0602bf;
		public static final int mtrl_low_ripple_default_alpha = 0x7f0602c0;
		public static final int mtrl_low_ripple_focused_alpha = 0x7f0602c1;
		public static final int mtrl_low_ripple_hovered_alpha = 0x7f0602c2;
		public static final int mtrl_low_ripple_pressed_alpha = 0x7f0602c3;
		public static final int mtrl_min_touch_target_size = 0x7f0602c4;
		public static final int mtrl_navigation_bar_item_default_icon_size = 0x7f0602c5;
		public static final int mtrl_navigation_bar_item_default_margin = 0x7f0602c6;
		public static final int mtrl_navigation_elevation = 0x7f0602c7;
		public static final int mtrl_navigation_item_horizontal_padding = 0x7f0602c8;
		public static final int mtrl_navigation_item_icon_padding = 0x7f0602c9;
		public static final int mtrl_navigation_item_icon_size = 0x7f0602ca;
		public static final int mtrl_navigation_item_shape_horizontal_margin = 0x7f0602cb;
		public static final int mtrl_navigation_item_shape_vertical_margin = 0x7f0602cc;
		public static final int mtrl_navigation_rail_active_text_size = 0x7f0602cd;
		public static final int mtrl_navigation_rail_compact_width = 0x7f0602ce;
		public static final int mtrl_navigation_rail_default_width = 0x7f0602cf;
		public static final int mtrl_navigation_rail_elevation = 0x7f0602d0;
		public static final int mtrl_navigation_rail_icon_margin = 0x7f0602d1;
		public static final int mtrl_navigation_rail_icon_size = 0x7f0602d2;
		public static final int mtrl_navigation_rail_margin = 0x7f0602d3;
		public static final int mtrl_navigation_rail_text_bottom_margin = 0x7f0602d4;
		public static final int mtrl_navigation_rail_text_size = 0x7f0602d5;
		public static final int mtrl_progress_circular_inset = 0x7f0602d6;
		public static final int mtrl_progress_circular_inset_extra_small = 0x7f0602d7;
		public static final int mtrl_progress_circular_inset_medium = 0x7f0602d8;
		public static final int mtrl_progress_circular_inset_small = 0x7f0602d9;
		public static final int mtrl_progress_circular_radius = 0x7f0602da;
		public static final int mtrl_progress_circular_size = 0x7f0602db;
		public static final int mtrl_progress_circular_size_extra_small = 0x7f0602dc;
		public static final int mtrl_progress_circular_size_medium = 0x7f0602dd;
		public static final int mtrl_progress_circular_size_small = 0x7f0602de;
		public static final int mtrl_progress_circular_track_thickness_extra_small = 0x7f0602df;
		public static final int mtrl_progress_circular_track_thickness_medium = 0x7f0602e0;
		public static final int mtrl_progress_circular_track_thickness_small = 0x7f0602e1;
		public static final int mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0602e2;
		public static final int mtrl_progress_track_thickness = 0x7f0602e3;
		public static final int mtrl_shape_corner_size_large_component = 0x7f0602e4;
		public static final int mtrl_shape_corner_size_medium_component = 0x7f0602e5;
		public static final int mtrl_shape_corner_size_small_component = 0x7f0602e6;
		public static final int mtrl_slider_halo_radius = 0x7f0602e7;
		public static final int mtrl_slider_label_padding = 0x7f0602e8;
		public static final int mtrl_slider_label_radius = 0x7f0602e9;
		public static final int mtrl_slider_label_square_side = 0x7f0602ea;
		public static final int mtrl_slider_thumb_elevation = 0x7f0602eb;
		public static final int mtrl_slider_thumb_radius = 0x7f0602ec;
		public static final int mtrl_slider_tick_radius = 0x7f0602ee;
		public static final int mtrl_slider_track_height = 0x7f0602ef;
		public static final int mtrl_slider_track_side_padding = 0x7f0602f0;
		public static final int mtrl_slider_widget_height = 0x7f0602f1;
		public static final int mtrl_snackbar_action_text_color_alpha = 0x7f0602f2;
		public static final int mtrl_snackbar_background_corner_radius = 0x7f0602f3;
		public static final int mtrl_snackbar_background_overlay_color_alpha = 0x7f0602f4;
		public static final int mtrl_snackbar_margin = 0x7f0602f5;
		public static final int mtrl_snackbar_message_margin_horizontal = 0x7f0602f6;
		public static final int mtrl_snackbar_padding_horizontal = 0x7f0602f7;
		public static final int mtrl_switch_text_padding = 0x7f0602f8;
		public static final int mtrl_switch_thumb_elevation = 0x7f0602f9;
		public static final int mtrl_switch_thumb_icon_size = 0x7f0602fa;
		public static final int mtrl_switch_thumb_size = 0x7f0602fb;
		public static final int mtrl_switch_track_height = 0x7f0602fc;
		public static final int mtrl_switch_track_width = 0x7f0602fd;
		public static final int mtrl_textinput_box_corner_radius_medium = 0x7f0602fe;
		public static final int mtrl_textinput_box_corner_radius_small = 0x7f0602ff;
		public static final int mtrl_textinput_box_label_cutout_padding = 0x7f060300;
		public static final int mtrl_textinput_box_stroke_width_default = 0x7f060301;
		public static final int mtrl_textinput_box_stroke_width_focused = 0x7f060302;
		public static final int mtrl_textinput_counter_margin_start = 0x7f060303;
		public static final int mtrl_textinput_end_icon_margin_start = 0x7f060304;
		public static final int mtrl_textinput_outline_box_expanded_padding = 0x7f060305;
		public static final int mtrl_textinput_start_icon_margin_end = 0x7f060306;
		public static final int mtrl_toolbar_default_height = 0x7f060307;
		public static final int mtrl_tooltip_arrowSize = 0x7f060308;
		public static final int mtrl_tooltip_cornerSize = 0x7f060309;
		public static final int mtrl_tooltip_minHeight = 0x7f06030a;
		public static final int mtrl_tooltip_minWidth = 0x7f06030b;
		public static final int mtrl_tooltip_padding = 0x7f06030c;
		public static final int mtrl_transition_shared_axis_slide_distance = 0x7f06030d;
		public static final int notification_action_icon_size = 0x7f06030e;
		public static final int notification_action_text_size = 0x7f06030f;
		public static final int notification_big_circle_margin = 0x7f060310;
		public static final int notification_content_margin_start = 0x7f060311;
		public static final int notification_large_icon_height = 0x7f060312;
		public static final int notification_large_icon_width = 0x7f060313;
		public static final int notification_main_column_padding_top = 0x7f060314;
		public static final int notification_media_narrow_margin = 0x7f060315;
		public static final int notification_right_icon_size = 0x7f060316;
		public static final int notification_right_side_padding_top = 0x7f060317;
		public static final int notification_small_icon_background_padding = 0x7f060318;
		public static final int notification_small_icon_size_as_large = 0x7f060319;
		public static final int notification_subtext_size = 0x7f06031a;
		public static final int notification_top_pad = 0x7f06031b;
		public static final int notification_top_pad_large_text = 0x7f06031c;
		public static final int tooltip_corner_radius = 0x7f06031e;
		public static final int tooltip_horizontal_padding = 0x7f06031f;
		public static final int tooltip_margin = 0x7f060320;
		public static final int tooltip_precise_anchor_extra_offset = 0x7f060321;
		public static final int tooltip_precise_anchor_threshold = 0x7f060322;
		public static final int tooltip_vertical_padding = 0x7f060323;
		public static final int tooltip_y_offset_non_touch = 0x7f060324;
		public static final int tooltip_y_offset_touch = 0x7f060325;
	}
	public static final class drawable {
		public static final int abc_ab_share_pack_mtrl_alpha = 0x7f070028;
		public static final int abc_action_bar_item_background_material = 0x7f070029;
		public static final int abc_btn_borderless_material = 0x7f07002a;
		public static final int abc_btn_check_material = 0x7f07002b;
		public static final int abc_btn_check_material_anim = 0x7f07002c;
		public static final int abc_btn_check_to_on_mtrl_000 = 0x7f07002d;
		public static final int abc_btn_check_to_on_mtrl_015 = 0x7f07002e;
		public static final int abc_btn_colored_material = 0x7f07002f;
		public static final int abc_btn_default_mtrl_shape = 0x7f070030;
		public static final int abc_btn_radio_material = 0x7f070031;
		public static final int abc_btn_radio_material_anim = 0x7f070032;
		public static final int abc_btn_radio_to_on_mtrl_000 = 0x7f070033;
		public static final int abc_btn_radio_to_on_mtrl_015 = 0x7f070034;
		public static final int abc_btn_switch_to_on_mtrl_00001 = 0x7f070035;
		public static final int abc_btn_switch_to_on_mtrl_00012 = 0x7f070036;
		public static final int abc_cab_background_internal_bg = 0x7f070037;
		public static final int abc_cab_background_top_material = 0x7f070038;
		public static final int abc_cab_background_top_mtrl_alpha = 0x7f070039;
		public static final int abc_control_background_material = 0x7f07003a;
		public static final int abc_dialog_material_background = 0x7f07003b;
		public static final int abc_edit_text_material = 0x7f07003c;
		public static final int abc_ic_ab_back_material = 0x7f07003d;
		public static final int abc_ic_arrow_drop_right_black_24dp = 0x7f07003e;
		public static final int abc_ic_clear_material = 0x7f07003f;
		public static final int abc_ic_commit_search_api_mtrl_alpha = 0x7f070040;
		public static final int abc_ic_go_search_api_material = 0x7f070041;
		public static final int abc_ic_menu_copy_mtrl_am_alpha = 0x7f070042;
		public static final int abc_ic_menu_cut_mtrl_alpha = 0x7f070043;
		public static final int abc_ic_menu_overflow_material = 0x7f070044;
		public static final int abc_ic_menu_paste_mtrl_am_alpha = 0x7f070045;
		public static final int abc_ic_menu_selectall_mtrl_alpha = 0x7f070046;
		public static final int abc_ic_menu_share_mtrl_alpha = 0x7f070047;
		public static final int abc_ic_search_api_material = 0x7f070048;
		public static final int abc_ic_voice_search_api_material = 0x7f070049;
		public static final int abc_item_background_holo_dark = 0x7f07004a;
		public static final int abc_item_background_holo_light = 0x7f07004b;
		public static final int abc_list_divider_material = 0x7f07004c;
		public static final int abc_list_divider_mtrl_alpha = 0x7f07004d;
		public static final int abc_list_focused_holo = 0x7f07004e;
		public static final int abc_list_longpressed_holo = 0x7f07004f;
		public static final int abc_list_pressed_holo_dark = 0x7f070050;
		public static final int abc_list_pressed_holo_light = 0x7f070051;
		public static final int abc_list_selector_background_transition_holo_dark = 0x7f070052;
		public static final int abc_list_selector_background_transition_holo_light = 0x7f070053;
		public static final int abc_list_selector_disabled_holo_dark = 0x7f070054;
		public static final int abc_list_selector_disabled_holo_light = 0x7f070055;
		public static final int abc_list_selector_holo_dark = 0x7f070056;
		public static final int abc_list_selector_holo_light = 0x7f070057;
		public static final int abc_menu_hardkey_panel_mtrl_mult = 0x7f070058;
		public static final int abc_popup_background_mtrl_mult = 0x7f070059;
		public static final int abc_ratingbar_indicator_material = 0x7f07005a;
		public static final int abc_ratingbar_material = 0x7f07005b;
		public static final int abc_ratingbar_small_material = 0x7f07005c;
		public static final int abc_scrubber_control_off_mtrl_alpha = 0x7f07005d;
		public static final int abc_scrubber_control_to_pressed_mtrl_000 = 0x7f07005e;
		public static final int abc_scrubber_control_to_pressed_mtrl_005 = 0x7f07005f;
		public static final int abc_scrubber_primary_mtrl_alpha = 0x7f070060;
		public static final int abc_scrubber_track_mtrl_alpha = 0x7f070061;
		public static final int abc_seekbar_thumb_material = 0x7f070062;
		public static final int abc_seekbar_tick_mark_material = 0x7f070063;
		public static final int abc_seekbar_track_material = 0x7f070064;
		public static final int abc_spinner_mtrl_am_alpha = 0x7f070065;
		public static final int abc_spinner_textfield_background_material = 0x7f070066;
		public static final int abc_star_black_48dp = 0x7f070067;
		public static final int abc_star_half_black_48dp = 0x7f070068;
		public static final int abc_switch_thumb_material = 0x7f070069;
		public static final int abc_switch_track_mtrl_alpha = 0x7f07006a;
		public static final int abc_tab_indicator_material = 0x7f07006b;
		public static final int abc_tab_indicator_mtrl_alpha = 0x7f07006c;
		public static final int abc_text_cursor_material = 0x7f07006d;
		public static final int abc_text_select_handle_left_mtrl = 0x7f07006e;
		public static final int abc_text_select_handle_middle_mtrl = 0x7f07006f;
		public static final int abc_text_select_handle_right_mtrl = 0x7f070070;
		public static final int abc_textfield_activated_mtrl_alpha = 0x7f070071;
		public static final int abc_textfield_default_mtrl_alpha = 0x7f070072;
		public static final int abc_textfield_search_activated_mtrl_alpha = 0x7f070073;
		public static final int abc_textfield_search_default_mtrl_alpha = 0x7f070074;
		public static final int abc_textfield_search_material = 0x7f070075;
		public static final int abc_vector_test = 0x7f070076;
		public static final int avd_hide_password = 0x7f070077;
		public static final int avd_show_password = 0x7f070078;
		public static final int btn_checkbox_checked_mtrl = 0x7f070079;
		public static final int btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f07007a;
		public static final int btn_checkbox_unchecked_mtrl = 0x7f07007b;
		public static final int btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f07007c;
		public static final int btn_radio_off_mtrl = 0x7f07007d;
		public static final int btn_radio_off_to_on_mtrl_animation = 0x7f07007e;
		public static final int btn_radio_on_mtrl = 0x7f07007f;
		public static final int btn_radio_on_to_off_mtrl_animation = 0x7f070080;
		public static final int design_fab_background = 0x7f070081;
		public static final int design_ic_visibility = 0x7f070082;
		public static final int design_ic_visibility_off = 0x7f070083;
		public static final int design_password_eye = 0x7f070084;
		public static final int design_snackbar_background = 0x7f070085;
		public static final int ic_arrow_back_black_24 = 0x7f070087;
		public static final int ic_call_answer = 0x7f070088;
		public static final int ic_call_answer_low = 0x7f070089;
		public static final int ic_call_answer_video = 0x7f07008a;
		public static final int ic_call_answer_video_low = 0x7f07008b;
		public static final int ic_call_decline = 0x7f07008c;
		public static final int ic_call_decline_low = 0x7f07008d;
		public static final int ic_clear_black_24 = 0x7f07008e;
		public static final int ic_clock_black_24dp = 0x7f07008f;
		public static final int ic_keyboard_black_24dp = 0x7f070090;
		public static final int ic_m3_chip_check = 0x7f070091;
		public static final int ic_m3_chip_checked_circle = 0x7f070092;
		public static final int ic_m3_chip_close = 0x7f070093;
		public static final int ic_mtrl_checked_circle = 0x7f070094;
		public static final int ic_mtrl_chip_checked_black = 0x7f070095;
		public static final int ic_mtrl_chip_checked_circle = 0x7f070096;
		public static final int ic_mtrl_chip_close_circle = 0x7f070097;
		public static final int ic_search_black_24 = 0x7f070098;
		public static final int m3_avd_hide_password = 0x7f07009a;
		public static final int m3_avd_show_password = 0x7f07009b;
		public static final int m3_bottom_sheet_drag_handle = 0x7f07009c;
		public static final int m3_password_eye = 0x7f07009d;
		public static final int m3_popupmenu_background_overlay = 0x7f07009e;
		public static final int m3_radiobutton_ripple = 0x7f07009f;
		public static final int m3_selection_control_ripple = 0x7f0700a0;
		public static final int m3_tabs_background = 0x7f0700a1;
		public static final int m3_tabs_line_indicator = 0x7f0700a2;
		public static final int m3_tabs_rounded_line_indicator = 0x7f0700a3;
		public static final int m3_tabs_transparent_background = 0x7f0700a4;
		public static final int material_cursor_drawable = 0x7f0700a5;
		public static final int material_ic_calendar_black_24dp = 0x7f0700a6;
		public static final int material_ic_clear_black_24dp = 0x7f0700a7;
		public static final int material_ic_edit_black_24dp = 0x7f0700a8;
		public static final int material_ic_keyboard_arrow_left_black_24dp = 0x7f0700a9;
		public static final int material_ic_keyboard_arrow_next_black_24dp = 0x7f0700aa;
		public static final int material_ic_keyboard_arrow_previous_black_24dp = 0x7f0700ab;
		public static final int material_ic_keyboard_arrow_right_black_24dp = 0x7f0700ac;
		public static final int material_ic_menu_arrow_down_black_24dp = 0x7f0700ad;
		public static final int material_ic_menu_arrow_up_black_24dp = 0x7f0700ae;
		public static final int mtrl_bottomsheet_drag_handle = 0x7f0700b1;
		public static final int mtrl_checkbox_button = 0x7f0700b2;
		public static final int mtrl_checkbox_button_checked_unchecked = 0x7f0700b3;
		public static final int mtrl_checkbox_button_icon = 0x7f0700b4;
		public static final int mtrl_checkbox_button_icon_checked_indeterminate = 0x7f0700b5;
		public static final int mtrl_checkbox_button_icon_checked_unchecked = 0x7f0700b6;
		public static final int mtrl_checkbox_button_icon_indeterminate_checked = 0x7f0700b7;
		public static final int mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f0700b8;
		public static final int mtrl_checkbox_button_icon_unchecked_checked = 0x7f0700b9;
		public static final int mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f0700ba;
		public static final int mtrl_checkbox_button_unchecked_checked = 0x7f0700bb;
		public static final int mtrl_dialog_background = 0x7f0700bc;
		public static final int mtrl_dropdown_arrow = 0x7f0700bd;
		public static final int mtrl_ic_arrow_drop_down = 0x7f0700be;
		public static final int mtrl_ic_arrow_drop_up = 0x7f0700bf;
		public static final int mtrl_ic_cancel = 0x7f0700c0;
		public static final int mtrl_ic_check_mark = 0x7f0700c1;
		public static final int mtrl_ic_checkbox_checked = 0x7f0700c2;
		public static final int mtrl_ic_checkbox_unchecked = 0x7f0700c3;
		public static final int mtrl_ic_error = 0x7f0700c4;
		public static final int mtrl_ic_indeterminate = 0x7f0700c5;
		public static final int mtrl_navigation_bar_item_background = 0x7f0700c6;
		public static final int mtrl_popupmenu_background = 0x7f0700c7;
		public static final int mtrl_popupmenu_background_overlay = 0x7f0700c8;
		public static final int mtrl_switch_thumb = 0x7f0700c9;
		public static final int mtrl_switch_thumb_checked = 0x7f0700ca;
		public static final int mtrl_switch_thumb_checked_pressed = 0x7f0700cb;
		public static final int mtrl_switch_thumb_checked_unchecked = 0x7f0700cc;
		public static final int mtrl_switch_thumb_pressed = 0x7f0700cd;
		public static final int mtrl_switch_thumb_pressed_checked = 0x7f0700ce;
		public static final int mtrl_switch_thumb_pressed_unchecked = 0x7f0700cf;
		public static final int mtrl_switch_thumb_unchecked = 0x7f0700d0;
		public static final int mtrl_switch_thumb_unchecked_checked = 0x7f0700d1;
		public static final int mtrl_switch_thumb_unchecked_pressed = 0x7f0700d2;
		public static final int mtrl_switch_track = 0x7f0700d3;
		public static final int mtrl_switch_track_decoration = 0x7f0700d4;
		public static final int mtrl_tabs_default_indicator = 0x7f0700d5;
		public static final int navigation_empty_icon = 0x7f0700d6;
		public static final int notification_action_background = 0x7f0700d7;
		public static final int notification_bg = 0x7f0700d8;
		public static final int notification_bg_low = 0x7f0700d9;
		public static final int notification_bg_low_normal = 0x7f0700da;
		public static final int notification_bg_low_pressed = 0x7f0700db;
		public static final int notification_bg_normal = 0x7f0700dc;
		public static final int notification_bg_normal_pressed = 0x7f0700dd;
		public static final int notification_icon_background = 0x7f0700de;
		public static final int notification_oversize_large_icon_bg = 0x7f0700df;
		public static final int notification_template_icon_bg = 0x7f0700e0;
		public static final int notification_template_icon_low_bg = 0x7f0700e1;
		public static final int notification_tile_bg = 0x7f0700e2;
		public static final int notify_panel_notification_icon_bg = 0x7f0700e3;
		public static final int test_level_drawable = 0x7f0700e5;
		public static final int tooltip_frame_dark = 0x7f0700e6;
		public static final int tooltip_frame_light = 0x7f0700e7;
	}
	public static final class id {
		public static final int BOTTOM_END = 0x7f080001;
		public static final int BOTTOM_START = 0x7f080002;
		public static final int NO_DEBUG = 0x7f080006;
		public static final int SHOW_ALL = 0x7f080008;
		public static final int SHOW_PATH = 0x7f080009;
		public static final int SHOW_PROGRESS = 0x7f08000a;
		public static final int TOP_END = 0x7f08000c;
		public static final int TOP_START = 0x7f08000d;
		public static final int accelerate = 0x7f08000f;
		public static final int accessibility_action_clickable_span = 0x7f080010;
		public static final int accessibility_custom_action_0 = 0x7f080011;
		public static final int accessibility_custom_action_1 = 0x7f080012;
		public static final int accessibility_custom_action_10 = 0x7f080013;
		public static final int accessibility_custom_action_11 = 0x7f080014;
		public static final int accessibility_custom_action_12 = 0x7f080015;
		public static final int accessibility_custom_action_13 = 0x7f080016;
		public static final int accessibility_custom_action_14 = 0x7f080017;
		public static final int accessibility_custom_action_15 = 0x7f080018;
		public static final int accessibility_custom_action_16 = 0x7f080019;
		public static final int accessibility_custom_action_17 = 0x7f08001a;
		public static final int accessibility_custom_action_18 = 0x7f08001b;
		public static final int accessibility_custom_action_19 = 0x7f08001c;
		public static final int accessibility_custom_action_2 = 0x7f08001d;
		public static final int accessibility_custom_action_20 = 0x7f08001e;
		public static final int accessibility_custom_action_21 = 0x7f08001f;
		public static final int accessibility_custom_action_22 = 0x7f080020;
		public static final int accessibility_custom_action_23 = 0x7f080021;
		public static final int accessibility_custom_action_24 = 0x7f080022;
		public static final int accessibility_custom_action_25 = 0x7f080023;
		public static final int accessibility_custom_action_26 = 0x7f080024;
		public static final int accessibility_custom_action_27 = 0x7f080025;
		public static final int accessibility_custom_action_28 = 0x7f080026;
		public static final int accessibility_custom_action_29 = 0x7f080027;
		public static final int accessibility_custom_action_3 = 0x7f080028;
		public static final int accessibility_custom_action_30 = 0x7f080029;
		public static final int accessibility_custom_action_31 = 0x7f08002a;
		public static final int accessibility_custom_action_4 = 0x7f08002b;
		public static final int accessibility_custom_action_5 = 0x7f08002c;
		public static final int accessibility_custom_action_6 = 0x7f08002d;
		public static final int accessibility_custom_action_7 = 0x7f08002e;
		public static final int accessibility_custom_action_8 = 0x7f08002f;
		public static final int accessibility_custom_action_9 = 0x7f080030;
		public static final int action_bar = 0x7f080034;
		public static final int action_bar_activity_content = 0x7f080035;
		public static final int action_bar_container = 0x7f080036;
		public static final int action_bar_root = 0x7f080037;
		public static final int action_bar_spinner = 0x7f080038;
		public static final int action_bar_subtitle = 0x7f080039;
		public static final int action_bar_title = 0x7f08003a;
		public static final int action_container = 0x7f08003b;
		public static final int action_context_bar = 0x7f08003c;
		public static final int action_divider = 0x7f08003d;
		public static final int action_image = 0x7f08003e;
		public static final int action_menu_divider = 0x7f08003f;
		public static final int action_menu_presenter = 0x7f080040;
		public static final int action_mode_bar = 0x7f080041;
		public static final int action_mode_bar_stub = 0x7f080042;
		public static final int action_mode_close_button = 0x7f080043;
		public static final int action_text = 0x7f080044;
		public static final int actions = 0x7f080045;
		public static final int activity_chooser_view_content = 0x7f080046;
		public static final int add = 0x7f080047;
		public static final int adjacent = 0x7f080048;
		public static final int alertTitle = 0x7f080049;
		public static final int aligned = 0x7f08004a;
		public static final int always = 0x7f08004d;
		public static final int alwaysAllow = 0x7f08004e;
		public static final int alwaysDisallow = 0x7f08004f;
		public static final int androidx_window_activity_scope = 0x7f080050;
		public static final int animateToEnd = 0x7f080051;
		public static final int animateToStart = 0x7f080052;
		public static final int arc = 0x7f080055;
		public static final int asConfigured = 0x7f080056;
		public static final int async = 0x7f080057;
		public static final int auto = 0x7f080058;
		public static final int autoComplete = 0x7f080059;
		public static final int autoCompleteToEnd = 0x7f08005a;
		public static final int autoCompleteToStart = 0x7f08005b;
		public static final int baseline = 0x7f08005e;
		public static final int blocking = 0x7f080063;
		public static final int bottom = 0x7f080064;
		public static final int bottomToTop = 0x7f080065;
		public static final int bounce = 0x7f080066;
		public static final int buttonPanel = 0x7f08006f;
		public static final int cancel_button = 0x7f080072;
		public static final int center = 0x7f080074;
		public static final int centerCrop = 0x7f080075;
		public static final int centerInside = 0x7f080076;
		public static final int chain = 0x7f080079;
		public static final int checkbox = 0x7f08007c;
		public static final int checked = 0x7f08007d;
		public static final int chronometer = 0x7f08007e;
		public static final int circle_center = 0x7f08007f;
		public static final int clear_text = 0x7f080080;
		public static final int clockwise = 0x7f080083;
		public static final int compress = 0x7f080086;
		public static final int confirm_button = 0x7f080087;
		public static final int container = 0x7f080089;
		public static final int content = 0x7f08008a;
		public static final int contentPanel = 0x7f08008b;
		public static final int contiguous = 0x7f08008c;
		public static final int coordinator = 0x7f08008e;
		public static final int cos = 0x7f08008f;
		public static final int counterclockwise = 0x7f080090;
		public static final int cradle = 0x7f080091;
		public static final int custom = 0x7f080093;
		public static final int customPanel = 0x7f080094;
		public static final int cut = 0x7f080095;
		public static final int date_picker_actions = 0x7f080096;
		public static final int decelerate = 0x7f080097;
		public static final int decelerateAndComplete = 0x7f080098;
		public static final int decor_content_parent = 0x7f080099;
		public static final int default_activity_button = 0x7f08009a;
		public static final int deltaRelative = 0x7f08009b;
		public static final int design_bottom_sheet = 0x7f08009d;
		public static final int design_menu_item_action_area = 0x7f08009e;
		public static final int design_menu_item_action_area_stub = 0x7f08009f;
		public static final int design_menu_item_text = 0x7f0800a0;
		public static final int design_navigation_view = 0x7f0800a1;
		public static final int dialog_button = 0x7f0800a2;
		public static final int disjoint = 0x7f0800a9;
		public static final int dragDown = 0x7f0800ac;
		public static final int dragEnd = 0x7f0800ad;
		public static final int dragLeft = 0x7f0800ae;
		public static final int dragRight = 0x7f0800af;
		public static final int dragStart = 0x7f0800b0;
		public static final int dragUp = 0x7f0800b1;
		public static final int dropdown_menu = 0x7f0800b2;
		public static final int easeIn = 0x7f0800b3;
		public static final int easeInOut = 0x7f0800b4;
		public static final int easeOut = 0x7f0800b5;
		public static final int edge = 0x7f0800b7;
		public static final int edit_query = 0x7f0800b8;
		public static final int edit_text_id = 0x7f0800b9;
		public static final int elastic = 0x7f0800ba;
		public static final int embed = 0x7f0800bb;
		public static final int end = 0x7f0800bc;
		public static final int endToStart = 0x7f0800bd;
		public static final int expand_activities_button = 0x7f0800c2;
		public static final int expanded_menu = 0x7f0800c3;
		public static final int fade = 0x7f0800c4;
		public static final int fill = 0x7f0800c5;
		public static final int filled = 0x7f0800c8;
		public static final int fitCenter = 0x7f0800c9;
		public static final int fitEnd = 0x7f0800ca;
		public static final int fitStart = 0x7f0800cb;
		public static final int fitXY = 0x7f0800cd;
		public static final int fixed = 0x7f0800ce;
		public static final int flip = 0x7f0800cf;
		public static final int floating = 0x7f0800d0;
		public static final int forever = 0x7f0800d2;
		public static final int fragment_container_view_tag = 0x7f0800d3;
		public static final int fullscreen_header = 0x7f0800d5;
		public static final int ghost_view = 0x7f0800d6;
		public static final int ghost_view_holder = 0x7f0800d7;
		public static final int glide_custom_view_target_tag = 0x7f0800d8;
		public static final int gone = 0x7f0800d9;
		public static final int group_divider = 0x7f0800dc;
		public static final int header_title = 0x7f0800df;
		public static final int hide_ime_id = 0x7f0800e0;
		public static final int home = 0x7f0800e2;
		public static final int honorRequest = 0x7f0800e4;
		public static final int icon = 0x7f0800e7;
		public static final int icon_group = 0x7f0800e8;
		public static final int ignore = 0x7f0800ea;
		public static final int ignoreRequest = 0x7f0800eb;
		public static final int image = 0x7f0800ec;
		public static final int indeterminate = 0x7f0800ef;
		public static final int info = 0x7f0800f0;
		public static final int invisible = 0x7f0800f1;
		public static final int inward = 0x7f0800f2;
		public static final int italic = 0x7f0800f4;
		public static final int item_touch_helper_previous_elevation = 0x7f0800f5;
		public static final int jumpToEnd = 0x7f0800f6;
		public static final int jumpToStart = 0x7f0800f7;
		public static final int labeled = 0x7f0800f8;
		public static final int layout = 0x7f0800f9;
		public static final int left = 0x7f0800fa;
		public static final int leftToRight = 0x7f0800fb;
		public static final int legacy = 0x7f0800fc;
		public static final int line1 = 0x7f0800fd;
		public static final int line3 = 0x7f0800fe;
		public static final int linear = 0x7f0800ff;
		public static final int listMode = 0x7f080100;
		public static final int list_item = 0x7f080101;
		public static final int locale = 0x7f080102;
		public static final int ltr = 0x7f080103;
		public static final int m3_side_sheet = 0x7f080104;
		public static final int marquee = 0x7f080105;
		public static final int masked = 0x7f080106;
		public static final int match_parent = 0x7f080108;
		public static final int material_clock_display = 0x7f080109;
		public static final int material_clock_display_and_toggle = 0x7f08010a;
		public static final int material_clock_face = 0x7f08010b;
		public static final int material_clock_hand = 0x7f08010c;
		public static final int material_clock_level = 0x7f08010d;
		public static final int material_clock_period_am_button = 0x7f08010e;
		public static final int material_clock_period_pm_button = 0x7f08010f;
		public static final int material_clock_period_toggle = 0x7f080110;
		public static final int material_hour_text_input = 0x7f080111;
		public static final int material_hour_tv = 0x7f080112;
		public static final int material_label = 0x7f080113;
		public static final int material_minute_text_input = 0x7f080114;
		public static final int material_minute_tv = 0x7f080115;
		public static final int material_textinput_timepicker = 0x7f080116;
		public static final int material_timepicker_cancel_button = 0x7f080117;
		public static final int material_timepicker_container = 0x7f080118;
		public static final int material_timepicker_mode_button = 0x7f080119;
		public static final int material_timepicker_ok_button = 0x7f08011a;
		public static final int material_timepicker_view = 0x7f08011b;
		public static final int material_value_index = 0x7f08011c;
		public static final int matrix = 0x7f08011d;
		public static final int maui_custom_view_target_running_callbacks_tag = 0x7f08011e;
		public static final int message = 0x7f08011f;
		public static final int middle = 0x7f080120;
		public static final int mini = 0x7f080121;
		public static final int month_grid = 0x7f080122;
		public static final int month_navigation_bar = 0x7f080123;
		public static final int month_navigation_fragment_toggle = 0x7f080124;
		public static final int month_navigation_next = 0x7f080125;
		public static final int month_navigation_previous = 0x7f080126;
		public static final int month_title = 0x7f080127;
		public static final int motion_base = 0x7f080128;
		public static final int mtrl_anchor_parent = 0x7f080129;
		public static final int mtrl_calendar_day_selector_frame = 0x7f08012a;
		public static final int mtrl_calendar_days_of_week = 0x7f08012b;
		public static final int mtrl_calendar_frame = 0x7f08012c;
		public static final int mtrl_calendar_main_pane = 0x7f08012d;
		public static final int mtrl_calendar_months = 0x7f08012e;
		public static final int mtrl_calendar_selection_frame = 0x7f08012f;
		public static final int mtrl_calendar_text_input_frame = 0x7f080130;
		public static final int mtrl_calendar_year_selector_frame = 0x7f080131;
		public static final int mtrl_card_checked_layer_id = 0x7f080132;
		public static final int mtrl_child_content_container = 0x7f080133;
		public static final int mtrl_internal_children_alpha_tag = 0x7f080134;
		public static final int mtrl_motion_snapshot_view = 0x7f080135;
		public static final int mtrl_picker_fullscreen = 0x7f080136;
		public static final int mtrl_picker_header = 0x7f080137;
		public static final int mtrl_picker_header_selection_text = 0x7f080138;
		public static final int mtrl_picker_header_title_and_selection = 0x7f080139;
		public static final int mtrl_picker_header_toggle = 0x7f08013a;
		public static final int mtrl_picker_text_input_date = 0x7f08013b;
		public static final int mtrl_picker_text_input_range_end = 0x7f08013c;
		public static final int mtrl_picker_text_input_range_start = 0x7f08013d;
		public static final int mtrl_picker_title_text = 0x7f08013e;
		public static final int mtrl_view_tag_bottom_padding = 0x7f08013f;
		public static final int multiply = 0x7f080140;
		public static final int navigation_bar_item_active_indicator_view = 0x7f080144;
		public static final int navigation_bar_item_icon_container = 0x7f080145;
		public static final int navigation_bar_item_icon_view = 0x7f080146;
		public static final int navigation_bar_item_labels_group = 0x7f080147;
		public static final int navigation_bar_item_large_label_view = 0x7f080148;
		public static final int navigation_bar_item_small_label_view = 0x7f080149;
		public static final int navigation_header_container = 0x7f08014a;
		public static final int never = 0x7f080150;
		public static final int none = 0x7f080155;
		public static final int normal = 0x7f080156;
		public static final int notification_background = 0x7f080158;
		public static final int notification_main_column = 0x7f080159;
		public static final int notification_main_column_container = 0x7f08015a;
		public static final int off = 0x7f08015b;
		public static final int on = 0x7f08015c;
		public static final int open_search_bar_text_view = 0x7f08015e;
		public static final int open_search_view_background = 0x7f08015f;
		public static final int open_search_view_clear_button = 0x7f080160;
		public static final int open_search_view_content_container = 0x7f080161;
		public static final int open_search_view_divider = 0x7f080162;
		public static final int open_search_view_dummy_toolbar = 0x7f080163;
		public static final int open_search_view_edit_text = 0x7f080164;
		public static final int open_search_view_header_container = 0x7f080165;
		public static final int open_search_view_root = 0x7f080166;
		public static final int open_search_view_scrim = 0x7f080167;
		public static final int open_search_view_search_prefix = 0x7f080168;
		public static final int open_search_view_status_bar_spacer = 0x7f080169;
		public static final int open_search_view_toolbar = 0x7f08016a;
		public static final int open_search_view_toolbar_container = 0x7f08016b;
		public static final int outline = 0x7f08016c;
		public static final int outward = 0x7f08016d;
		public static final int packed = 0x7f08016f;
		public static final int parallax = 0x7f080170;
		public static final int parent = 0x7f080171;
		public static final int parentPanel = 0x7f080172;
		public static final int parentRelative = 0x7f080173;
		public static final int parent_matrix = 0x7f080174;
		public static final int password_toggle = 0x7f080175;
		public static final int path = 0x7f080176;
		public static final int pathRelative = 0x7f080177;
		public static final int percent = 0x7f080179;
		public static final int pin = 0x7f08017a;
		public static final int position = 0x7f08017c;
		public static final int postLayout = 0x7f08017d;
		public static final int pressed = 0x7f08017e;
		public static final int progress_circular = 0x7f08017f;
		public static final int progress_horizontal = 0x7f080180;
		public static final int radio = 0x7f080181;
		public static final int rectangles = 0x7f080183;
		public static final int report_drawn = 0x7f080184;
		public static final int reverseSawtooth = 0x7f080185;
		public static final int right = 0x7f080186;
		public static final int rightToLeft = 0x7f080187;
		public static final int right_icon = 0x7f080188;
		public static final int right_side = 0x7f080189;
		public static final int rounded = 0x7f08018a;
		public static final int row_index_key = 0x7f08018b;
		public static final int rtl = 0x7f08018c;
		public static final int save_non_transition_alpha = 0x7f08018d;
		public static final int save_overlay_view = 0x7f08018e;
		public static final int sawtooth = 0x7f08018f;
		public static final int scale = 0x7f080190;
		public static final int screen = 0x7f080191;
		public static final int scrollIndicatorDown = 0x7f080193;
		public static final int scrollIndicatorUp = 0x7f080194;
		public static final int scrollView = 0x7f080195;
		public static final int scrollable = 0x7f080196;
		public static final int search_badge = 0x7f080197;
		public static final int search_bar = 0x7f080198;
		public static final int search_button = 0x7f080199;
		public static final int search_close_btn = 0x7f08019a;
		public static final int search_edit_frame = 0x7f08019b;
		public static final int search_go_btn = 0x7f08019c;
		public static final int search_mag_icon = 0x7f08019d;
		public static final int search_plate = 0x7f08019e;
		public static final int search_src_text = 0x7f08019f;
		public static final int search_voice_btn = 0x7f0801a0;
		public static final int select_dialog_listview = 0x7f0801a1;
		public static final int selected = 0x7f0801a2;
		public static final int selection_type = 0x7f0801a3;
		public static final int shortcut = 0x7f0801a7;
		public static final int sin = 0x7f0801ab;
		public static final int slide = 0x7f0801ae;
		public static final int snackbar_action = 0x7f0801b1;
		public static final int snackbar_text = 0x7f0801b2;
		public static final int spacer = 0x7f0801b6;
		public static final int special_effects_controller_view_tag = 0x7f0801b7;
		public static final int spline = 0x7f0801b8;
		public static final int split_action_bar = 0x7f0801b9;
		public static final int spread = 0x7f0801ba;
		public static final int spread_inside = 0x7f0801bb;
		public static final int square = 0x7f0801bd;
		public static final int src_atop = 0x7f0801be;
		public static final int src_in = 0x7f0801bf;
		public static final int src_over = 0x7f0801c0;
		public static final int standard = 0x7f0801c1;
		public static final int start = 0x7f0801c2;
		public static final int startHorizontal = 0x7f0801c3;
		public static final int startToEnd = 0x7f0801c4;
		public static final int startVertical = 0x7f0801c5;
		public static final int staticLayout = 0x7f0801c6;
		public static final int staticPostLayout = 0x7f0801c7;
		public static final int stop = 0x7f0801c8;
		public static final int stretch = 0x7f0801c9;
		public static final int submenuarrow = 0x7f0801ca;
		public static final int submit_area = 0x7f0801cb;
		public static final int tabMode = 0x7f0801cd;
		public static final int tag_accessibility_actions = 0x7f0801ce;
		public static final int tag_accessibility_clickable_spans = 0x7f0801cf;
		public static final int tag_accessibility_heading = 0x7f0801d0;
		public static final int tag_accessibility_pane_title = 0x7f0801d1;
		public static final int tag_on_apply_window_listener = 0x7f0801d3;
		public static final int tag_on_receive_content_listener = 0x7f0801d4;
		public static final int tag_on_receive_content_mime_types = 0x7f0801d5;
		public static final int tag_screen_reader_focusable = 0x7f0801d6;
		public static final int tag_state_description = 0x7f0801d7;
		public static final int tag_transition_group = 0x7f0801d9;
		public static final int tag_unhandled_key_event_manager = 0x7f0801da;
		public static final int tag_unhandled_key_listeners = 0x7f0801db;
		public static final int tag_window_insets_animation_callback = 0x7f0801dc;
		public static final int text = 0x7f0801dd;
		public static final int text2 = 0x7f0801de;
		public static final int textSpacerNoButtons = 0x7f0801e0;
		public static final int textSpacerNoTitle = 0x7f0801e1;
		public static final int text_input_end_icon = 0x7f0801e4;
		public static final int text_input_error_icon = 0x7f0801e5;
		public static final int text_input_start_icon = 0x7f0801e6;
		public static final int textinput_counter = 0x7f0801e7;
		public static final int textinput_error = 0x7f0801e8;
		public static final int textinput_helper_text = 0x7f0801e9;
		public static final int textinput_placeholder = 0x7f0801ea;
		public static final int textinput_prefix_text = 0x7f0801eb;
		public static final int textinput_suffix_text = 0x7f0801ec;
		public static final int time = 0x7f0801ed;
		public static final int title = 0x7f0801ee;
		public static final int titleDividerNoCustom = 0x7f0801ef;
		public static final int title_template = 0x7f0801f0;
		public static final int top = 0x7f0801f2;
		public static final int topPanel = 0x7f0801f3;
		public static final int topToBottom = 0x7f0801f4;
		public static final int touch_outside = 0x7f0801f5;
		public static final int transition_current_scene = 0x7f0801f9;
		public static final int transition_layout_save = 0x7f0801fb;
		public static final int transition_position = 0x7f0801fd;
		public static final int transition_scene_layoutid_cache = 0x7f0801fe;
		public static final int transition_transform = 0x7f0801ff;
		public static final int triangle = 0x7f080200;
		public static final int unchecked = 0x7f080201;
		public static final int uniform = 0x7f080202;
		public static final int unlabeled = 0x7f080203;
		public static final int up = 0x7f080204;
		public static final int view_offset_helper = 0x7f080208;
		public static final int view_tree_lifecycle_owner = 0x7f08020b;
		public static final int view_tree_on_back_pressed_dispatcher_owner = 0x7f08020c;
		public static final int view_tree_saved_state_registry_owner = 0x7f08020d;
		public static final int view_tree_view_model_store_owner = 0x7f08020e;
		public static final int visible = 0x7f08020f;
		public static final int visible_removing_fragment_view_tag = 0x7f080210;
		public static final int with_icon = 0x7f080213;
		public static final int withinBounds = 0x7f080214;
		public static final int wrap = 0x7f080215;
		public static final int wrap_content = 0x7f080216;
	}
	public static final class integer {
		public static final int abc_config_activityDefaultDur = 0x7f090000;
		public static final int abc_config_activityShortDur = 0x7f090001;
		public static final int app_bar_elevation_anim_duration = 0x7f090002;
		public static final int bottom_sheet_slide_duration = 0x7f090003;
		public static final int cancel_button_image_alpha = 0x7f090004;
		public static final int config_tooltipAnimTime = 0x7f090006;
		public static final int design_snackbar_text_max_lines = 0x7f090007;
		public static final int design_tab_indicator_anim_duration_ms = 0x7f090008;
		public static final int hide_password_duration = 0x7f090009;
		public static final int m3_badge_max_number = 0x7f09000a;
		public static final int m3_btn_anim_delay_ms = 0x7f09000b;
		public static final int m3_btn_anim_duration_ms = 0x7f09000c;
		public static final int m3_card_anim_delay_ms = 0x7f09000d;
		public static final int m3_card_anim_duration_ms = 0x7f09000e;
		public static final int m3_chip_anim_duration = 0x7f09000f;
		public static final int m3_sys_motion_duration_extra_long1 = 0x7f090010;
		public static final int m3_sys_motion_duration_extra_long2 = 0x7f090011;
		public static final int m3_sys_motion_duration_extra_long3 = 0x7f090012;
		public static final int m3_sys_motion_duration_extra_long4 = 0x7f090013;
		public static final int m3_sys_motion_duration_long1 = 0x7f090014;
		public static final int m3_sys_motion_duration_long2 = 0x7f090015;
		public static final int m3_sys_motion_duration_long3 = 0x7f090016;
		public static final int m3_sys_motion_duration_long4 = 0x7f090017;
		public static final int m3_sys_motion_duration_medium1 = 0x7f090018;
		public static final int m3_sys_motion_duration_medium2 = 0x7f090019;
		public static final int m3_sys_motion_duration_medium3 = 0x7f09001a;
		public static final int m3_sys_motion_duration_medium4 = 0x7f09001b;
		public static final int m3_sys_motion_duration_short1 = 0x7f09001c;
		public static final int m3_sys_motion_duration_short2 = 0x7f09001d;
		public static final int m3_sys_motion_duration_short3 = 0x7f09001e;
		public static final int m3_sys_motion_duration_short4 = 0x7f09001f;
		public static final int m3_sys_motion_path = 0x7f090020;
		public static final int m3_sys_shape_corner_extra_large_corner_family = 0x7f090021;
		public static final int m3_sys_shape_corner_extra_small_corner_family = 0x7f090022;
		public static final int m3_sys_shape_corner_full_corner_family = 0x7f090023;
		public static final int m3_sys_shape_corner_large_corner_family = 0x7f090024;
		public static final int m3_sys_shape_corner_medium_corner_family = 0x7f090025;
		public static final int m3_sys_shape_corner_small_corner_family = 0x7f090026;
		public static final int material_motion_duration_long_1 = 0x7f090027;
		public static final int material_motion_duration_long_2 = 0x7f090028;
		public static final int material_motion_duration_medium_1 = 0x7f090029;
		public static final int material_motion_duration_medium_2 = 0x7f09002a;
		public static final int material_motion_duration_short_1 = 0x7f09002b;
		public static final int material_motion_duration_short_2 = 0x7f09002c;
		public static final int material_motion_path = 0x7f09002d;
		public static final int mtrl_badge_max_character_count = 0x7f09002e;
		public static final int mtrl_btn_anim_delay_ms = 0x7f09002f;
		public static final int mtrl_btn_anim_duration_ms = 0x7f090030;
		public static final int mtrl_calendar_header_orientation = 0x7f090031;
		public static final int mtrl_calendar_selection_text_lines = 0x7f090032;
		public static final int mtrl_calendar_year_selector_span = 0x7f090033;
		public static final int mtrl_card_anim_delay_ms = 0x7f090034;
		public static final int mtrl_card_anim_duration_ms = 0x7f090035;
		public static final int mtrl_chip_anim_duration = 0x7f090036;
		public static final int mtrl_switch_thumb_motion_duration = 0x7f090037;
		public static final int mtrl_switch_thumb_post_morphing_duration = 0x7f090038;
		public static final int mtrl_switch_thumb_pre_morphing_duration = 0x7f090039;
		public static final int mtrl_switch_thumb_pressed_duration = 0x7f09003a;
		public static final int mtrl_switch_thumb_viewport_center_coordinate = 0x7f09003b;
		public static final int mtrl_switch_thumb_viewport_size = 0x7f09003c;
		public static final int mtrl_switch_track_viewport_height = 0x7f09003d;
		public static final int mtrl_switch_track_viewport_width = 0x7f09003e;
		public static final int mtrl_tab_indicator_anim_duration_ms = 0x7f09003f;
		public static final int mtrl_view_gone = 0x7f090040;
		public static final int mtrl_view_invisible = 0x7f090041;
		public static final int mtrl_view_visible = 0x7f090042;
		public static final int show_password_duration = 0x7f090043;
		public static final int status_bar_notification_info_maxnum = 0x7f090044;
	}
	public static final class interpolator {
		public static final int btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000;
		public static final int btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001;
		public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002;
		public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003;
		public static final int btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004;
		public static final int btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005;
		public static final int fast_out_slow_in = 0x7f0a0006;
		public static final int m3_sys_motion_easing_emphasized = 0x7f0a0007;
		public static final int m3_sys_motion_easing_emphasized_accelerate = 0x7f0a0008;
		public static final int m3_sys_motion_easing_emphasized_decelerate = 0x7f0a0009;
		public static final int m3_sys_motion_easing_linear = 0x7f0a000a;
		public static final int m3_sys_motion_easing_standard = 0x7f0a000b;
		public static final int m3_sys_motion_easing_standard_accelerate = 0x7f0a000c;
		public static final int m3_sys_motion_easing_standard_decelerate = 0x7f0a000d;
		public static final int mtrl_fast_out_linear_in = 0x7f0a000e;
		public static final int mtrl_fast_out_slow_in = 0x7f0a000f;
		public static final int mtrl_linear = 0x7f0a0010;
		public static final int mtrl_linear_out_slow_in = 0x7f0a0011;
	}
	public static final class layout {
		public static final int abc_action_bar_title_item = 0x7f0b0000;
		public static final int abc_action_bar_up_container = 0x7f0b0001;
		public static final int abc_action_menu_item_layout = 0x7f0b0002;
		public static final int abc_action_menu_layout = 0x7f0b0003;
		public static final int abc_action_mode_bar = 0x7f0b0004;
		public static final int abc_action_mode_close_item_material = 0x7f0b0005;
		public static final int abc_activity_chooser_view = 0x7f0b0006;
		public static final int abc_activity_chooser_view_list_item = 0x7f0b0007;
		public static final int abc_alert_dialog_button_bar_material = 0x7f0b0008;
		public static final int abc_alert_dialog_material = 0x7f0b0009;
		public static final int abc_alert_dialog_title_material = 0x7f0b000a;
		public static final int abc_cascading_menu_item_layout = 0x7f0b000b;
		public static final int abc_dialog_title_material = 0x7f0b000c;
		public static final int abc_expanded_menu_layout = 0x7f0b000d;
		public static final int abc_list_menu_item_checkbox = 0x7f0b000e;
		public static final int abc_list_menu_item_icon = 0x7f0b000f;
		public static final int abc_list_menu_item_layout = 0x7f0b0010;
		public static final int abc_list_menu_item_radio = 0x7f0b0011;
		public static final int abc_popup_menu_header_item_layout = 0x7f0b0012;
		public static final int abc_popup_menu_item_layout = 0x7f0b0013;
		public static final int abc_screen_content_include = 0x7f0b0014;
		public static final int abc_screen_simple = 0x7f0b0015;
		public static final int abc_screen_simple_overlay_action_mode = 0x7f0b0016;
		public static final int abc_screen_toolbar = 0x7f0b0017;
		public static final int abc_search_dropdown_item_icons_2line = 0x7f0b0018;
		public static final int abc_search_view = 0x7f0b0019;
		public static final int abc_select_dialog_material = 0x7f0b001a;
		public static final int abc_tooltip = 0x7f0b001b;
		public static final int custom_dialog = 0x7f0b001e;
		public static final int design_bottom_navigation_item = 0x7f0b001f;
		public static final int design_bottom_sheet_dialog = 0x7f0b0020;
		public static final int design_layout_snackbar = 0x7f0b0021;
		public static final int design_layout_snackbar_include = 0x7f0b0022;
		public static final int design_layout_tab_icon = 0x7f0b0023;
		public static final int design_layout_tab_text = 0x7f0b0024;
		public static final int design_menu_item_action_area = 0x7f0b0025;
		public static final int design_navigation_item = 0x7f0b0026;
		public static final int design_navigation_item_header = 0x7f0b0027;
		public static final int design_navigation_item_separator = 0x7f0b0028;
		public static final int design_navigation_item_subheader = 0x7f0b0029;
		public static final int design_navigation_menu = 0x7f0b002a;
		public static final int design_navigation_menu_item = 0x7f0b002b;
		public static final int design_text_input_end_icon = 0x7f0b002c;
		public static final int design_text_input_start_icon = 0x7f0b002d;
		public static final int ime_base_split_test_activity = 0x7f0b0031;
		public static final int ime_secondary_split_test_activity = 0x7f0b0032;
		public static final int m3_alert_dialog = 0x7f0b0033;
		public static final int m3_alert_dialog_actions = 0x7f0b0034;
		public static final int m3_alert_dialog_title = 0x7f0b0035;
		public static final int m3_auto_complete_simple_item = 0x7f0b0036;
		public static final int m3_side_sheet_dialog = 0x7f0b0037;
		public static final int material_chip_input_combo = 0x7f0b0038;
		public static final int material_clock_display = 0x7f0b0039;
		public static final int material_clock_display_divider = 0x7f0b003a;
		public static final int material_clock_period_toggle = 0x7f0b003b;
		public static final int material_clock_period_toggle_land = 0x7f0b003c;
		public static final int material_clockface_textview = 0x7f0b003d;
		public static final int material_clockface_view = 0x7f0b003e;
		public static final int material_radial_view_group = 0x7f0b003f;
		public static final int material_textinput_timepicker = 0x7f0b0040;
		public static final int material_time_chip = 0x7f0b0041;
		public static final int material_time_input = 0x7f0b0042;
		public static final int material_timepicker = 0x7f0b0043;
		public static final int material_timepicker_dialog = 0x7f0b0044;
		public static final int material_timepicker_textinput_display = 0x7f0b0045;
		public static final int mtrl_alert_dialog = 0x7f0b0046;
		public static final int mtrl_alert_dialog_actions = 0x7f0b0047;
		public static final int mtrl_alert_dialog_title = 0x7f0b0048;
		public static final int mtrl_alert_select_dialog_item = 0x7f0b0049;
		public static final int mtrl_alert_select_dialog_multichoice = 0x7f0b004a;
		public static final int mtrl_alert_select_dialog_singlechoice = 0x7f0b004b;
		public static final int mtrl_auto_complete_simple_item = 0x7f0b004c;
		public static final int mtrl_calendar_day = 0x7f0b004d;
		public static final int mtrl_calendar_day_of_week = 0x7f0b004e;
		public static final int mtrl_calendar_days_of_week = 0x7f0b004f;
		public static final int mtrl_calendar_horizontal = 0x7f0b0050;
		public static final int mtrl_calendar_month = 0x7f0b0051;
		public static final int mtrl_calendar_month_labeled = 0x7f0b0052;
		public static final int mtrl_calendar_month_navigation = 0x7f0b0053;
		public static final int mtrl_calendar_months = 0x7f0b0054;
		public static final int mtrl_calendar_vertical = 0x7f0b0055;
		public static final int mtrl_calendar_year = 0x7f0b0056;
		public static final int mtrl_layout_snackbar = 0x7f0b0057;
		public static final int mtrl_layout_snackbar_include = 0x7f0b0058;
		public static final int mtrl_navigation_rail_item = 0x7f0b0059;
		public static final int mtrl_picker_actions = 0x7f0b005a;
		public static final int mtrl_picker_dialog = 0x7f0b005b;
		public static final int mtrl_picker_fullscreen = 0x7f0b005c;
		public static final int mtrl_picker_header_dialog = 0x7f0b005d;
		public static final int mtrl_picker_header_fullscreen = 0x7f0b005e;
		public static final int mtrl_picker_header_selection_text = 0x7f0b005f;
		public static final int mtrl_picker_header_title_text = 0x7f0b0060;
		public static final int mtrl_picker_header_toggle = 0x7f0b0061;
		public static final int mtrl_picker_text_input_date = 0x7f0b0062;
		public static final int mtrl_picker_text_input_date_range = 0x7f0b0063;
		public static final int mtrl_search_bar = 0x7f0b0064;
		public static final int mtrl_search_view = 0x7f0b0065;
		public static final int notification_action = 0x7f0b0067;
		public static final int notification_action_tombstone = 0x7f0b0068;
		public static final int notification_template_custom_big = 0x7f0b0069;
		public static final int notification_template_icon_group = 0x7f0b006a;
		public static final int notification_template_part_chronometer = 0x7f0b006b;
		public static final int notification_template_part_time = 0x7f0b006c;
		public static final int select_dialog_item_material = 0x7f0b006d;
		public static final int select_dialog_multichoice_material = 0x7f0b006e;
		public static final int select_dialog_singlechoice_material = 0x7f0b006f;
		public static final int support_simple_spinner_dropdown_item = 0x7f0b0071;
	}
	public static final class plurals {
		public static final int mtrl_badge_content_description = 0x7f0e0000;
	}
	public static final class string {
		public static final int abc_action_bar_home_description = 0x7f0f0000;
		public static final int abc_action_bar_up_description = 0x7f0f0001;
		public static final int abc_action_menu_overflow_description = 0x7f0f0002;
		public static final int abc_action_mode_done = 0x7f0f0003;
		public static final int abc_activity_chooser_view_see_all = 0x7f0f0004;
		public static final int abc_activitychooserview_choose_application = 0x7f0f0005;
		public static final int abc_capital_off = 0x7f0f0006;
		public static final int abc_capital_on = 0x7f0f0007;
		public static final int abc_menu_alt_shortcut_label = 0x7f0f0008;
		public static final int abc_menu_ctrl_shortcut_label = 0x7f0f0009;
		public static final int abc_menu_delete_shortcut_label = 0x7f0f000a;
		public static final int abc_menu_enter_shortcut_label = 0x7f0f000b;
		public static final int abc_menu_function_shortcut_label = 0x7f0f000c;
		public static final int abc_menu_meta_shortcut_label = 0x7f0f000d;
		public static final int abc_menu_shift_shortcut_label = 0x7f0f000e;
		public static final int abc_menu_space_shortcut_label = 0x7f0f000f;
		public static final int abc_menu_sym_shortcut_label = 0x7f0f0010;
		public static final int abc_prepend_shortcut_label = 0x7f0f0011;
		public static final int abc_search_hint = 0x7f0f0012;
		public static final int abc_searchview_description_clear = 0x7f0f0013;
		public static final int abc_searchview_description_query = 0x7f0f0014;
		public static final int abc_searchview_description_search = 0x7f0f0015;
		public static final int abc_searchview_description_submit = 0x7f0f0016;
		public static final int abc_searchview_description_voice = 0x7f0f0017;
		public static final int abc_shareactionprovider_share_with = 0x7f0f0018;
		public static final int abc_shareactionprovider_share_with_application = 0x7f0f0019;
		public static final int abc_toolbar_collapse_description = 0x7f0f001a;
		public static final int appbar_scrolling_view_behavior = 0x7f0f001c;
		public static final int bottom_sheet_behavior = 0x7f0f001d;
		public static final int bottomsheet_action_collapse = 0x7f0f001e;
		public static final int bottomsheet_action_expand = 0x7f0f001f;
		public static final int bottomsheet_action_expand_halfway = 0x7f0f0020;
		public static final int bottomsheet_drag_handle_clicked = 0x7f0f0021;
		public static final int bottomsheet_drag_handle_content_description = 0x7f0f0022;
		public static final int call_notification_answer_action = 0x7f0f0023;
		public static final int call_notification_answer_video_action = 0x7f0f0024;
		public static final int call_notification_decline_action = 0x7f0f0025;
		public static final int call_notification_hang_up_action = 0x7f0f0026;
		public static final int call_notification_incoming_text = 0x7f0f0027;
		public static final int call_notification_ongoing_text = 0x7f0f0028;
		public static final int call_notification_screening_text = 0x7f0f0029;
		public static final int character_counter_content_description = 0x7f0f002a;
		public static final int character_counter_overflowed_content_description = 0x7f0f002b;
		public static final int character_counter_pattern = 0x7f0f002c;
		public static final int clear_text_end_icon_content_description = 0x7f0f002d;
		public static final int error_a11y_label = 0x7f0f0030;
		public static final int error_icon_content_description = 0x7f0f0031;
		public static final int exposed_dropdown_menu_content_description = 0x7f0f0032;
		public static final int fab_transformation_scrim_behavior = 0x7f0f0033;
		public static final int fab_transformation_sheet_behavior = 0x7f0f0034;
		public static final int hide_bottom_view_on_scroll_behavior = 0x7f0f0038;
		public static final int icon_content_description = 0x7f0f0039;
		public static final int item_view_role_description = 0x7f0f003a;
		public static final int m3_exceed_max_badge_text_suffix = 0x7f0f003b;
		public static final int m3_ref_typeface_brand_medium = 0x7f0f003c;
		public static final int m3_ref_typeface_brand_regular = 0x7f0f003d;
		public static final int m3_ref_typeface_plain_medium = 0x7f0f003e;
		public static final int m3_ref_typeface_plain_regular = 0x7f0f003f;
		public static final int m3_sys_motion_easing_emphasized = 0x7f0f0040;
		public static final int m3_sys_motion_easing_emphasized_accelerate = 0x7f0f0041;
		public static final int m3_sys_motion_easing_emphasized_decelerate = 0x7f0f0042;
		public static final int m3_sys_motion_easing_emphasized_path_data = 0x7f0f0043;
		public static final int m3_sys_motion_easing_legacy = 0x7f0f0044;
		public static final int m3_sys_motion_easing_legacy_accelerate = 0x7f0f0045;
		public static final int m3_sys_motion_easing_legacy_decelerate = 0x7f0f0046;
		public static final int m3_sys_motion_easing_linear = 0x7f0f0047;
		public static final int m3_sys_motion_easing_standard = 0x7f0f0048;
		public static final int m3_sys_motion_easing_standard_accelerate = 0x7f0f0049;
		public static final int m3_sys_motion_easing_standard_decelerate = 0x7f0f004a;
		public static final int material_clock_display_divider = 0x7f0f004b;
		public static final int material_clock_toggle_content_description = 0x7f0f004c;
		public static final int material_hour_24h_suffix = 0x7f0f004d;
		public static final int material_hour_selection = 0x7f0f004e;
		public static final int material_hour_suffix = 0x7f0f004f;
		public static final int material_minute_selection = 0x7f0f0050;
		public static final int material_minute_suffix = 0x7f0f0051;
		public static final int material_motion_easing_accelerated = 0x7f0f0052;
		public static final int material_motion_easing_decelerated = 0x7f0f0053;
		public static final int material_motion_easing_emphasized = 0x7f0f0054;
		public static final int material_motion_easing_linear = 0x7f0f0055;
		public static final int material_motion_easing_standard = 0x7f0f0056;
		public static final int material_slider_range_end = 0x7f0f0057;
		public static final int material_slider_range_start = 0x7f0f0058;
		public static final int material_slider_value = 0x7f0f0059;
		public static final int material_timepicker_am = 0x7f0f005a;
		public static final int material_timepicker_clock_mode_description = 0x7f0f005b;
		public static final int material_timepicker_hour = 0x7f0f005c;
		public static final int material_timepicker_minute = 0x7f0f005d;
		public static final int material_timepicker_pm = 0x7f0f005e;
		public static final int material_timepicker_select_time = 0x7f0f005f;
		public static final int material_timepicker_text_input_mode_description = 0x7f0f0060;
		public static final int maui_empty_unused = 0x7f0f0061;
		public static final int mtrl_badge_numberless_content_description = 0x7f0f0062;
		public static final int mtrl_checkbox_button_icon_path_checked = 0x7f0f0063;
		public static final int mtrl_checkbox_button_icon_path_group_name = 0x7f0f0064;
		public static final int mtrl_checkbox_button_icon_path_indeterminate = 0x7f0f0065;
		public static final int mtrl_checkbox_button_icon_path_name = 0x7f0f0066;
		public static final int mtrl_checkbox_button_path_checked = 0x7f0f0067;
		public static final int mtrl_checkbox_button_path_group_name = 0x7f0f0068;
		public static final int mtrl_checkbox_button_path_name = 0x7f0f0069;
		public static final int mtrl_checkbox_button_path_unchecked = 0x7f0f006a;
		public static final int mtrl_checkbox_state_description_checked = 0x7f0f006b;
		public static final int mtrl_checkbox_state_description_indeterminate = 0x7f0f006c;
		public static final int mtrl_checkbox_state_description_unchecked = 0x7f0f006d;
		public static final int mtrl_chip_close_icon_content_description = 0x7f0f006e;
		public static final int mtrl_exceed_max_badge_number_content_description = 0x7f0f006f;
		public static final int mtrl_exceed_max_badge_number_suffix = 0x7f0f0070;
		public static final int mtrl_picker_a11y_next_month = 0x7f0f0071;
		public static final int mtrl_picker_a11y_prev_month = 0x7f0f0072;
		public static final int mtrl_picker_announce_current_range_selection = 0x7f0f0073;
		public static final int mtrl_picker_announce_current_selection = 0x7f0f0074;
		public static final int mtrl_picker_announce_current_selection_none = 0x7f0f0075;
		public static final int mtrl_picker_cancel = 0x7f0f0076;
		public static final int mtrl_picker_confirm = 0x7f0f0077;
		public static final int mtrl_picker_date_header_selected = 0x7f0f0078;
		public static final int mtrl_picker_date_header_title = 0x7f0f0079;
		public static final int mtrl_picker_date_header_unselected = 0x7f0f007a;
		public static final int mtrl_picker_day_of_week_column_header = 0x7f0f007b;
		public static final int mtrl_picker_end_date_description = 0x7f0f007c;
		public static final int mtrl_picker_invalid_format = 0x7f0f007d;
		public static final int mtrl_picker_invalid_format_example = 0x7f0f007e;
		public static final int mtrl_picker_invalid_format_use = 0x7f0f007f;
		public static final int mtrl_picker_invalid_range = 0x7f0f0080;
		public static final int mtrl_picker_navigate_to_current_year_description = 0x7f0f0081;
		public static final int mtrl_picker_navigate_to_year_description = 0x7f0f0082;
		public static final int mtrl_picker_out_of_range = 0x7f0f0083;
		public static final int mtrl_picker_range_header_only_end_selected = 0x7f0f0084;
		public static final int mtrl_picker_range_header_only_start_selected = 0x7f0f0085;
		public static final int mtrl_picker_range_header_selected = 0x7f0f0086;
		public static final int mtrl_picker_range_header_title = 0x7f0f0087;
		public static final int mtrl_picker_range_header_unselected = 0x7f0f0088;
		public static final int mtrl_picker_save = 0x7f0f0089;
		public static final int mtrl_picker_start_date_description = 0x7f0f008a;
		public static final int mtrl_picker_text_input_date_hint = 0x7f0f008b;
		public static final int mtrl_picker_text_input_date_range_end_hint = 0x7f0f008c;
		public static final int mtrl_picker_text_input_date_range_start_hint = 0x7f0f008d;
		public static final int mtrl_picker_text_input_day_abbr = 0x7f0f008e;
		public static final int mtrl_picker_text_input_month_abbr = 0x7f0f008f;
		public static final int mtrl_picker_text_input_year_abbr = 0x7f0f0090;
		public static final int mtrl_picker_today_description = 0x7f0f0091;
		public static final int mtrl_picker_toggle_to_calendar_input_mode = 0x7f0f0092;
		public static final int mtrl_picker_toggle_to_day_selection = 0x7f0f0093;
		public static final int mtrl_picker_toggle_to_text_input_mode = 0x7f0f0094;
		public static final int mtrl_picker_toggle_to_year_selection = 0x7f0f0095;
		public static final int mtrl_switch_thumb_group_name = 0x7f0f0096;
		public static final int mtrl_switch_thumb_path_checked = 0x7f0f0097;
		public static final int mtrl_switch_thumb_path_morphing = 0x7f0f0098;
		public static final int mtrl_switch_thumb_path_name = 0x7f0f0099;
		public static final int mtrl_switch_thumb_path_pressed = 0x7f0f009a;
		public static final int mtrl_switch_thumb_path_unchecked = 0x7f0f009b;
		public static final int mtrl_switch_track_decoration_path = 0x7f0f009c;
		public static final int mtrl_switch_track_path = 0x7f0f009d;
		public static final int mtrl_timepicker_cancel = 0x7f0f009e;
		public static final int mtrl_timepicker_confirm = 0x7f0f009f;
		public static final int password_toggle_content_description = 0x7f0f00a3;
		public static final int path_password_eye = 0x7f0f00a4;
		public static final int path_password_eye_mask_strike_through = 0x7f0f00a5;
		public static final int path_password_eye_mask_visible = 0x7f0f00a6;
		public static final int path_password_strike_through = 0x7f0f00a7;
		public static final int search_menu_title = 0x7f0f00a8;
		public static final int searchbar_scrolling_view_behavior = 0x7f0f00a9;
		public static final int searchview_clear_text_content_description = 0x7f0f00aa;
		public static final int searchview_navigation_content_description = 0x7f0f00ab;
		public static final int side_sheet_accessibility_pane_title = 0x7f0f00ac;
		public static final int side_sheet_behavior = 0x7f0f00ad;
		public static final int status_bar_notification_info_overflow = 0x7f0f00ae;
	}
	public static final class style {
		public static final int AlertDialog_AppCompat = 0x7f100001;
		public static final int AlertDialog_AppCompat_Light = 0x7f100002;
		public static final int Animation_AppCompat_Dialog = 0x7f100003;
		public static final int Animation_AppCompat_DropDownUp = 0x7f100004;
		public static final int Animation_AppCompat_Tooltip = 0x7f100005;
		public static final int Animation_Design_BottomSheetDialog = 0x7f100006;
		public static final int Animation_Material3_BottomSheetDialog = 0x7f100007;
		public static final int Animation_Material3_SideSheetDialog = 0x7f100008;
		public static final int Animation_Material3_SideSheetDialog_Left = 0x7f100009;
		public static final int Animation_Material3_SideSheetDialog_Right = 0x7f10000a;
		public static final int Animation_MaterialComponents_BottomSheetDialog = 0x7f10000b;
		public static final int Base_AlertDialog_AppCompat = 0x7f10000e;
		public static final int Base_AlertDialog_AppCompat_Light = 0x7f10000f;
		public static final int Base_Animation_AppCompat_Dialog = 0x7f100010;
		public static final int Base_Animation_AppCompat_DropDownUp = 0x7f100011;
		public static final int Base_Animation_AppCompat_Tooltip = 0x7f100012;
		public static final int Base_CardView = 0x7f100013;
		public static final int Base_DialogWindowTitleBackground_AppCompat = 0x7f100015;
		public static final int Base_DialogWindowTitle_AppCompat = 0x7f100014;
		public static final int Base_MaterialAlertDialog_MaterialComponents_Title_Icon = 0x7f100016;
		public static final int Base_MaterialAlertDialog_MaterialComponents_Title_Panel = 0x7f100017;
		public static final int Base_MaterialAlertDialog_MaterialComponents_Title_Text = 0x7f100018;
		public static final int Base_TextAppearance_AppCompat = 0x7f100019;
		public static final int Base_TextAppearance_AppCompat_Body1 = 0x7f10001a;
		public static final int Base_TextAppearance_AppCompat_Body2 = 0x7f10001b;
		public static final int Base_TextAppearance_AppCompat_Button = 0x7f10001c;
		public static final int Base_TextAppearance_AppCompat_Caption = 0x7f10001d;
		public static final int Base_TextAppearance_AppCompat_Display1 = 0x7f10001e;
		public static final int Base_TextAppearance_AppCompat_Display2 = 0x7f10001f;
		public static final int Base_TextAppearance_AppCompat_Display3 = 0x7f100020;
		public static final int Base_TextAppearance_AppCompat_Display4 = 0x7f100021;
		public static final int Base_TextAppearance_AppCompat_Headline = 0x7f100022;
		public static final int Base_TextAppearance_AppCompat_Inverse = 0x7f100023;
		public static final int Base_TextAppearance_AppCompat_Large = 0x7f100024;
		public static final int Base_TextAppearance_AppCompat_Large_Inverse = 0x7f100025;
		public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f100026;
		public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f100027;
		public static final int Base_TextAppearance_AppCompat_Medium = 0x7f100028;
		public static final int Base_TextAppearance_AppCompat_Medium_Inverse = 0x7f100029;
		public static final int Base_TextAppearance_AppCompat_Menu = 0x7f10002a;
		public static final int Base_TextAppearance_AppCompat_SearchResult = 0x7f10002b;
		public static final int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f10002c;
		public static final int Base_TextAppearance_AppCompat_SearchResult_Title = 0x7f10002d;
		public static final int Base_TextAppearance_AppCompat_Small = 0x7f10002e;
		public static final int Base_TextAppearance_AppCompat_Small_Inverse = 0x7f10002f;
		public static final int Base_TextAppearance_AppCompat_Subhead = 0x7f100030;
		public static final int Base_TextAppearance_AppCompat_Subhead_Inverse = 0x7f100031;
		public static final int Base_TextAppearance_AppCompat_Title = 0x7f100032;
		public static final int Base_TextAppearance_AppCompat_Title_Inverse = 0x7f100033;
		public static final int Base_TextAppearance_AppCompat_Tooltip = 0x7f100034;
		public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f100035;
		public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f100036;
		public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f100037;
		public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f100038;
		public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f100039;
		public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f10003a;
		public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f10003b;
		public static final int Base_TextAppearance_AppCompat_Widget_Button = 0x7f10003c;
		public static final int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f10003d;
		public static final int Base_TextAppearance_AppCompat_Widget_Button_Colored = 0x7f10003e;
		public static final int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f10003f;
		public static final int Base_TextAppearance_AppCompat_Widget_DropDownItem = 0x7f100040;
		public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f100041;
		public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f100042;
		public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f100043;
		public static final int Base_TextAppearance_AppCompat_Widget_Switch = 0x7f100044;
		public static final int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f100045;
		public static final int Base_TextAppearance_Material3_Search = 0x7f100046;
		public static final int Base_TextAppearance_MaterialComponents_Badge = 0x7f100047;
		public static final int Base_TextAppearance_MaterialComponents_Button = 0x7f100048;
		public static final int Base_TextAppearance_MaterialComponents_Headline6 = 0x7f100049;
		public static final int Base_TextAppearance_MaterialComponents_Subtitle2 = 0x7f10004a;
		public static final int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f10004b;
		public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f10004c;
		public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f10004d;
		public static final int Base_ThemeOverlay_AppCompat = 0x7f10007b;
		public static final int Base_ThemeOverlay_AppCompat_ActionBar = 0x7f10007c;
		public static final int Base_ThemeOverlay_AppCompat_Dark = 0x7f10007d;
		public static final int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f10007e;
		public static final int Base_ThemeOverlay_AppCompat_Dialog = 0x7f10007f;
		public static final int Base_ThemeOverlay_AppCompat_Dialog_Alert = 0x7f100080;
		public static final int Base_ThemeOverlay_AppCompat_Light = 0x7f100081;
		public static final int Base_ThemeOverlay_Material3_AutoCompleteTextView = 0x7f100082;
		public static final int Base_ThemeOverlay_Material3_BottomSheetDialog = 0x7f100083;
		public static final int Base_ThemeOverlay_Material3_Dialog = 0x7f100084;
		public static final int Base_ThemeOverlay_Material3_SideSheetDialog = 0x7f100085;
		public static final int Base_ThemeOverlay_Material3_TextInputEditText = 0x7f100086;
		public static final int Base_ThemeOverlay_MaterialComponents_Dialog = 0x7f100087;
		public static final int Base_ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f100088;
		public static final int Base_ThemeOverlay_MaterialComponents_Dialog_Alert_Framework = 0x7f100089;
		public static final int Base_ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework = 0x7f10008a;
		public static final int Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog = 0x7f10008b;
		public static final int Base_Theme_AppCompat = 0x7f10004e;
		public static final int Base_Theme_AppCompat_CompactMenu = 0x7f10004f;
		public static final int Base_Theme_AppCompat_Dialog = 0x7f100050;
		public static final int Base_Theme_AppCompat_DialogWhenLarge = 0x7f100054;
		public static final int Base_Theme_AppCompat_Dialog_Alert = 0x7f100051;
		public static final int Base_Theme_AppCompat_Dialog_FixedSize = 0x7f100052;
		public static final int Base_Theme_AppCompat_Dialog_MinWidth = 0x7f100053;
		public static final int Base_Theme_AppCompat_Light = 0x7f100055;
		public static final int Base_Theme_AppCompat_Light_DarkActionBar = 0x7f100056;
		public static final int Base_Theme_AppCompat_Light_Dialog = 0x7f100057;
		public static final int Base_Theme_AppCompat_Light_DialogWhenLarge = 0x7f10005b;
		public static final int Base_Theme_AppCompat_Light_Dialog_Alert = 0x7f100058;
		public static final int Base_Theme_AppCompat_Light_Dialog_FixedSize = 0x7f100059;
		public static final int Base_Theme_AppCompat_Light_Dialog_MinWidth = 0x7f10005a;
		public static final int Base_Theme_Material3_Dark = 0x7f10005c;
		public static final int Base_Theme_Material3_Dark_BottomSheetDialog = 0x7f10005d;
		public static final int Base_Theme_Material3_Dark_Dialog = 0x7f10005e;
		public static final int Base_Theme_Material3_Dark_DialogWhenLarge = 0x7f100060;
		public static final int Base_Theme_Material3_Dark_Dialog_FixedSize = 0x7f10005f;
		public static final int Base_Theme_Material3_Dark_SideSheetDialog = 0x7f100061;
		public static final int Base_Theme_Material3_Light = 0x7f100062;
		public static final int Base_Theme_Material3_Light_BottomSheetDialog = 0x7f100063;
		public static final int Base_Theme_Material3_Light_Dialog = 0x7f100064;
		public static final int Base_Theme_Material3_Light_DialogWhenLarge = 0x7f100066;
		public static final int Base_Theme_Material3_Light_Dialog_FixedSize = 0x7f100065;
		public static final int Base_Theme_Material3_Light_SideSheetDialog = 0x7f100067;
		public static final int Base_Theme_MaterialComponents = 0x7f100068;
		public static final int Base_Theme_MaterialComponents_Bridge = 0x7f100069;
		public static final int Base_Theme_MaterialComponents_CompactMenu = 0x7f10006a;
		public static final int Base_Theme_MaterialComponents_Dialog = 0x7f10006b;
		public static final int Base_Theme_MaterialComponents_DialogWhenLarge = 0x7f100070;
		public static final int Base_Theme_MaterialComponents_Dialog_Alert = 0x7f10006c;
		public static final int Base_Theme_MaterialComponents_Dialog_Bridge = 0x7f10006d;
		public static final int Base_Theme_MaterialComponents_Dialog_FixedSize = 0x7f10006e;
		public static final int Base_Theme_MaterialComponents_Dialog_MinWidth = 0x7f10006f;
		public static final int Base_Theme_MaterialComponents_Light = 0x7f100071;
		public static final int Base_Theme_MaterialComponents_Light_Bridge = 0x7f100072;
		public static final int Base_Theme_MaterialComponents_Light_DarkActionBar = 0x7f100073;
		public static final int Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f100074;
		public static final int Base_Theme_MaterialComponents_Light_Dialog = 0x7f100075;
		public static final int Base_Theme_MaterialComponents_Light_DialogWhenLarge = 0x7f10007a;
		public static final int Base_Theme_MaterialComponents_Light_Dialog_Alert = 0x7f100076;
		public static final int Base_Theme_MaterialComponents_Light_Dialog_Bridge = 0x7f100077;
		public static final int Base_Theme_MaterialComponents_Light_Dialog_FixedSize = 0x7f100078;
		public static final int Base_Theme_MaterialComponents_Light_Dialog_MinWidth = 0x7f100079;
		public static final int Base_V14_ThemeOverlay_Material3_BottomSheetDialog = 0x7f10009d;
		public static final int Base_V14_ThemeOverlay_Material3_SideSheetDialog = 0x7f10009e;
		public static final int Base_V14_ThemeOverlay_MaterialComponents_BottomSheetDialog = 0x7f10009f;
		public static final int Base_V14_ThemeOverlay_MaterialComponents_Dialog = 0x7f1000a0;
		public static final int Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f1000a1;
		public static final int Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog = 0x7f1000a2;
		public static final int Base_V14_Theme_Material3_Dark = 0x7f10008c;
		public static final int Base_V14_Theme_Material3_Dark_BottomSheetDialog = 0x7f10008d;
		public static final int Base_V14_Theme_Material3_Dark_Dialog = 0x7f10008e;
		public static final int Base_V14_Theme_Material3_Dark_SideSheetDialog = 0x7f10008f;
		public static final int Base_V14_Theme_Material3_Light = 0x7f100090;
		public static final int Base_V14_Theme_Material3_Light_BottomSheetDialog = 0x7f100091;
		public static final int Base_V14_Theme_Material3_Light_Dialog = 0x7f100092;
		public static final int Base_V14_Theme_Material3_Light_SideSheetDialog = 0x7f100093;
		public static final int Base_V14_Theme_MaterialComponents = 0x7f100094;
		public static final int Base_V14_Theme_MaterialComponents_Bridge = 0x7f100095;
		public static final int Base_V14_Theme_MaterialComponents_Dialog = 0x7f100096;
		public static final int Base_V14_Theme_MaterialComponents_Dialog_Bridge = 0x7f100097;
		public static final int Base_V14_Theme_MaterialComponents_Light = 0x7f100098;
		public static final int Base_V14_Theme_MaterialComponents_Light_Bridge = 0x7f100099;
		public static final int Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f10009a;
		public static final int Base_V14_Theme_MaterialComponents_Light_Dialog = 0x7f10009b;
		public static final int Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge = 0x7f10009c;
		public static final int Base_V14_Widget_MaterialComponents_AutoCompleteTextView = 0x7f1000a3;
		public static final int Base_V21_ThemeOverlay_AppCompat_Dialog = 0x7f1000ac;
		public static final int Base_V21_ThemeOverlay_Material3_BottomSheetDialog = 0x7f1000ad;
		public static final int Base_V21_ThemeOverlay_Material3_SideSheetDialog = 0x7f1000ae;
		public static final int Base_V21_ThemeOverlay_MaterialComponents_BottomSheetDialog = 0x7f1000af;
		public static final int Base_V21_Theme_AppCompat = 0x7f1000a4;
		public static final int Base_V21_Theme_AppCompat_Dialog = 0x7f1000a5;
		public static final int Base_V21_Theme_AppCompat_Light = 0x7f1000a6;
		public static final int Base_V21_Theme_AppCompat_Light_Dialog = 0x7f1000a7;
		public static final int Base_V21_Theme_MaterialComponents = 0x7f1000a8;
		public static final int Base_V21_Theme_MaterialComponents_Dialog = 0x7f1000a9;
		public static final int Base_V21_Theme_MaterialComponents_Light = 0x7f1000aa;
		public static final int Base_V21_Theme_MaterialComponents_Light_Dialog = 0x7f1000ab;
		public static final int Base_V22_Theme_AppCompat = 0x7f1000b0;
		public static final int Base_V22_Theme_AppCompat_Light = 0x7f1000b1;
		public static final int Base_V23_Theme_AppCompat = 0x7f1000b2;
		public static final int Base_V23_Theme_AppCompat_Light = 0x7f1000b3;
		public static final int Base_V24_Theme_Material3_Dark = 0x7f1000b4;
		public static final int Base_V24_Theme_Material3_Dark_Dialog = 0x7f1000b5;
		public static final int Base_V24_Theme_Material3_Light = 0x7f1000b6;
		public static final int Base_V24_Theme_Material3_Light_Dialog = 0x7f1000b7;
		public static final int Base_V26_Theme_AppCompat = 0x7f1000b8;
		public static final int Base_V26_Theme_AppCompat_Light = 0x7f1000b9;
		public static final int Base_V26_Widget_AppCompat_Toolbar = 0x7f1000ba;
		public static final int Base_V28_Theme_AppCompat = 0x7f1000bb;
		public static final int Base_V28_Theme_AppCompat_Light = 0x7f1000bc;
		public static final int Base_V7_ThemeOverlay_AppCompat_Dialog = 0x7f1000c1;
		public static final int Base_V7_Theme_AppCompat = 0x7f1000bd;
		public static final int Base_V7_Theme_AppCompat_Dialog = 0x7f1000be;
		public static final int Base_V7_Theme_AppCompat_Light = 0x7f1000bf;
		public static final int Base_V7_Theme_AppCompat_Light_Dialog = 0x7f1000c0;
		public static final int Base_V7_Widget_AppCompat_AutoCompleteTextView = 0x7f1000c2;
		public static final int Base_V7_Widget_AppCompat_EditText = 0x7f1000c3;
		public static final int Base_V7_Widget_AppCompat_Toolbar = 0x7f1000c4;
		public static final int Base_Widget_AppCompat_ActionBar = 0x7f1000c5;
		public static final int Base_Widget_AppCompat_ActionBar_Solid = 0x7f1000c6;
		public static final int Base_Widget_AppCompat_ActionBar_TabBar = 0x7f1000c7;
		public static final int Base_Widget_AppCompat_ActionBar_TabText = 0x7f1000c8;
		public static final int Base_Widget_AppCompat_ActionBar_TabView = 0x7f1000c9;
		public static final int Base_Widget_AppCompat_ActionButton = 0x7f1000ca;
		public static final int Base_Widget_AppCompat_ActionButton_CloseMode = 0x7f1000cb;
		public static final int Base_Widget_AppCompat_ActionButton_Overflow = 0x7f1000cc;
		public static final int Base_Widget_AppCompat_ActionMode = 0x7f1000cd;
		public static final int Base_Widget_AppCompat_ActivityChooserView = 0x7f1000ce;
		public static final int Base_Widget_AppCompat_AutoCompleteTextView = 0x7f1000cf;
		public static final int Base_Widget_AppCompat_Button = 0x7f1000d0;
		public static final int Base_Widget_AppCompat_ButtonBar = 0x7f1000d6;
		public static final int Base_Widget_AppCompat_ButtonBar_AlertDialog = 0x7f1000d7;
		public static final int Base_Widget_AppCompat_Button_Borderless = 0x7f1000d1;
		public static final int Base_Widget_AppCompat_Button_Borderless_Colored = 0x7f1000d2;
		public static final int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f1000d3;
		public static final int Base_Widget_AppCompat_Button_Colored = 0x7f1000d4;
		public static final int Base_Widget_AppCompat_Button_Small = 0x7f1000d5;
		public static final int Base_Widget_AppCompat_CompoundButton_CheckBox = 0x7f1000d8;
		public static final int Base_Widget_AppCompat_CompoundButton_RadioButton = 0x7f1000d9;
		public static final int Base_Widget_AppCompat_CompoundButton_Switch = 0x7f1000da;
		public static final int Base_Widget_AppCompat_DrawerArrowToggle = 0x7f1000db;
		public static final int Base_Widget_AppCompat_DrawerArrowToggle_Common = 0x7f1000dc;
		public static final int Base_Widget_AppCompat_DropDownItem_Spinner = 0x7f1000dd;
		public static final int Base_Widget_AppCompat_EditText = 0x7f1000de;
		public static final int Base_Widget_AppCompat_ImageButton = 0x7f1000df;
		public static final int Base_Widget_AppCompat_Light_ActionBar = 0x7f1000e0;
		public static final int Base_Widget_AppCompat_Light_ActionBar_Solid = 0x7f1000e1;
		public static final int Base_Widget_AppCompat_Light_ActionBar_TabBar = 0x7f1000e2;
		public static final int Base_Widget_AppCompat_Light_ActionBar_TabText = 0x7f1000e3;
		public static final int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f1000e4;
		public static final int Base_Widget_AppCompat_Light_ActionBar_TabView = 0x7f1000e5;
		public static final int Base_Widget_AppCompat_Light_PopupMenu = 0x7f1000e6;
		public static final int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f1000e7;
		public static final int Base_Widget_AppCompat_ListMenuView = 0x7f1000e8;
		public static final int Base_Widget_AppCompat_ListPopupWindow = 0x7f1000e9;
		public static final int Base_Widget_AppCompat_ListView = 0x7f1000ea;
		public static final int Base_Widget_AppCompat_ListView_DropDown = 0x7f1000eb;
		public static final int Base_Widget_AppCompat_ListView_Menu = 0x7f1000ec;
		public static final int Base_Widget_AppCompat_PopupMenu = 0x7f1000ed;
		public static final int Base_Widget_AppCompat_PopupMenu_Overflow = 0x7f1000ee;
		public static final int Base_Widget_AppCompat_PopupWindow = 0x7f1000ef;
		public static final int Base_Widget_AppCompat_ProgressBar = 0x7f1000f0;
		public static final int Base_Widget_AppCompat_ProgressBar_Horizontal = 0x7f1000f1;
		public static final int Base_Widget_AppCompat_RatingBar = 0x7f1000f2;
		public static final int Base_Widget_AppCompat_RatingBar_Indicator = 0x7f1000f3;
		public static final int Base_Widget_AppCompat_RatingBar_Small = 0x7f1000f4;
		public static final int Base_Widget_AppCompat_SearchView = 0x7f1000f5;
		public static final int Base_Widget_AppCompat_SearchView_ActionBar = 0x7f1000f6;
		public static final int Base_Widget_AppCompat_SeekBar = 0x7f1000f7;
		public static final int Base_Widget_AppCompat_SeekBar_Discrete = 0x7f1000f8;
		public static final int Base_Widget_AppCompat_Spinner = 0x7f1000f9;
		public static final int Base_Widget_AppCompat_Spinner_Underlined = 0x7f1000fa;
		public static final int Base_Widget_AppCompat_TextView = 0x7f1000fb;
		public static final int Base_Widget_AppCompat_TextView_SpinnerItem = 0x7f1000fc;
		public static final int Base_Widget_AppCompat_Toolbar = 0x7f1000fd;
		public static final int Base_Widget_AppCompat_Toolbar_Button_Navigation = 0x7f1000fe;
		public static final int Base_Widget_Design_TabLayout = 0x7f1000ff;
		public static final int Base_Widget_Material3_ActionBar_Solid = 0x7f100100;
		public static final int Base_Widget_Material3_ActionMode = 0x7f100101;
		public static final int Base_Widget_Material3_BottomNavigationView = 0x7f100102;
		public static final int Base_Widget_Material3_CardView = 0x7f100103;
		public static final int Base_Widget_Material3_Chip = 0x7f100104;
		public static final int Base_Widget_Material3_CollapsingToolbar = 0x7f100105;
		public static final int Base_Widget_Material3_CompoundButton_CheckBox = 0x7f100106;
		public static final int Base_Widget_Material3_CompoundButton_RadioButton = 0x7f100107;
		public static final int Base_Widget_Material3_CompoundButton_Switch = 0x7f100108;
		public static final int Base_Widget_Material3_ExtendedFloatingActionButton = 0x7f100109;
		public static final int Base_Widget_Material3_ExtendedFloatingActionButton_Icon = 0x7f10010a;
		public static final int Base_Widget_Material3_FloatingActionButton = 0x7f10010b;
		public static final int Base_Widget_Material3_FloatingActionButton_Large = 0x7f10010c;
		public static final int Base_Widget_Material3_FloatingActionButton_Small = 0x7f10010d;
		public static final int Base_Widget_Material3_Light_ActionBar_Solid = 0x7f10010e;
		public static final int Base_Widget_Material3_MaterialCalendar_NavigationButton = 0x7f10010f;
		public static final int Base_Widget_Material3_Snackbar = 0x7f100110;
		public static final int Base_Widget_Material3_TabLayout = 0x7f100111;
		public static final int Base_Widget_Material3_TabLayout_OnSurface = 0x7f100112;
		public static final int Base_Widget_Material3_TabLayout_Secondary = 0x7f100113;
		public static final int Base_Widget_MaterialComponents_AutoCompleteTextView = 0x7f100114;
		public static final int Base_Widget_MaterialComponents_CheckedTextView = 0x7f100115;
		public static final int Base_Widget_MaterialComponents_Chip = 0x7f100116;
		public static final int Base_Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton = 0x7f100117;
		public static final int Base_Widget_MaterialComponents_MaterialCalendar_NavigationButton = 0x7f100118;
		public static final int Base_Widget_MaterialComponents_PopupMenu = 0x7f100119;
		public static final int Base_Widget_MaterialComponents_PopupMenu_ContextMenu = 0x7f10011a;
		public static final int Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow = 0x7f10011b;
		public static final int Base_Widget_MaterialComponents_PopupMenu_Overflow = 0x7f10011c;
		public static final int Base_Widget_MaterialComponents_Slider = 0x7f10011d;
		public static final int Base_Widget_MaterialComponents_Snackbar = 0x7f10011e;
		public static final int Base_Widget_MaterialComponents_TextInputEditText = 0x7f10011f;
		public static final int Base_Widget_MaterialComponents_TextInputLayout = 0x7f100120;
		public static final int Base_Widget_MaterialComponents_TextView = 0x7f100121;
		public static final int CardView = 0x7f100122;
		public static final int CardView_Dark = 0x7f100123;
		public static final int CardView_Light = 0x7f100124;
		public static final int MaterialAlertDialog_Material3 = 0x7f100128;
		public static final int MaterialAlertDialog_Material3_Animation = 0x7f100129;
		public static final int MaterialAlertDialog_Material3_Body_Text = 0x7f10012a;
		public static final int MaterialAlertDialog_Material3_Body_Text_CenterStacked = 0x7f10012b;
		public static final int MaterialAlertDialog_Material3_Title_Icon = 0x7f10012c;
		public static final int MaterialAlertDialog_Material3_Title_Icon_CenterStacked = 0x7f10012d;
		public static final int MaterialAlertDialog_Material3_Title_Panel = 0x7f10012e;
		public static final int MaterialAlertDialog_Material3_Title_Panel_CenterStacked = 0x7f10012f;
		public static final int MaterialAlertDialog_Material3_Title_Text = 0x7f100130;
		public static final int MaterialAlertDialog_Material3_Title_Text_CenterStacked = 0x7f100131;
		public static final int MaterialAlertDialog_MaterialComponents = 0x7f100132;
		public static final int MaterialAlertDialog_MaterialComponents_Body_Text = 0x7f100133;
		public static final int MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar = 0x7f100134;
		public static final int MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner = 0x7f100135;
		public static final int MaterialAlertDialog_MaterialComponents_Title_Icon = 0x7f100136;
		public static final int MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked = 0x7f100137;
		public static final int MaterialAlertDialog_MaterialComponents_Title_Panel = 0x7f100138;
		public static final int MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked = 0x7f100139;
		public static final int MaterialAlertDialog_MaterialComponents_Title_Text = 0x7f10013a;
		public static final int MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked = 0x7f10013b;
		public static final int Platform_AppCompat = 0x7f100143;
		public static final int Platform_AppCompat_Light = 0x7f100144;
		public static final int Platform_MaterialComponents = 0x7f100145;
		public static final int Platform_MaterialComponents_Dialog = 0x7f100146;
		public static final int Platform_MaterialComponents_Light = 0x7f100147;
		public static final int Platform_MaterialComponents_Light_Dialog = 0x7f100148;
		public static final int Platform_ThemeOverlay_AppCompat = 0x7f100149;
		public static final int Platform_ThemeOverlay_AppCompat_Dark = 0x7f10014a;
		public static final int Platform_ThemeOverlay_AppCompat_Light = 0x7f10014b;
		public static final int Platform_V21_AppCompat = 0x7f10014c;
		public static final int Platform_V21_AppCompat_Light = 0x7f10014d;
		public static final int Platform_V25_AppCompat = 0x7f10014e;
		public static final int Platform_V25_AppCompat_Light = 0x7f10014f;
		public static final int Platform_Widget_AppCompat_Spinner = 0x7f100150;
		public static final int RtlOverlay_DialogWindowTitle_AppCompat = 0x7f100151;
		public static final int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 0x7f100152;
		public static final int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 0x7f100153;
		public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem = 0x7f100154;
		public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 0x7f100155;
		public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut = 0x7f100156;
		public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow = 0x7f100157;
		public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 0x7f100158;
		public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Title = 0x7f100159;
		public static final int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 0x7f10015f;
		public static final int RtlOverlay_Widget_AppCompat_Search_DropDown = 0x7f10015a;
		public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 0x7f10015b;
		public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 0x7f10015c;
		public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 0x7f10015d;
		public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 0x7f10015e;
		public static final int RtlUnderlay_Widget_AppCompat_ActionButton = 0x7f100160;
		public static final int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 0x7f100161;
		public static final int ShapeAppearanceOverlay_Material3_Button = 0x7f10018d;
		public static final int ShapeAppearanceOverlay_Material3_Chip = 0x7f10018e;
		public static final int ShapeAppearanceOverlay_Material3_Corner_Bottom = 0x7f10018f;
		public static final int ShapeAppearanceOverlay_Material3_Corner_Left = 0x7f100190;
		public static final int ShapeAppearanceOverlay_Material3_Corner_Right = 0x7f100191;
		public static final int ShapeAppearanceOverlay_Material3_Corner_Top = 0x7f100192;
		public static final int ShapeAppearanceOverlay_Material3_FloatingActionButton = 0x7f100193;
		public static final int ShapeAppearanceOverlay_Material3_NavigationView_Item = 0x7f100194;
		public static final int ShapeAppearanceOverlay_Material3_SearchBar = 0x7f100195;
		public static final int ShapeAppearanceOverlay_Material3_SearchView = 0x7f100196;
		public static final int ShapeAppearanceOverlay_MaterialAlertDialog_Material3 = 0x7f100197;
		public static final int ShapeAppearanceOverlay_MaterialComponents_BottomSheet = 0x7f100198;
		public static final int ShapeAppearanceOverlay_MaterialComponents_Chip = 0x7f100199;
		public static final int ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton = 0x7f10019a;
		public static final int ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton = 0x7f10019b;
		public static final int ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day = 0x7f10019c;
		public static final int ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen = 0x7f10019d;
		public static final int ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year = 0x7f10019e;
		public static final int ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox = 0x7f10019f;
		public static final int ShapeAppearance_M3_Comp_Badge_Large_Shape = 0x7f100162;
		public static final int ShapeAppearance_M3_Comp_Badge_Shape = 0x7f100163;
		public static final int ShapeAppearance_M3_Comp_BottomAppBar_Container_Shape = 0x7f100164;
		public static final int ShapeAppearance_M3_Comp_DatePicker_Modal_Date_Container_Shape = 0x7f100165;
		public static final int ShapeAppearance_M3_Comp_FilledButton_Container_Shape = 0x7f100166;
		public static final int ShapeAppearance_M3_Comp_NavigationBar_ActiveIndicator_Shape = 0x7f100167;
		public static final int ShapeAppearance_M3_Comp_NavigationBar_Container_Shape = 0x7f100168;
		public static final int ShapeAppearance_M3_Comp_NavigationDrawer_ActiveIndicator_Shape = 0x7f100169;
		public static final int ShapeAppearance_M3_Comp_NavigationRail_ActiveIndicator_Shape = 0x7f10016a;
		public static final int ShapeAppearance_M3_Comp_NavigationRail_Container_Shape = 0x7f10016b;
		public static final int ShapeAppearance_M3_Comp_SearchBar_Avatar_Shape = 0x7f10016c;
		public static final int ShapeAppearance_M3_Comp_SearchBar_Container_Shape = 0x7f10016d;
		public static final int ShapeAppearance_M3_Comp_SearchView_FullScreen_Container_Shape = 0x7f10016e;
		public static final int ShapeAppearance_M3_Comp_Sheet_Side_Docked_Container_Shape = 0x7f10016f;
		public static final int ShapeAppearance_M3_Comp_Switch_Handle_Shape = 0x7f100170;
		public static final int ShapeAppearance_M3_Comp_Switch_StateLayer_Shape = 0x7f100171;
		public static final int ShapeAppearance_M3_Comp_Switch_Track_Shape = 0x7f100172;
		public static final int ShapeAppearance_M3_Comp_TextButton_Container_Shape = 0x7f100173;
		public static final int ShapeAppearance_M3_Sys_Shape_Corner_ExtraLarge = 0x7f100174;
		public static final int ShapeAppearance_M3_Sys_Shape_Corner_ExtraSmall = 0x7f100175;
		public static final int ShapeAppearance_M3_Sys_Shape_Corner_Full = 0x7f100176;
		public static final int ShapeAppearance_M3_Sys_Shape_Corner_Large = 0x7f100177;
		public static final int ShapeAppearance_M3_Sys_Shape_Corner_Medium = 0x7f100178;
		public static final int ShapeAppearance_M3_Sys_Shape_Corner_None = 0x7f100179;
		public static final int ShapeAppearance_M3_Sys_Shape_Corner_Small = 0x7f10017a;
		public static final int ShapeAppearance_Material3_Corner_ExtraLarge = 0x7f10017b;
		public static final int ShapeAppearance_Material3_Corner_ExtraSmall = 0x7f10017c;
		public static final int ShapeAppearance_Material3_Corner_Full = 0x7f10017d;
		public static final int ShapeAppearance_Material3_Corner_Large = 0x7f10017e;
		public static final int ShapeAppearance_Material3_Corner_Medium = 0x7f10017f;
		public static final int ShapeAppearance_Material3_Corner_None = 0x7f100180;
		public static final int ShapeAppearance_Material3_Corner_Small = 0x7f100181;
		public static final int ShapeAppearance_Material3_LargeComponent = 0x7f100182;
		public static final int ShapeAppearance_Material3_MediumComponent = 0x7f100183;
		public static final int ShapeAppearance_Material3_NavigationBarView_ActiveIndicator = 0x7f100184;
		public static final int ShapeAppearance_Material3_SmallComponent = 0x7f100185;
		public static final int ShapeAppearance_Material3_Tooltip = 0x7f100186;
		public static final int ShapeAppearance_MaterialComponents = 0x7f100187;
		public static final int ShapeAppearance_MaterialComponents_Badge = 0x7f100188;
		public static final int ShapeAppearance_MaterialComponents_LargeComponent = 0x7f100189;
		public static final int ShapeAppearance_MaterialComponents_MediumComponent = 0x7f10018a;
		public static final int ShapeAppearance_MaterialComponents_SmallComponent = 0x7f10018b;
		public static final int ShapeAppearance_MaterialComponents_Tooltip = 0x7f10018c;
		public static final int TextAppearance_AppCompat = 0x7f1001a0;
		public static final int TextAppearance_AppCompat_Body1 = 0x7f1001a1;
		public static final int TextAppearance_AppCompat_Body2 = 0x7f1001a2;
		public static final int TextAppearance_AppCompat_Button = 0x7f1001a3;
		public static final int TextAppearance_AppCompat_Caption = 0x7f1001a4;
		public static final int TextAppearance_AppCompat_Display1 = 0x7f1001a5;
		public static final int TextAppearance_AppCompat_Display2 = 0x7f1001a6;
		public static final int TextAppearance_AppCompat_Display3 = 0x7f1001a7;
		public static final int TextAppearance_AppCompat_Display4 = 0x7f1001a8;
		public static final int TextAppearance_AppCompat_Headline = 0x7f1001a9;
		public static final int TextAppearance_AppCompat_Inverse = 0x7f1001aa;
		public static final int TextAppearance_AppCompat_Large = 0x7f1001ab;
		public static final int TextAppearance_AppCompat_Large_Inverse = 0x7f1001ac;
		public static final int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 0x7f1001ad;
		public static final int TextAppearance_AppCompat_Light_SearchResult_Title = 0x7f1001ae;
		public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f1001af;
		public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f1001b0;
		public static final int TextAppearance_AppCompat_Medium = 0x7f1001b1;
		public static final int TextAppearance_AppCompat_Medium_Inverse = 0x7f1001b2;
		public static final int TextAppearance_AppCompat_Menu = 0x7f1001b3;
		public static final int TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f1001b4;
		public static final int TextAppearance_AppCompat_SearchResult_Title = 0x7f1001b5;
		public static final int TextAppearance_AppCompat_Small = 0x7f1001b6;
		public static final int TextAppearance_AppCompat_Small_Inverse = 0x7f1001b7;
		public static final int TextAppearance_AppCompat_Subhead = 0x7f1001b8;
		public static final int TextAppearance_AppCompat_Subhead_Inverse = 0x7f1001b9;
		public static final int TextAppearance_AppCompat_Title = 0x7f1001ba;
		public static final int TextAppearance_AppCompat_Title_Inverse = 0x7f1001bb;
		public static final int TextAppearance_AppCompat_Tooltip = 0x7f1001bc;
		public static final int TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f1001bd;
		public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f1001be;
		public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f1001bf;
		public static final int TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f1001c0;
		public static final int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f1001c1;
		public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f1001c2;
		public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 0x7f1001c3;
		public static final int TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f1001c4;
		public static final int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 0x7f1001c5;
		public static final int TextAppearance_AppCompat_Widget_Button = 0x7f1001c6;
		public static final int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f1001c7;
		public static final int TextAppearance_AppCompat_Widget_Button_Colored = 0x7f1001c8;
		public static final int TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f1001c9;
		public static final int TextAppearance_AppCompat_Widget_DropDownItem = 0x7f1001ca;
		public static final int TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f1001cb;
		public static final int TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f1001cc;
		public static final int TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f1001cd;
		public static final int TextAppearance_AppCompat_Widget_Switch = 0x7f1001ce;
		public static final int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f1001cf;
		public static final int TextAppearance_Compat_Notification = 0x7f1001d0;
		public static final int TextAppearance_Compat_Notification_Info = 0x7f1001d1;
		public static final int TextAppearance_Compat_Notification_Line2 = 0x7f1001d2;
		public static final int TextAppearance_Compat_Notification_Time = 0x7f1001d3;
		public static final int TextAppearance_Compat_Notification_Title = 0x7f1001d4;
		public static final int TextAppearance_Design_CollapsingToolbar_Expanded = 0x7f1001d5;
		public static final int TextAppearance_Design_Counter = 0x7f1001d6;
		public static final int TextAppearance_Design_Counter_Overflow = 0x7f1001d7;
		public static final int TextAppearance_Design_Error = 0x7f1001d8;
		public static final int TextAppearance_Design_HelperText = 0x7f1001d9;
		public static final int TextAppearance_Design_Hint = 0x7f1001da;
		public static final int TextAppearance_Design_Placeholder = 0x7f1001db;
		public static final int TextAppearance_Design_Prefix = 0x7f1001dc;
		public static final int TextAppearance_Design_Snackbar_Message = 0x7f1001dd;
		public static final int TextAppearance_Design_Suffix = 0x7f1001de;
		public static final int TextAppearance_Design_Tab = 0x7f1001df;
		public static final int TextAppearance_M3_Sys_Typescale_BodyLarge = 0x7f1001e0;
		public static final int TextAppearance_M3_Sys_Typescale_BodyMedium = 0x7f1001e1;
		public static final int TextAppearance_M3_Sys_Typescale_BodySmall = 0x7f1001e2;
		public static final int TextAppearance_M3_Sys_Typescale_DisplayLarge = 0x7f1001e3;
		public static final int TextAppearance_M3_Sys_Typescale_DisplayMedium = 0x7f1001e4;
		public static final int TextAppearance_M3_Sys_Typescale_DisplaySmall = 0x7f1001e5;
		public static final int TextAppearance_M3_Sys_Typescale_HeadlineLarge = 0x7f1001e6;
		public static final int TextAppearance_M3_Sys_Typescale_HeadlineMedium = 0x7f1001e7;
		public static final int TextAppearance_M3_Sys_Typescale_HeadlineSmall = 0x7f1001e8;
		public static final int TextAppearance_M3_Sys_Typescale_LabelLarge = 0x7f1001e9;
		public static final int TextAppearance_M3_Sys_Typescale_LabelMedium = 0x7f1001ea;
		public static final int TextAppearance_M3_Sys_Typescale_LabelSmall = 0x7f1001eb;
		public static final int TextAppearance_M3_Sys_Typescale_TitleLarge = 0x7f1001ec;
		public static final int TextAppearance_M3_Sys_Typescale_TitleMedium = 0x7f1001ed;
		public static final int TextAppearance_M3_Sys_Typescale_TitleSmall = 0x7f1001ee;
		public static final int TextAppearance_Material3_ActionBar_Subtitle = 0x7f1001ef;
		public static final int TextAppearance_Material3_ActionBar_Title = 0x7f1001f0;
		public static final int TextAppearance_Material3_BodyLarge = 0x7f1001f1;
		public static final int TextAppearance_Material3_BodyMedium = 0x7f1001f2;
		public static final int TextAppearance_Material3_BodySmall = 0x7f1001f3;
		public static final int TextAppearance_Material3_DisplayLarge = 0x7f1001f4;
		public static final int TextAppearance_Material3_DisplayMedium = 0x7f1001f5;
		public static final int TextAppearance_Material3_DisplaySmall = 0x7f1001f6;
		public static final int TextAppearance_Material3_HeadlineLarge = 0x7f1001f7;
		public static final int TextAppearance_Material3_HeadlineMedium = 0x7f1001f8;
		public static final int TextAppearance_Material3_HeadlineSmall = 0x7f1001f9;
		public static final int TextAppearance_Material3_LabelLarge = 0x7f1001fa;
		public static final int TextAppearance_Material3_LabelMedium = 0x7f1001fb;
		public static final int TextAppearance_Material3_LabelSmall = 0x7f1001fc;
		public static final int TextAppearance_Material3_MaterialTimePicker_Title = 0x7f1001fd;
		public static final int TextAppearance_Material3_SearchBar = 0x7f1001fe;
		public static final int TextAppearance_Material3_SearchView = 0x7f1001ff;
		public static final int TextAppearance_Material3_SearchView_Prefix = 0x7f100200;
		public static final int TextAppearance_Material3_TitleLarge = 0x7f100201;
		public static final int TextAppearance_Material3_TitleMedium = 0x7f100202;
		public static final int TextAppearance_Material3_TitleSmall = 0x7f100203;
		public static final int TextAppearance_MaterialComponents_Badge = 0x7f100204;
		public static final int TextAppearance_MaterialComponents_Body1 = 0x7f100205;
		public static final int TextAppearance_MaterialComponents_Body2 = 0x7f100206;
		public static final int TextAppearance_MaterialComponents_Button = 0x7f100207;
		public static final int TextAppearance_MaterialComponents_Caption = 0x7f100208;
		public static final int TextAppearance_MaterialComponents_Chip = 0x7f100209;
		public static final int TextAppearance_MaterialComponents_Headline1 = 0x7f10020a;
		public static final int TextAppearance_MaterialComponents_Headline2 = 0x7f10020b;
		public static final int TextAppearance_MaterialComponents_Headline3 = 0x7f10020c;
		public static final int TextAppearance_MaterialComponents_Headline4 = 0x7f10020d;
		public static final int TextAppearance_MaterialComponents_Headline5 = 0x7f10020e;
		public static final int TextAppearance_MaterialComponents_Headline6 = 0x7f10020f;
		public static final int TextAppearance_MaterialComponents_Overline = 0x7f100210;
		public static final int TextAppearance_MaterialComponents_Subtitle1 = 0x7f100211;
		public static final int TextAppearance_MaterialComponents_Subtitle2 = 0x7f100212;
		public static final int TextAppearance_MaterialComponents_TimePicker_Title = 0x7f100213;
		public static final int TextAppearance_MaterialComponents_Tooltip = 0x7f100214;
		public static final int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f100215;
		public static final int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f100216;
		public static final int TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f100217;
		public static final int ThemeOverlay_AppCompat = 0x7f100281;
		public static final int ThemeOverlay_AppCompat_ActionBar = 0x7f100282;
		public static final int ThemeOverlay_AppCompat_Dark = 0x7f100283;
		public static final int ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f100284;
		public static final int ThemeOverlay_AppCompat_DayNight = 0x7f100285;
		public static final int ThemeOverlay_AppCompat_DayNight_ActionBar = 0x7f100286;
		public static final int ThemeOverlay_AppCompat_Dialog = 0x7f100287;
		public static final int ThemeOverlay_AppCompat_Dialog_Alert = 0x7f100288;
		public static final int ThemeOverlay_AppCompat_Light = 0x7f100289;
		public static final int ThemeOverlay_Design_TextInputEditText = 0x7f10028a;
		public static final int ThemeOverlay_Material3 = 0x7f10028b;
		public static final int ThemeOverlay_Material3_ActionBar = 0x7f10028c;
		public static final int ThemeOverlay_Material3_AutoCompleteTextView = 0x7f10028d;
		public static final int ThemeOverlay_Material3_AutoCompleteTextView_FilledBox = 0x7f10028e;
		public static final int ThemeOverlay_Material3_AutoCompleteTextView_FilledBox_Dense = 0x7f10028f;
		public static final int ThemeOverlay_Material3_AutoCompleteTextView_OutlinedBox = 0x7f100290;
		public static final int ThemeOverlay_Material3_AutoCompleteTextView_OutlinedBox_Dense = 0x7f100291;
		public static final int ThemeOverlay_Material3_BottomAppBar = 0x7f100292;
		public static final int ThemeOverlay_Material3_BottomAppBar_Legacy = 0x7f100293;
		public static final int ThemeOverlay_Material3_BottomNavigationView = 0x7f100294;
		public static final int ThemeOverlay_Material3_BottomSheetDialog = 0x7f100295;
		public static final int ThemeOverlay_Material3_Button = 0x7f100296;
		public static final int ThemeOverlay_Material3_Button_ElevatedButton = 0x7f100297;
		public static final int ThemeOverlay_Material3_Button_IconButton = 0x7f100298;
		public static final int ThemeOverlay_Material3_Button_IconButton_Filled = 0x7f100299;
		public static final int ThemeOverlay_Material3_Button_IconButton_Filled_Tonal = 0x7f10029a;
		public static final int ThemeOverlay_Material3_Button_TextButton = 0x7f10029b;
		public static final int ThemeOverlay_Material3_Button_TextButton_Snackbar = 0x7f10029c;
		public static final int ThemeOverlay_Material3_Button_TonalButton = 0x7f10029d;
		public static final int ThemeOverlay_Material3_Chip = 0x7f10029e;
		public static final int ThemeOverlay_Material3_Chip_Assist = 0x7f10029f;
		public static final int ThemeOverlay_Material3_Dark = 0x7f1002a0;
		public static final int ThemeOverlay_Material3_Dark_ActionBar = 0x7f1002a1;
		public static final int ThemeOverlay_Material3_DayNight_BottomSheetDialog = 0x7f1002a2;
		public static final int ThemeOverlay_Material3_DayNight_SideSheetDialog = 0x7f1002a3;
		public static final int ThemeOverlay_Material3_Dialog = 0x7f1002a4;
		public static final int ThemeOverlay_Material3_Dialog_Alert = 0x7f1002a5;
		public static final int ThemeOverlay_Material3_Dialog_Alert_Framework = 0x7f1002a6;
		public static final int ThemeOverlay_Material3_DynamicColors_Dark = 0x7f1002a7;
		public static final int ThemeOverlay_Material3_DynamicColors_DayNight = 0x7f1002a8;
		public static final int ThemeOverlay_Material3_DynamicColors_Light = 0x7f1002a9;
		public static final int ThemeOverlay_Material3_ExtendedFloatingActionButton_Primary = 0x7f1002aa;
		public static final int ThemeOverlay_Material3_ExtendedFloatingActionButton_Secondary = 0x7f1002ab;
		public static final int ThemeOverlay_Material3_ExtendedFloatingActionButton_Surface = 0x7f1002ac;
		public static final int ThemeOverlay_Material3_ExtendedFloatingActionButton_Tertiary = 0x7f1002ad;
		public static final int ThemeOverlay_Material3_FloatingActionButton_Primary = 0x7f1002ae;
		public static final int ThemeOverlay_Material3_FloatingActionButton_Secondary = 0x7f1002af;
		public static final int ThemeOverlay_Material3_FloatingActionButton_Surface = 0x7f1002b0;
		public static final int ThemeOverlay_Material3_FloatingActionButton_Tertiary = 0x7f1002b1;
		public static final int ThemeOverlay_Material3_HarmonizedColors = 0x7f1002b2;
		public static final int ThemeOverlay_Material3_HarmonizedColors_Empty = 0x7f1002b3;
		public static final int ThemeOverlay_Material3_Light = 0x7f1002b4;
		public static final int ThemeOverlay_Material3_Light_Dialog_Alert_Framework = 0x7f1002b5;
		public static final int ThemeOverlay_Material3_MaterialAlertDialog = 0x7f1002b6;
		public static final int ThemeOverlay_Material3_MaterialAlertDialog_Centered = 0x7f1002b7;
		public static final int ThemeOverlay_Material3_MaterialCalendar = 0x7f1002b8;
		public static final int ThemeOverlay_Material3_MaterialCalendar_Fullscreen = 0x7f1002b9;
		public static final int ThemeOverlay_Material3_MaterialCalendar_HeaderCancelButton = 0x7f1002ba;
		public static final int ThemeOverlay_Material3_MaterialTimePicker = 0x7f1002bb;
		public static final int ThemeOverlay_Material3_MaterialTimePicker_Display_TextInputEditText = 0x7f1002bc;
		public static final int ThemeOverlay_Material3_NavigationRailView = 0x7f1002bd;
		public static final int ThemeOverlay_Material3_NavigationView = 0x7f1002be;
		public static final int ThemeOverlay_Material3_PersonalizedColors = 0x7f1002bf;
		public static final int ThemeOverlay_Material3_Search = 0x7f1002c0;
		public static final int ThemeOverlay_Material3_SideSheetDialog = 0x7f1002c1;
		public static final int ThemeOverlay_Material3_Snackbar = 0x7f1002c2;
		public static final int ThemeOverlay_Material3_TabLayout = 0x7f1002c3;
		public static final int ThemeOverlay_Material3_TextInputEditText = 0x7f1002c4;
		public static final int ThemeOverlay_Material3_TextInputEditText_FilledBox = 0x7f1002c5;
		public static final int ThemeOverlay_Material3_TextInputEditText_FilledBox_Dense = 0x7f1002c6;
		public static final int ThemeOverlay_Material3_TextInputEditText_OutlinedBox = 0x7f1002c7;
		public static final int ThemeOverlay_Material3_TextInputEditText_OutlinedBox_Dense = 0x7f1002c8;
		public static final int ThemeOverlay_Material3_Toolbar_Surface = 0x7f1002c9;
		public static final int ThemeOverlay_MaterialAlertDialog_Material3_Title_Icon = 0x7f1002ca;
		public static final int ThemeOverlay_MaterialComponents = 0x7f1002cb;
		public static final int ThemeOverlay_MaterialComponents_ActionBar = 0x7f1002cc;
		public static final int ThemeOverlay_MaterialComponents_ActionBar_Primary = 0x7f1002cd;
		public static final int ThemeOverlay_MaterialComponents_ActionBar_Surface = 0x7f1002ce;
		public static final int ThemeOverlay_MaterialComponents_AutoCompleteTextView = 0x7f1002cf;
		public static final int ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox = 0x7f1002d0;
		public static final int ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense = 0x7f1002d1;
		public static final int ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox = 0x7f1002d2;
		public static final int ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense = 0x7f1002d3;
		public static final int ThemeOverlay_MaterialComponents_BottomAppBar_Primary = 0x7f1002d4;
		public static final int ThemeOverlay_MaterialComponents_BottomAppBar_Surface = 0x7f1002d5;
		public static final int ThemeOverlay_MaterialComponents_BottomSheetDialog = 0x7f1002d6;
		public static final int ThemeOverlay_MaterialComponents_Dark = 0x7f1002d7;
		public static final int ThemeOverlay_MaterialComponents_Dark_ActionBar = 0x7f1002d8;
		public static final int ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog = 0x7f1002d9;
		public static final int ThemeOverlay_MaterialComponents_Dialog = 0x7f1002da;
		public static final int ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f1002db;
		public static final int ThemeOverlay_MaterialComponents_Dialog_Alert_Framework = 0x7f1002dc;
		public static final int ThemeOverlay_MaterialComponents_Light = 0x7f1002dd;
		public static final int ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework = 0x7f1002de;
		public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog = 0x7f1002df;
		public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered = 0x7f1002e0;
		public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date = 0x7f1002e1;
		public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar = 0x7f1002e2;
		public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text = 0x7f1002e3;
		public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day = 0x7f1002e4;
		public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner = 0x7f1002e5;
		public static final int ThemeOverlay_MaterialComponents_MaterialCalendar = 0x7f1002e6;
		public static final int ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen = 0x7f1002e7;
		public static final int ThemeOverlay_MaterialComponents_TextInputEditText = 0x7f1002e8;
		public static final int ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox = 0x7f1002e9;
		public static final int ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense = 0x7f1002ea;
		public static final int ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox = 0x7f1002eb;
		public static final int ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense = 0x7f1002ec;
		public static final int ThemeOverlay_MaterialComponents_TimePicker = 0x7f1002ed;
		public static final int ThemeOverlay_MaterialComponents_TimePicker_Display = 0x7f1002ee;
		public static final int ThemeOverlay_MaterialComponents_TimePicker_Display_TextInputEditText = 0x7f1002ef;
		public static final int ThemeOverlay_MaterialComponents_Toolbar_Popup_Primary = 0x7f1002f0;
		public static final int ThemeOverlay_MaterialComponents_Toolbar_Primary = 0x7f1002f1;
		public static final int ThemeOverlay_MaterialComponents_Toolbar_Surface = 0x7f1002f2;
		public static final int Theme_AppCompat = 0x7f100218;
		public static final int Theme_AppCompat_CompactMenu = 0x7f100219;
		public static final int Theme_AppCompat_DayNight = 0x7f10021a;
		public static final int Theme_AppCompat_DayNight_DarkActionBar = 0x7f10021b;
		public static final int Theme_AppCompat_DayNight_Dialog = 0x7f10021c;
		public static final int Theme_AppCompat_DayNight_DialogWhenLarge = 0x7f10021f;
		public static final int Theme_AppCompat_DayNight_Dialog_Alert = 0x7f10021d;
		public static final int Theme_AppCompat_DayNight_Dialog_MinWidth = 0x7f10021e;
		public static final int Theme_AppCompat_DayNight_NoActionBar = 0x7f100220;
		public static final int Theme_AppCompat_Dialog = 0x7f100221;
		public static final int Theme_AppCompat_DialogWhenLarge = 0x7f100224;
		public static final int Theme_AppCompat_Dialog_Alert = 0x7f100222;
		public static final int Theme_AppCompat_Dialog_MinWidth = 0x7f100223;
		public static final int Theme_AppCompat_Empty = 0x7f100225;
		public static final int Theme_AppCompat_Light = 0x7f100226;
		public static final int Theme_AppCompat_Light_DarkActionBar = 0x7f100227;
		public static final int Theme_AppCompat_Light_Dialog = 0x7f100228;
		public static final int Theme_AppCompat_Light_DialogWhenLarge = 0x7f10022b;
		public static final int Theme_AppCompat_Light_Dialog_Alert = 0x7f100229;
		public static final int Theme_AppCompat_Light_Dialog_MinWidth = 0x7f10022a;
		public static final int Theme_AppCompat_Light_NoActionBar = 0x7f10022c;
		public static final int Theme_AppCompat_NoActionBar = 0x7f10022d;
		public static final int Theme_Design = 0x7f10022e;
		public static final int Theme_Design_BottomSheetDialog = 0x7f10022f;
		public static final int Theme_Design_Light = 0x7f100230;
		public static final int Theme_Design_Light_BottomSheetDialog = 0x7f100231;
		public static final int Theme_Design_Light_NoActionBar = 0x7f100232;
		public static final int Theme_Design_NoActionBar = 0x7f100233;
		public static final int Theme_Material3_Dark = 0x7f100234;
		public static final int Theme_Material3_Dark_BottomSheetDialog = 0x7f100235;
		public static final int Theme_Material3_Dark_Dialog = 0x7f100236;
		public static final int Theme_Material3_Dark_DialogWhenLarge = 0x7f100239;
		public static final int Theme_Material3_Dark_Dialog_Alert = 0x7f100237;
		public static final int Theme_Material3_Dark_Dialog_MinWidth = 0x7f100238;
		public static final int Theme_Material3_Dark_NoActionBar = 0x7f10023a;
		public static final int Theme_Material3_Dark_SideSheetDialog = 0x7f10023b;
		public static final int Theme_Material3_DayNight = 0x7f10023c;
		public static final int Theme_Material3_DayNight_BottomSheetDialog = 0x7f10023d;
		public static final int Theme_Material3_DayNight_Dialog = 0x7f10023e;
		public static final int Theme_Material3_DayNight_DialogWhenLarge = 0x7f100241;
		public static final int Theme_Material3_DayNight_Dialog_Alert = 0x7f10023f;
		public static final int Theme_Material3_DayNight_Dialog_MinWidth = 0x7f100240;
		public static final int Theme_Material3_DayNight_NoActionBar = 0x7f100242;
		public static final int Theme_Material3_DayNight_SideSheetDialog = 0x7f100243;
		public static final int Theme_Material3_DynamicColors_Dark = 0x7f100244;
		public static final int Theme_Material3_DynamicColors_DayNight = 0x7f100246;
		public static final int Theme_Material3_DynamicColors_Light = 0x7f100248;
		public static final int Theme_Material3_Light = 0x7f10024a;
		public static final int Theme_Material3_Light_BottomSheetDialog = 0x7f10024b;
		public static final int Theme_Material3_Light_Dialog = 0x7f10024c;
		public static final int Theme_Material3_Light_DialogWhenLarge = 0x7f10024f;
		public static final int Theme_Material3_Light_Dialog_Alert = 0x7f10024d;
		public static final int Theme_Material3_Light_Dialog_MinWidth = 0x7f10024e;
		public static final int Theme_Material3_Light_NoActionBar = 0x7f100250;
		public static final int Theme_Material3_Light_SideSheetDialog = 0x7f100251;
		public static final int Theme_MaterialComponents = 0x7f100252;
		public static final int Theme_MaterialComponents_BottomSheetDialog = 0x7f100253;
		public static final int Theme_MaterialComponents_Bridge = 0x7f100254;
		public static final int Theme_MaterialComponents_CompactMenu = 0x7f100255;
		public static final int Theme_MaterialComponents_DayNight = 0x7f100256;
		public static final int Theme_MaterialComponents_DayNight_BottomSheetDialog = 0x7f100257;
		public static final int Theme_MaterialComponents_DayNight_Bridge = 0x7f100258;
		public static final int Theme_MaterialComponents_DayNight_DarkActionBar = 0x7f100259;
		public static final int Theme_MaterialComponents_DayNight_DarkActionBar_Bridge = 0x7f10025a;
		public static final int Theme_MaterialComponents_DayNight_Dialog = 0x7f10025b;
		public static final int Theme_MaterialComponents_DayNight_DialogWhenLarge = 0x7f100263;
		public static final int Theme_MaterialComponents_DayNight_Dialog_Alert = 0x7f10025c;
		public static final int Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge = 0x7f10025d;
		public static final int Theme_MaterialComponents_DayNight_Dialog_Bridge = 0x7f10025e;
		public static final int Theme_MaterialComponents_DayNight_Dialog_FixedSize = 0x7f10025f;
		public static final int Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge = 0x7f100260;
		public static final int Theme_MaterialComponents_DayNight_Dialog_MinWidth = 0x7f100261;
		public static final int Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge = 0x7f100262;
		public static final int Theme_MaterialComponents_DayNight_NoActionBar = 0x7f100264;
		public static final int Theme_MaterialComponents_DayNight_NoActionBar_Bridge = 0x7f100265;
		public static final int Theme_MaterialComponents_Dialog = 0x7f100266;
		public static final int Theme_MaterialComponents_DialogWhenLarge = 0x7f10026e;
		public static final int Theme_MaterialComponents_Dialog_Alert = 0x7f100267;
		public static final int Theme_MaterialComponents_Dialog_Alert_Bridge = 0x7f100268;
		public static final int Theme_MaterialComponents_Dialog_Bridge = 0x7f100269;
		public static final int Theme_MaterialComponents_Dialog_FixedSize = 0x7f10026a;
		public static final int Theme_MaterialComponents_Dialog_FixedSize_Bridge = 0x7f10026b;
		public static final int Theme_MaterialComponents_Dialog_MinWidth = 0x7f10026c;
		public static final int Theme_MaterialComponents_Dialog_MinWidth_Bridge = 0x7f10026d;
		public static final int Theme_MaterialComponents_Light = 0x7f10026f;
		public static final int Theme_MaterialComponents_Light_BottomSheetDialog = 0x7f100270;
		public static final int Theme_MaterialComponents_Light_Bridge = 0x7f100271;
		public static final int Theme_MaterialComponents_Light_DarkActionBar = 0x7f100272;
		public static final int Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f100273;
		public static final int Theme_MaterialComponents_Light_Dialog = 0x7f100274;
		public static final int Theme_MaterialComponents_Light_DialogWhenLarge = 0x7f10027c;
		public static final int Theme_MaterialComponents_Light_Dialog_Alert = 0x7f100275;
		public static final int Theme_MaterialComponents_Light_Dialog_Alert_Bridge = 0x7f100276;
		public static final int Theme_MaterialComponents_Light_Dialog_Bridge = 0x7f100277;
		public static final int Theme_MaterialComponents_Light_Dialog_FixedSize = 0x7f100278;
		public static final int Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge = 0x7f100279;
		public static final int Theme_MaterialComponents_Light_Dialog_MinWidth = 0x7f10027a;
		public static final int Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge = 0x7f10027b;
		public static final int Theme_MaterialComponents_Light_NoActionBar = 0x7f10027d;
		public static final int Theme_MaterialComponents_Light_NoActionBar_Bridge = 0x7f10027e;
		public static final int Theme_MaterialComponents_NoActionBar = 0x7f10027f;
		public static final int Theme_MaterialComponents_NoActionBar_Bridge = 0x7f100280;
		public static final int Widget_AppCompat_ActionBar = 0x7f1002f3;
		public static final int Widget_AppCompat_ActionBar_Solid = 0x7f1002f4;
		public static final int Widget_AppCompat_ActionBar_TabBar = 0x7f1002f5;
		public static final int Widget_AppCompat_ActionBar_TabText = 0x7f1002f6;
		public static final int Widget_AppCompat_ActionBar_TabView = 0x7f1002f7;
		public static final int Widget_AppCompat_ActionButton = 0x7f1002f8;
		public static final int Widget_AppCompat_ActionButton_CloseMode = 0x7f1002f9;
		public static final int Widget_AppCompat_ActionButton_Overflow = 0x7f1002fa;
		public static final int Widget_AppCompat_ActionMode = 0x7f1002fb;
		public static final int Widget_AppCompat_ActivityChooserView = 0x7f1002fc;
		public static final int Widget_AppCompat_AutoCompleteTextView = 0x7f1002fd;
		public static final int Widget_AppCompat_Button = 0x7f1002fe;
		public static final int Widget_AppCompat_ButtonBar = 0x7f100304;
		public static final int Widget_AppCompat_ButtonBar_AlertDialog = 0x7f100305;
		public static final int Widget_AppCompat_Button_Borderless = 0x7f1002ff;
		public static final int Widget_AppCompat_Button_Borderless_Colored = 0x7f100300;
		public static final int Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f100301;
		public static final int Widget_AppCompat_Button_Colored = 0x7f100302;
		public static final int Widget_AppCompat_Button_Small = 0x7f100303;
		public static final int Widget_AppCompat_CompoundButton_CheckBox = 0x7f100306;
		public static final int Widget_AppCompat_CompoundButton_RadioButton = 0x7f100307;
		public static final int Widget_AppCompat_CompoundButton_Switch = 0x7f100308;
		public static final int Widget_AppCompat_DrawerArrowToggle = 0x7f100309;
		public static final int Widget_AppCompat_DropDownItem_Spinner = 0x7f10030a;
		public static final int Widget_AppCompat_EditText = 0x7f10030b;
		public static final int Widget_AppCompat_ImageButton = 0x7f10030c;
		public static final int Widget_AppCompat_Light_ActionBar = 0x7f10030d;
		public static final int Widget_AppCompat_Light_ActionBar_Solid = 0x7f10030e;
		public static final int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 0x7f10030f;
		public static final int Widget_AppCompat_Light_ActionBar_TabBar = 0x7f100310;
		public static final int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 0x7f100311;
		public static final int Widget_AppCompat_Light_ActionBar_TabText = 0x7f100312;
		public static final int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f100313;
		public static final int Widget_AppCompat_Light_ActionBar_TabView = 0x7f100314;
		public static final int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 0x7f100315;
		public static final int Widget_AppCompat_Light_ActionButton = 0x7f100316;
		public static final int Widget_AppCompat_Light_ActionButton_CloseMode = 0x7f100317;
		public static final int Widget_AppCompat_Light_ActionButton_Overflow = 0x7f100318;
		public static final int Widget_AppCompat_Light_ActionMode_Inverse = 0x7f100319;
		public static final int Widget_AppCompat_Light_ActivityChooserView = 0x7f10031a;
		public static final int Widget_AppCompat_Light_AutoCompleteTextView = 0x7f10031b;
		public static final int Widget_AppCompat_Light_DropDownItem_Spinner = 0x7f10031c;
		public static final int Widget_AppCompat_Light_ListPopupWindow = 0x7f10031d;
		public static final int Widget_AppCompat_Light_ListView_DropDown = 0x7f10031e;
		public static final int Widget_AppCompat_Light_PopupMenu = 0x7f10031f;
		public static final int Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f100320;
		public static final int Widget_AppCompat_Light_SearchView = 0x7f100321;
		public static final int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 0x7f100322;
		public static final int Widget_AppCompat_ListMenuView = 0x7f100323;
		public static final int Widget_AppCompat_ListPopupWindow = 0x7f100324;
		public static final int Widget_AppCompat_ListView = 0x7f100325;
		public static final int Widget_AppCompat_ListView_DropDown = 0x7f100326;
		public static final int Widget_AppCompat_ListView_Menu = 0x7f100327;
		public static final int Widget_AppCompat_PopupMenu = 0x7f100328;
		public static final int Widget_AppCompat_PopupMenu_Overflow = 0x7f100329;
		public static final int Widget_AppCompat_PopupWindow = 0x7f10032a;
		public static final int Widget_AppCompat_ProgressBar = 0x7f10032b;
		public static final int Widget_AppCompat_ProgressBar_Horizontal = 0x7f10032c;
		public static final int Widget_AppCompat_RatingBar = 0x7f10032d;
		public static final int Widget_AppCompat_RatingBar_Indicator = 0x7f10032e;
		public static final int Widget_AppCompat_RatingBar_Small = 0x7f10032f;
		public static final int Widget_AppCompat_SearchView = 0x7f100330;
		public static final int Widget_AppCompat_SearchView_ActionBar = 0x7f100331;
		public static final int Widget_AppCompat_SeekBar = 0x7f100332;
		public static final int Widget_AppCompat_SeekBar_Discrete = 0x7f100333;
		public static final int Widget_AppCompat_Spinner = 0x7f100334;
		public static final int Widget_AppCompat_Spinner_DropDown = 0x7f100335;
		public static final int Widget_AppCompat_Spinner_DropDown_ActionBar = 0x7f100336;
		public static final int Widget_AppCompat_Spinner_Underlined = 0x7f100337;
		public static final int Widget_AppCompat_TextView = 0x7f100338;
		public static final int Widget_AppCompat_TextView_SpinnerItem = 0x7f100339;
		public static final int Widget_AppCompat_Toolbar = 0x7f10033a;
		public static final int Widget_AppCompat_Toolbar_Button_Navigation = 0x7f10033b;
		public static final int Widget_Compat_NotificationActionContainer = 0x7f10033c;
		public static final int Widget_Compat_NotificationActionText = 0x7f10033d;
		public static final int Widget_Design_AppBarLayout = 0x7f10033e;
		public static final int Widget_Design_BottomNavigationView = 0x7f10033f;
		public static final int Widget_Design_BottomSheet_Modal = 0x7f100340;
		public static final int Widget_Design_CollapsingToolbar = 0x7f100341;
		public static final int Widget_Design_FloatingActionButton = 0x7f100342;
		public static final int Widget_Design_NavigationView = 0x7f100343;
		public static final int Widget_Design_ScrimInsetsFrameLayout = 0x7f100344;
		public static final int Widget_Design_Snackbar = 0x7f100345;
		public static final int Widget_Design_TabLayout = 0x7f100346;
		public static final int Widget_Design_TextInputEditText = 0x7f100347;
		public static final int Widget_Design_TextInputLayout = 0x7f100348;
		public static final int Widget_Material3_ActionBar_Solid = 0x7f100349;
		public static final int Widget_Material3_ActionMode = 0x7f10034a;
		public static final int Widget_Material3_AppBarLayout = 0x7f10034b;
		public static final int Widget_Material3_AutoCompleteTextView_FilledBox = 0x7f10034c;
		public static final int Widget_Material3_AutoCompleteTextView_FilledBox_Dense = 0x7f10034d;
		public static final int Widget_Material3_AutoCompleteTextView_OutlinedBox = 0x7f10034e;
		public static final int Widget_Material3_AutoCompleteTextView_OutlinedBox_Dense = 0x7f10034f;
		public static final int Widget_Material3_Badge = 0x7f100350;
		public static final int Widget_Material3_Badge_AdjustToBounds = 0x7f100351;
		public static final int Widget_Material3_BottomAppBar = 0x7f100352;
		public static final int Widget_Material3_BottomAppBar_Button_Navigation = 0x7f100353;
		public static final int Widget_Material3_BottomAppBar_Legacy = 0x7f100354;
		public static final int Widget_Material3_BottomNavigationView = 0x7f100356;
		public static final int Widget_Material3_BottomNavigationView_ActiveIndicator = 0x7f100357;
		public static final int Widget_Material3_BottomNavigation_Badge = 0x7f100355;
		public static final int Widget_Material3_BottomSheet = 0x7f100358;
		public static final int Widget_Material3_BottomSheet_DragHandle = 0x7f100359;
		public static final int Widget_Material3_BottomSheet_Modal = 0x7f10035a;
		public static final int Widget_Material3_Button = 0x7f10035b;
		public static final int Widget_Material3_Button_ElevatedButton = 0x7f10035c;
		public static final int Widget_Material3_Button_ElevatedButton_Icon = 0x7f10035d;
		public static final int Widget_Material3_Button_Icon = 0x7f10035e;
		public static final int Widget_Material3_Button_IconButton = 0x7f10035f;
		public static final int Widget_Material3_Button_IconButton_Filled = 0x7f100360;
		public static final int Widget_Material3_Button_IconButton_Filled_Tonal = 0x7f100361;
		public static final int Widget_Material3_Button_IconButton_Outlined = 0x7f100362;
		public static final int Widget_Material3_Button_OutlinedButton = 0x7f100363;
		public static final int Widget_Material3_Button_OutlinedButton_Icon = 0x7f100364;
		public static final int Widget_Material3_Button_TextButton = 0x7f100365;
		public static final int Widget_Material3_Button_TextButton_Dialog = 0x7f100366;
		public static final int Widget_Material3_Button_TextButton_Dialog_Flush = 0x7f100367;
		public static final int Widget_Material3_Button_TextButton_Dialog_Icon = 0x7f100368;
		public static final int Widget_Material3_Button_TextButton_Icon = 0x7f100369;
		public static final int Widget_Material3_Button_TextButton_Snackbar = 0x7f10036a;
		public static final int Widget_Material3_Button_TonalButton = 0x7f10036b;
		public static final int Widget_Material3_Button_TonalButton_Icon = 0x7f10036c;
		public static final int Widget_Material3_Button_UnelevatedButton = 0x7f10036d;
		public static final int Widget_Material3_CardView_Elevated = 0x7f10036e;
		public static final int Widget_Material3_CardView_Filled = 0x7f10036f;
		public static final int Widget_Material3_CardView_Outlined = 0x7f100370;
		public static final int Widget_Material3_CheckedTextView = 0x7f100371;
		public static final int Widget_Material3_ChipGroup = 0x7f10037c;
		public static final int Widget_Material3_Chip_Assist = 0x7f100372;
		public static final int Widget_Material3_Chip_Assist_Elevated = 0x7f100373;
		public static final int Widget_Material3_Chip_Filter = 0x7f100374;
		public static final int Widget_Material3_Chip_Filter_Elevated = 0x7f100375;
		public static final int Widget_Material3_Chip_Input = 0x7f100376;
		public static final int Widget_Material3_Chip_Input_Elevated = 0x7f100377;
		public static final int Widget_Material3_Chip_Input_Icon = 0x7f100378;
		public static final int Widget_Material3_Chip_Input_Icon_Elevated = 0x7f100379;
		public static final int Widget_Material3_Chip_Suggestion = 0x7f10037a;
		public static final int Widget_Material3_Chip_Suggestion_Elevated = 0x7f10037b;
		public static final int Widget_Material3_CircularProgressIndicator = 0x7f10037d;
		public static final int Widget_Material3_CircularProgressIndicator_ExtraSmall = 0x7f10037e;
		public static final int Widget_Material3_CircularProgressIndicator_Medium = 0x7f100383;
		public static final int Widget_Material3_CircularProgressIndicator_Small = 0x7f100384;
		public static final int Widget_Material3_CollapsingToolbar = 0x7f100385;
		public static final int Widget_Material3_CollapsingToolbar_Large = 0x7f100386;
		public static final int Widget_Material3_CollapsingToolbar_Medium = 0x7f100387;
		public static final int Widget_Material3_CompoundButton_CheckBox = 0x7f100388;
		public static final int Widget_Material3_CompoundButton_MaterialSwitch = 0x7f100389;
		public static final int Widget_Material3_CompoundButton_RadioButton = 0x7f10038a;
		public static final int Widget_Material3_CompoundButton_Switch = 0x7f10038b;
		public static final int Widget_Material3_DrawerLayout = 0x7f10038c;
		public static final int Widget_Material3_ExtendedFloatingActionButton_Icon_Primary = 0x7f10038d;
		public static final int Widget_Material3_ExtendedFloatingActionButton_Icon_Secondary = 0x7f10038e;
		public static final int Widget_Material3_ExtendedFloatingActionButton_Icon_Surface = 0x7f10038f;
		public static final int Widget_Material3_ExtendedFloatingActionButton_Icon_Tertiary = 0x7f100390;
		public static final int Widget_Material3_ExtendedFloatingActionButton_Primary = 0x7f100391;
		public static final int Widget_Material3_ExtendedFloatingActionButton_Secondary = 0x7f100392;
		public static final int Widget_Material3_ExtendedFloatingActionButton_Surface = 0x7f100393;
		public static final int Widget_Material3_ExtendedFloatingActionButton_Tertiary = 0x7f100394;
		public static final int Widget_Material3_FloatingActionButton_Large_Primary = 0x7f100395;
		public static final int Widget_Material3_FloatingActionButton_Large_Secondary = 0x7f100396;
		public static final int Widget_Material3_FloatingActionButton_Large_Surface = 0x7f100397;
		public static final int Widget_Material3_FloatingActionButton_Large_Tertiary = 0x7f100398;
		public static final int Widget_Material3_FloatingActionButton_Primary = 0x7f100399;
		public static final int Widget_Material3_FloatingActionButton_Secondary = 0x7f10039a;
		public static final int Widget_Material3_FloatingActionButton_Small_Primary = 0x7f10039b;
		public static final int Widget_Material3_FloatingActionButton_Small_Secondary = 0x7f10039c;
		public static final int Widget_Material3_FloatingActionButton_Small_Surface = 0x7f10039d;
		public static final int Widget_Material3_FloatingActionButton_Small_Tertiary = 0x7f10039e;
		public static final int Widget_Material3_FloatingActionButton_Surface = 0x7f10039f;
		public static final int Widget_Material3_FloatingActionButton_Tertiary = 0x7f1003a0;
		public static final int Widget_Material3_Light_ActionBar_Solid = 0x7f1003a1;
		public static final int Widget_Material3_LinearProgressIndicator = 0x7f1003a2;
		public static final int Widget_Material3_MaterialButtonToggleGroup = 0x7f1003a4;
		public static final int Widget_Material3_MaterialCalendar = 0x7f1003a5;
		public static final int Widget_Material3_MaterialCalendar_Day = 0x7f1003a6;
		public static final int Widget_Material3_MaterialCalendar_DayOfWeekLabel = 0x7f1003aa;
		public static final int Widget_Material3_MaterialCalendar_DayTextView = 0x7f1003ab;
		public static final int Widget_Material3_MaterialCalendar_Day_Invalid = 0x7f1003a7;
		public static final int Widget_Material3_MaterialCalendar_Day_Selected = 0x7f1003a8;
		public static final int Widget_Material3_MaterialCalendar_Day_Today = 0x7f1003a9;
		public static final int Widget_Material3_MaterialCalendar_Fullscreen = 0x7f1003ac;
		public static final int Widget_Material3_MaterialCalendar_HeaderCancelButton = 0x7f1003ad;
		public static final int Widget_Material3_MaterialCalendar_HeaderDivider = 0x7f1003ae;
		public static final int Widget_Material3_MaterialCalendar_HeaderLayout = 0x7f1003af;
		public static final int Widget_Material3_MaterialCalendar_HeaderLayout_Fullscreen = 0x7f1003b0;
		public static final int Widget_Material3_MaterialCalendar_HeaderSelection = 0x7f1003b1;
		public static final int Widget_Material3_MaterialCalendar_HeaderSelection_Fullscreen = 0x7f1003b2;
		public static final int Widget_Material3_MaterialCalendar_HeaderTitle = 0x7f1003b3;
		public static final int Widget_Material3_MaterialCalendar_HeaderToggleButton = 0x7f1003b4;
		public static final int Widget_Material3_MaterialCalendar_Item = 0x7f1003b5;
		public static final int Widget_Material3_MaterialCalendar_MonthNavigationButton = 0x7f1003b6;
		public static final int Widget_Material3_MaterialCalendar_MonthTextView = 0x7f1003b7;
		public static final int Widget_Material3_MaterialCalendar_Year = 0x7f1003b8;
		public static final int Widget_Material3_MaterialCalendar_YearNavigationButton = 0x7f1003bb;
		public static final int Widget_Material3_MaterialCalendar_Year_Selected = 0x7f1003b9;
		public static final int Widget_Material3_MaterialCalendar_Year_Today = 0x7f1003ba;
		public static final int Widget_Material3_MaterialDivider = 0x7f1003bc;
		public static final int Widget_Material3_MaterialDivider_Heavy = 0x7f1003bd;
		public static final int Widget_Material3_MaterialTimePicker = 0x7f1003be;
		public static final int Widget_Material3_MaterialTimePicker_Button = 0x7f1003bf;
		public static final int Widget_Material3_MaterialTimePicker_Clock = 0x7f1003c0;
		public static final int Widget_Material3_MaterialTimePicker_Display = 0x7f1003c1;
		public static final int Widget_Material3_MaterialTimePicker_Display_Divider = 0x7f1003c2;
		public static final int Widget_Material3_MaterialTimePicker_Display_HelperText = 0x7f1003c3;
		public static final int Widget_Material3_MaterialTimePicker_Display_TextInputEditText = 0x7f1003c4;
		public static final int Widget_Material3_MaterialTimePicker_Display_TextInputLayout = 0x7f1003c5;
		public static final int Widget_Material3_MaterialTimePicker_ImageButton = 0x7f1003c6;
		public static final int Widget_Material3_NavigationRailView = 0x7f1003c7;
		public static final int Widget_Material3_NavigationRailView_ActiveIndicator = 0x7f1003c8;
		public static final int Widget_Material3_NavigationRailView_Badge = 0x7f1003c9;
		public static final int Widget_Material3_NavigationView = 0x7f1003ca;
		public static final int Widget_Material3_PopupMenu = 0x7f1003cb;
		public static final int Widget_Material3_PopupMenu_ContextMenu = 0x7f1003cc;
		public static final int Widget_Material3_PopupMenu_ListPopupWindow = 0x7f1003cd;
		public static final int Widget_Material3_PopupMenu_Overflow = 0x7f1003ce;
		public static final int Widget_Material3_SearchBar = 0x7f1003d1;
		public static final int Widget_Material3_SearchBar_Outlined = 0x7f1003d2;
		public static final int Widget_Material3_SearchView = 0x7f1003d3;
		public static final int Widget_Material3_SearchView_Prefix = 0x7f1003d4;
		public static final int Widget_Material3_SearchView_Toolbar = 0x7f1003d5;
		public static final int Widget_Material3_Search_ActionButton_Overflow = 0x7f1003cf;
		public static final int Widget_Material3_Search_Toolbar_Button_Navigation = 0x7f1003d0;
		public static final int Widget_Material3_SideSheet = 0x7f1003d6;
		public static final int Widget_Material3_SideSheet_Detached = 0x7f1003d7;
		public static final int Widget_Material3_SideSheet_Modal = 0x7f1003d8;
		public static final int Widget_Material3_SideSheet_Modal_Detached = 0x7f1003d9;
		public static final int Widget_Material3_Slider = 0x7f1003da;
		public static final int Widget_Material3_Slider_Label = 0x7f1003db;
		public static final int Widget_Material3_Snackbar = 0x7f1003de;
		public static final int Widget_Material3_Snackbar_FullWidth = 0x7f1003df;
		public static final int Widget_Material3_Snackbar_TextView = 0x7f1003e0;
		public static final int Widget_Material3_TabLayout = 0x7f1003e1;
		public static final int Widget_Material3_TabLayout_OnSurface = 0x7f1003e2;
		public static final int Widget_Material3_TabLayout_Secondary = 0x7f1003e3;
		public static final int Widget_Material3_TextInputEditText_FilledBox = 0x7f1003e4;
		public static final int Widget_Material3_TextInputEditText_FilledBox_Dense = 0x7f1003e5;
		public static final int Widget_Material3_TextInputEditText_OutlinedBox = 0x7f1003e6;
		public static final int Widget_Material3_TextInputEditText_OutlinedBox_Dense = 0x7f1003e7;
		public static final int Widget_Material3_TextInputLayout_FilledBox = 0x7f1003e8;
		public static final int Widget_Material3_TextInputLayout_FilledBox_Dense = 0x7f1003e9;
		public static final int Widget_Material3_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu = 0x7f1003ea;
		public static final int Widget_Material3_TextInputLayout_FilledBox_ExposedDropdownMenu = 0x7f1003eb;
		public static final int Widget_Material3_TextInputLayout_OutlinedBox = 0x7f1003ec;
		public static final int Widget_Material3_TextInputLayout_OutlinedBox_Dense = 0x7f1003ed;
		public static final int Widget_Material3_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu = 0x7f1003ee;
		public static final int Widget_Material3_TextInputLayout_OutlinedBox_ExposedDropdownMenu = 0x7f1003ef;
		public static final int Widget_Material3_Toolbar = 0x7f1003f0;
		public static final int Widget_Material3_Toolbar_OnSurface = 0x7f1003f1;
		public static final int Widget_Material3_Toolbar_Surface = 0x7f1003f2;
		public static final int Widget_Material3_Tooltip = 0x7f1003f3;
		public static final int Widget_MaterialComponents_ActionBar_Primary = 0x7f1003f4;
		public static final int Widget_MaterialComponents_ActionBar_PrimarySurface = 0x7f1003f5;
		public static final int Widget_MaterialComponents_ActionBar_Solid = 0x7f1003f6;
		public static final int Widget_MaterialComponents_ActionBar_Surface = 0x7f1003f7;
		public static final int Widget_MaterialComponents_ActionMode = 0x7f1003f8;
		public static final int Widget_MaterialComponents_AppBarLayout_Primary = 0x7f1003f9;
		public static final int Widget_MaterialComponents_AppBarLayout_PrimarySurface = 0x7f1003fa;
		public static final int Widget_MaterialComponents_AppBarLayout_Surface = 0x7f1003fb;
		public static final int Widget_MaterialComponents_AutoCompleteTextView_FilledBox = 0x7f1003fc;
		public static final int Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense = 0x7f1003fd;
		public static final int Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox = 0x7f1003fe;
		public static final int Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense = 0x7f1003ff;
		public static final int Widget_MaterialComponents_Badge = 0x7f100400;
		public static final int Widget_MaterialComponents_BottomAppBar = 0x7f100401;
		public static final int Widget_MaterialComponents_BottomAppBar_Colored = 0x7f100402;
		public static final int Widget_MaterialComponents_BottomAppBar_PrimarySurface = 0x7f100403;
		public static final int Widget_MaterialComponents_BottomNavigationView = 0x7f100404;
		public static final int Widget_MaterialComponents_BottomNavigationView_Colored = 0x7f100405;
		public static final int Widget_MaterialComponents_BottomNavigationView_PrimarySurface = 0x7f100406;
		public static final int Widget_MaterialComponents_BottomSheet = 0x7f100407;
		public static final int Widget_MaterialComponents_BottomSheet_Modal = 0x7f100408;
		public static final int Widget_MaterialComponents_Button = 0x7f100409;
		public static final int Widget_MaterialComponents_Button_Icon = 0x7f10040a;
		public static final int Widget_MaterialComponents_Button_OutlinedButton = 0x7f10040b;
		public static final int Widget_MaterialComponents_Button_OutlinedButton_Icon = 0x7f10040c;
		public static final int Widget_MaterialComponents_Button_TextButton = 0x7f10040d;
		public static final int Widget_MaterialComponents_Button_TextButton_Dialog = 0x7f10040e;
		public static final int Widget_MaterialComponents_Button_TextButton_Dialog_Flush = 0x7f10040f;
		public static final int Widget_MaterialComponents_Button_TextButton_Dialog_Icon = 0x7f100410;
		public static final int Widget_MaterialComponents_Button_TextButton_Icon = 0x7f100411;
		public static final int Widget_MaterialComponents_Button_TextButton_Snackbar = 0x7f100412;
		public static final int Widget_MaterialComponents_Button_UnelevatedButton = 0x7f100413;
		public static final int Widget_MaterialComponents_Button_UnelevatedButton_Icon = 0x7f100414;
		public static final int Widget_MaterialComponents_CardView = 0x7f100415;
		public static final int Widget_MaterialComponents_CheckedTextView = 0x7f100416;
		public static final int Widget_MaterialComponents_ChipGroup = 0x7f10041b;
		public static final int Widget_MaterialComponents_Chip_Action = 0x7f100417;
		public static final int Widget_MaterialComponents_Chip_Choice = 0x7f100418;
		public static final int Widget_MaterialComponents_Chip_Entry = 0x7f100419;
		public static final int Widget_MaterialComponents_Chip_Filter = 0x7f10041a;
		public static final int Widget_MaterialComponents_CircularProgressIndicator = 0x7f10041c;
		public static final int Widget_MaterialComponents_CircularProgressIndicator_ExtraSmall = 0x7f10041d;
		public static final int Widget_MaterialComponents_CircularProgressIndicator_Medium = 0x7f10041e;
		public static final int Widget_MaterialComponents_CircularProgressIndicator_Small = 0x7f10041f;
		public static final int Widget_MaterialComponents_CollapsingToolbar = 0x7f100420;
		public static final int Widget_MaterialComponents_CompoundButton_CheckBox = 0x7f100421;
		public static final int Widget_MaterialComponents_CompoundButton_RadioButton = 0x7f100422;
		public static final int Widget_MaterialComponents_CompoundButton_Switch = 0x7f100423;
		public static final int Widget_MaterialComponents_ExtendedFloatingActionButton = 0x7f100424;
		public static final int Widget_MaterialComponents_ExtendedFloatingActionButton_Icon = 0x7f100425;
		public static final int Widget_MaterialComponents_FloatingActionButton = 0x7f100426;
		public static final int Widget_MaterialComponents_Light_ActionBar_Solid = 0x7f100427;
		public static final int Widget_MaterialComponents_LinearProgressIndicator = 0x7f100428;
		public static final int Widget_MaterialComponents_MaterialButtonToggleGroup = 0x7f100429;
		public static final int Widget_MaterialComponents_MaterialCalendar = 0x7f10042a;
		public static final int Widget_MaterialComponents_MaterialCalendar_Day = 0x7f10042b;
		public static final int Widget_MaterialComponents_MaterialCalendar_DayOfWeekLabel = 0x7f10042f;
		public static final int Widget_MaterialComponents_MaterialCalendar_DayTextView = 0x7f100430;
		public static final int Widget_MaterialComponents_MaterialCalendar_Day_Invalid = 0x7f10042c;
		public static final int Widget_MaterialComponents_MaterialCalendar_Day_Selected = 0x7f10042d;
		public static final int Widget_MaterialComponents_MaterialCalendar_Day_Today = 0x7f10042e;
		public static final int Widget_MaterialComponents_MaterialCalendar_Fullscreen = 0x7f100431;
		public static final int Widget_MaterialComponents_MaterialCalendar_HeaderCancelButton = 0x7f100432;
		public static final int Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton = 0x7f100433;
		public static final int Widget_MaterialComponents_MaterialCalendar_HeaderDivider = 0x7f100434;
		public static final int Widget_MaterialComponents_MaterialCalendar_HeaderLayout = 0x7f100435;
		public static final int Widget_MaterialComponents_MaterialCalendar_HeaderLayout_Fullscreen = 0x7f100436;
		public static final int Widget_MaterialComponents_MaterialCalendar_HeaderSelection = 0x7f100437;
		public static final int Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen = 0x7f100438;
		public static final int Widget_MaterialComponents_MaterialCalendar_HeaderTitle = 0x7f100439;
		public static final int Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton = 0x7f10043a;
		public static final int Widget_MaterialComponents_MaterialCalendar_Item = 0x7f10043b;
		public static final int Widget_MaterialComponents_MaterialCalendar_MonthNavigationButton = 0x7f10043c;
		public static final int Widget_MaterialComponents_MaterialCalendar_MonthTextView = 0x7f10043d;
		public static final int Widget_MaterialComponents_MaterialCalendar_Year = 0x7f10043e;
		public static final int Widget_MaterialComponents_MaterialCalendar_YearNavigationButton = 0x7f100441;
		public static final int Widget_MaterialComponents_MaterialCalendar_Year_Selected = 0x7f10043f;
		public static final int Widget_MaterialComponents_MaterialCalendar_Year_Today = 0x7f100440;
		public static final int Widget_MaterialComponents_MaterialDivider = 0x7f100442;
		public static final int Widget_MaterialComponents_NavigationRailView = 0x7f100443;
		public static final int Widget_MaterialComponents_NavigationRailView_Colored = 0x7f100444;
		public static final int Widget_MaterialComponents_NavigationRailView_Colored_Compact = 0x7f100445;
		public static final int Widget_MaterialComponents_NavigationRailView_Compact = 0x7f100446;
		public static final int Widget_MaterialComponents_NavigationRailView_PrimarySurface = 0x7f100447;
		public static final int Widget_MaterialComponents_NavigationView = 0x7f100448;
		public static final int Widget_MaterialComponents_PopupMenu = 0x7f100449;
		public static final int Widget_MaterialComponents_PopupMenu_ContextMenu = 0x7f10044a;
		public static final int Widget_MaterialComponents_PopupMenu_ListPopupWindow = 0x7f10044b;
		public static final int Widget_MaterialComponents_PopupMenu_Overflow = 0x7f10044c;
		public static final int Widget_MaterialComponents_ProgressIndicator = 0x7f10044d;
		public static final int Widget_MaterialComponents_ShapeableImageView = 0x7f10044e;
		public static final int Widget_MaterialComponents_Slider = 0x7f10044f;
		public static final int Widget_MaterialComponents_Snackbar = 0x7f100450;
		public static final int Widget_MaterialComponents_Snackbar_FullWidth = 0x7f100451;
		public static final int Widget_MaterialComponents_Snackbar_TextView = 0x7f100452;
		public static final int Widget_MaterialComponents_TabLayout = 0x7f100453;
		public static final int Widget_MaterialComponents_TabLayout_Colored = 0x7f100454;
		public static final int Widget_MaterialComponents_TabLayout_PrimarySurface = 0x7f100455;
		public static final int Widget_MaterialComponents_TextInputEditText_FilledBox = 0x7f100456;
		public static final int Widget_MaterialComponents_TextInputEditText_FilledBox_Dense = 0x7f100457;
		public static final int Widget_MaterialComponents_TextInputEditText_OutlinedBox = 0x7f100458;
		public static final int Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense = 0x7f100459;
		public static final int Widget_MaterialComponents_TextInputLayout_FilledBox = 0x7f10045a;
		public static final int Widget_MaterialComponents_TextInputLayout_FilledBox_Dense = 0x7f10045b;
		public static final int Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu = 0x7f10045c;
		public static final int Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu = 0x7f10045d;
		public static final int Widget_MaterialComponents_TextInputLayout_OutlinedBox = 0x7f10045e;
		public static final int Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense = 0x7f10045f;
		public static final int Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu = 0x7f100460;
		public static final int Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu = 0x7f100461;
		public static final int Widget_MaterialComponents_TextView = 0x7f100462;
		public static final int Widget_MaterialComponents_TimePicker = 0x7f100463;
		public static final int Widget_MaterialComponents_TimePicker_Button = 0x7f100464;
		public static final int Widget_MaterialComponents_TimePicker_Clock = 0x7f100465;
		public static final int Widget_MaterialComponents_TimePicker_Display = 0x7f100466;
		public static final int Widget_MaterialComponents_TimePicker_Display_Divider = 0x7f100467;
		public static final int Widget_MaterialComponents_TimePicker_Display_HelperText = 0x7f100468;
		public static final int Widget_MaterialComponents_TimePicker_Display_TextInputEditText = 0x7f100469;
		public static final int Widget_MaterialComponents_TimePicker_Display_TextInputLayout = 0x7f10046a;
		public static final int Widget_MaterialComponents_TimePicker_ImageButton = 0x7f10046b;
		public static final int Widget_MaterialComponents_TimePicker_ImageButton_ShapeAppearance = 0x7f10046c;
		public static final int Widget_MaterialComponents_Toolbar = 0x7f10046d;
		public static final int Widget_MaterialComponents_Toolbar_Primary = 0x7f10046e;
		public static final int Widget_MaterialComponents_Toolbar_PrimarySurface = 0x7f10046f;
		public static final int Widget_MaterialComponents_Toolbar_Surface = 0x7f100470;
		public static final int Widget_MaterialComponents_Tooltip = 0x7f100471;
		public static final int Widget_Support_CoordinatorLayout = 0x7f100472;
	}
	public static final class styleable {
		public static final int[] ActionBar = new int[] { 0x7f03004d, 0x7f030054, 0x7f030055, 0x7f030143, 0x7f030144, 0x7f030145, 0x7f030146, 0x7f030147, 0x7f030148, 0x7f030171, 0x7f030188, 0x7f030189, 0x7f0301a9, 0x7f030231, 0x7f030239, 0x7f03023f, 0x7f030240, 0x7f030244, 0x7f030255, 0x7f03026c, 0x7f0302ea, 0x7f030371, 0x7f0303aa, 0x7f0303b2, 0x7f0303b3, 0x7f030439, 0x7f03043d, 0x7f0304c8, 0x7f0304d6 };
		public static final int ActionBar_background = 0;
		public static final int ActionBar_backgroundSplit = 1;
		public static final int ActionBar_backgroundStacked = 2;
		public static final int ActionBar_contentInsetEnd = 3;
		public static final int ActionBar_contentInsetEndWithActions = 4;
		public static final int ActionBar_contentInsetLeft = 5;
		public static final int ActionBar_contentInsetRight = 6;
		public static final int ActionBar_contentInsetStart = 7;
		public static final int ActionBar_contentInsetStartWithNavigation = 8;
		public static final int ActionBar_customNavigationLayout = 9;
		public static final int ActionBar_displayOptions = 10;
		public static final int ActionBar_divider = 11;
		public static final int ActionBar_elevation = 12;
		public static final int ActionBar_height = 13;
		public static final int ActionBar_hideOnContentScroll = 14;
		public static final int ActionBar_homeAsUpIndicator = 15;
		public static final int ActionBar_homeLayout = 16;
		public static final int ActionBar_icon = 17;
		public static final int ActionBar_indeterminateProgressStyle = 18;
		public static final int ActionBar_itemPadding = 19;
		public static final int ActionBar_logo = 20;
		public static final int ActionBar_navigationMode = 21;
		public static final int ActionBar_popupTheme = 22;
		public static final int ActionBar_progressBarPadding = 23;
		public static final int ActionBar_progressBarStyle = 24;
		public static final int ActionBar_subtitle = 25;
		public static final int ActionBar_subtitleTextStyle = 26;
		public static final int ActionBar_title = 27;
		public static final int ActionBar_titleTextStyle = 28;
		public static final int[] ActionBarLayout = new int[] { 0x010100b3 };
		public static final int ActionBarLayout_android_layout_gravity = 0;
		public static final int[] ActionMenuItemView = new int[] { 0x0101013f };
		public static final int ActionMenuItemView_android_minWidth = 0;
		public static final int[] ActionMenuView = new int[] { };
		public static final int[] ActionMode = new int[] { 0x7f03004d, 0x7f030054, 0x7f0300ee, 0x7f030231, 0x7f03043d, 0x7f0304d6 };
		public static final int ActionMode_background = 0;
		public static final int ActionMode_backgroundSplit = 1;
		public static final int ActionMode_closeItemLayout = 2;
		public static final int ActionMode_height = 3;
		public static final int ActionMode_subtitleTextStyle = 4;
		public static final int ActionMode_titleTextStyle = 5;
		public static final int[] ActivityChooserView = new int[] { 0x7f0301c6, 0x7f03025c };
		public static final int ActivityChooserView_expandActivityOverflowButtonDrawable = 0;
		public static final int ActivityChooserView_initialActivityCount = 1;
		public static final int[] ActivityFilter = new int[] { 0x7f030028, 0x7f03002a };
		public static final int ActivityFilter_activityAction = 0;
		public static final int ActivityFilter_activityName = 1;
		public static final int[] ActivityRule = new int[] { 0x7f030034, 0x7f030464 };
		public static final int ActivityRule_alwaysExpand = 0;
		public static final int ActivityRule_tag = 1;
		public static final int[] AlertDialog = new int[] { 0x010100f2, 0x7f030099, 0x7f03009c, 0x7f0302df, 0x7f0302e0, 0x7f03036c, 0x7f0303f7, 0x7f0303ff };
		public static final int AlertDialog_android_layout = 0;
		public static final int AlertDialog_buttonIconDimen = 1;
		public static final int AlertDialog_buttonPanelSideLayout = 2;
		public static final int AlertDialog_listItemLayout = 3;
		public static final int AlertDialog_listLayout = 4;
		public static final int AlertDialog_multiChoiceItemLayout = 5;
		public static final int AlertDialog_showTitle = 6;
		public static final int AlertDialog_singleChoiceItemLayout = 7;
		public static final int[] AnimatedStateListDrawableCompat = new int[] { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d };
		public static final int AnimatedStateListDrawableCompat_android_constantSize = 3;
		public static final int AnimatedStateListDrawableCompat_android_dither = 0;
		public static final int AnimatedStateListDrawableCompat_android_enterFadeDuration = 4;
		public static final int AnimatedStateListDrawableCompat_android_exitFadeDuration = 5;
		public static final int AnimatedStateListDrawableCompat_android_variablePadding = 2;
		public static final int AnimatedStateListDrawableCompat_android_visible = 1;
		public static final int[] AnimatedStateListDrawableItem = new int[] { 0x010100d0, 0x01010199 };
		public static final int AnimatedStateListDrawableItem_android_drawable = 1;
		public static final int AnimatedStateListDrawableItem_android_id = 0;
		public static final int[] AnimatedStateListDrawableTransition = new int[] { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b };
		public static final int AnimatedStateListDrawableTransition_android_drawable = 0;
		public static final int AnimatedStateListDrawableTransition_android_fromId = 2;
		public static final int AnimatedStateListDrawableTransition_android_reversible = 3;
		public static final int AnimatedStateListDrawableTransition_android_toId = 1;
		public static final int[] AppBarLayout = new int[] { 0x010100d4, 0x0101048f, 0x01010540, 0x7f0301a9, 0x7f0301c7, 0x7f0302d4, 0x7f0302d5, 0x7f0302d6, 0x7f03042e };
		public static final int AppBarLayout_android_background = 0;
		public static final int AppBarLayout_android_keyboardNavigationCluster = 2;
		public static final int AppBarLayout_android_touchscreenBlocksFocus = 1;
		public static final int AppBarLayout_elevation = 3;
		public static final int AppBarLayout_expanded = 4;
		public static final int AppBarLayout_liftOnScroll = 5;
		public static final int AppBarLayout_liftOnScrollColor = 6;
		public static final int AppBarLayout_liftOnScrollTargetViewId = 7;
		public static final int AppBarLayout_statusBarForeground = 8;
		public static final int[] AppBarLayoutStates = new int[] { 0x7f030425, 0x7f030426, 0x7f03042a, 0x7f03042b };
		public static final int AppBarLayoutStates_state_collapsed = 0;
		public static final int AppBarLayoutStates_state_collapsible = 1;
		public static final int AppBarLayoutStates_state_liftable = 2;
		public static final int AppBarLayoutStates_state_lifted = 3;
		public static final int[] AppBarLayout_Layout = new int[] { 0x7f0302d0, 0x7f0302d1, 0x7f0302d2 };
		public static final int AppBarLayout_Layout_layout_scrollEffect = 0;
		public static final int AppBarLayout_Layout_layout_scrollFlags = 1;
		public static final int AppBarLayout_Layout_layout_scrollInterpolator = 2;
		public static final int[] AppCompatEmojiHelper = new int[] { };
		public static final int[] AppCompatImageView = new int[] { 0x01010119, 0x7f030418, 0x7f0304c5, 0x7f0304c6 };
		public static final int AppCompatImageView_android_src = 0;
		public static final int AppCompatImageView_srcCompat = 1;
		public static final int AppCompatImageView_tint = 2;
		public static final int AppCompatImageView_tintMode = 3;
		public static final int[] AppCompatSeekBar = new int[] { 0x01010142, 0x7f0304bf, 0x7f0304c0, 0x7f0304c1 };
		public static final int AppCompatSeekBar_android_thumb = 0;
		public static final int AppCompatSeekBar_tickMark = 1;
		public static final int AppCompatSeekBar_tickMarkTint = 2;
		public static final int AppCompatSeekBar_tickMarkTintMode = 3;
		public static final int[] AppCompatTextHelper = new int[] { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 };
		public static final int AppCompatTextHelper_android_drawableBottom = 2;
		public static final int AppCompatTextHelper_android_drawableEnd = 6;
		public static final int AppCompatTextHelper_android_drawableLeft = 3;
		public static final int AppCompatTextHelper_android_drawableRight = 4;
		public static final int AppCompatTextHelper_android_drawableStart = 5;
		public static final int AppCompatTextHelper_android_drawableTop = 1;
		public static final int AppCompatTextHelper_android_textAppearance = 0;
		public static final int[] AppCompatTextView = new int[] { 0x01010034, 0x7f030046, 0x7f030047, 0x7f030048, 0x7f030049, 0x7f03004a, 0x7f030195, 0x7f030196, 0x7f030197, 0x7f030198, 0x7f03019a, 0x7f03019b, 0x7f03019c, 0x7f03019d, 0x7f0301ad, 0x7f0301ea, 0x7f03020e, 0x7f030218, 0x7f030288, 0x7f0302d8, 0x7f03046a, 0x7f0304a1 };
		public static final int AppCompatTextView_android_textAppearance = 0;
		public static final int AppCompatTextView_autoSizeMaxTextSize = 1;
		public static final int AppCompatTextView_autoSizeMinTextSize = 2;
		public static final int AppCompatTextView_autoSizePresetSizes = 3;
		public static final int AppCompatTextView_autoSizeStepGranularity = 4;
		public static final int AppCompatTextView_autoSizeTextType = 5;
		public static final int AppCompatTextView_drawableBottomCompat = 6;
		public static final int AppCompatTextView_drawableEndCompat = 7;
		public static final int AppCompatTextView_drawableLeftCompat = 8;
		public static final int AppCompatTextView_drawableRightCompat = 9;
		public static final int AppCompatTextView_drawableStartCompat = 10;
		public static final int AppCompatTextView_drawableTint = 11;
		public static final int AppCompatTextView_drawableTintMode = 12;
		public static final int AppCompatTextView_drawableTopCompat = 13;
		public static final int AppCompatTextView_emojiCompatEnabled = 14;
		public static final int AppCompatTextView_firstBaselineToTopHeight = 15;
		public static final int AppCompatTextView_fontFamily = 16;
		public static final int AppCompatTextView_fontVariationSettings = 17;
		public static final int AppCompatTextView_lastBaselineToBottomHeight = 18;
		public static final int AppCompatTextView_lineHeight = 19;
		public static final int AppCompatTextView_textAllCaps = 20;
		public static final int AppCompatTextView_textLocale = 21;
		public static final int[] AppCompatTheme = new int[] { 0x01010057, 0x010100ae, 0x7f030003, 0x7f030004, 0x7f030005, 0x7f030006, 0x7f030007, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000d, 0x7f03000e, 0x7f03000f, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030014, 0x7f030015, 0x7f030016, 0x7f030017, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030020, 0x7f030021, 0x7f030022, 0x7f030023, 0x7f030029, 0x7f03002c, 0x7f03002d, 0x7f03002e, 0x7f03002f, 0x7f030044, 0x7f03007d, 0x7f030091, 0x7f030092, 0x7f030093, 0x7f030094, 0x7f030095, 0x7f03009d, 0x7f03009e, 0x7f0300b9, 0x7f0300c4, 0x7f0300fc, 0x7f0300fd, 0x7f0300fe, 0x7f030100, 0x7f030101, 0x7f030102, 0x7f030103, 0x7f03011c, 0x7f03011e, 0x7f030133, 0x7f030152, 0x7f030185, 0x7f030186, 0x7f030187, 0x7f03018b, 0x7f030190, 0x7f0301a2, 0x7f0301a3, 0x7f0301a6, 0x7f0301a7, 0x7f0301a8, 0x7f03023f, 0x7f03024f, 0x7f0302db, 0x7f0302dc, 0x7f0302dd, 0x7f0302de, 0x7f0302e1, 0x7f0302e2, 0x7f0302e3, 0x7f0302e4, 0x7f0302e5, 0x7f0302e6, 0x7f0302e7, 0x7f0302e8, 0x7f0302e9, 0x7f03038d, 0x7f03038e, 0x7f03038f, 0x7f0303a9, 0x7f0303ab, 0x7f0303ba, 0x7f0303bc, 0x7f0303bd, 0x7f0303be, 0x7f0303d9, 0x7f0303dc, 0x7f0303dd, 0x7f0303de, 0x7f030409, 0x7f03040a, 0x7f030445, 0x7f030481, 0x7f030483, 0x7f030484, 0x7f030485, 0x7f030487, 0x7f030488, 0x7f030489, 0x7f03048a, 0x7f030495, 0x7f030496, 0x7f0304d9, 0x7f0304da, 0x7f0304dc, 0x7f0304dd, 0x7f030504, 0x7f030512, 0x7f030513, 0x7f030514, 0x7f030515, 0x7f030516, 0x7f030517, 0x7f030518, 0x7f030519, 0x7f03051a, 0x7f03051b };
		public static final int AppCompatTheme_actionBarDivider = 2;
		public static final int AppCompatTheme_actionBarItemBackground = 3;
		public static final int AppCompatTheme_actionBarPopupTheme = 4;
		public static final int AppCompatTheme_actionBarSize = 5;
		public static final int AppCompatTheme_actionBarSplitStyle = 6;
		public static final int AppCompatTheme_actionBarStyle = 7;
		public static final int AppCompatTheme_actionBarTabBarStyle = 8;
		public static final int AppCompatTheme_actionBarTabStyle = 9;
		public static final int AppCompatTheme_actionBarTabTextStyle = 10;
		public static final int AppCompatTheme_actionBarTheme = 11;
		public static final int AppCompatTheme_actionBarWidgetTheme = 12;
		public static final int AppCompatTheme_actionButtonStyle = 13;
		public static final int AppCompatTheme_actionDropDownStyle = 14;
		public static final int AppCompatTheme_actionMenuTextAppearance = 15;
		public static final int AppCompatTheme_actionMenuTextColor = 16;
		public static final int AppCompatTheme_actionModeBackground = 17;
		public static final int AppCompatTheme_actionModeCloseButtonStyle = 18;
		public static final int AppCompatTheme_actionModeCloseContentDescription = 19;
		public static final int AppCompatTheme_actionModeCloseDrawable = 20;
		public static final int AppCompatTheme_actionModeCopyDrawable = 21;
		public static final int AppCompatTheme_actionModeCutDrawable = 22;
		public static final int AppCompatTheme_actionModeFindDrawable = 23;
		public static final int AppCompatTheme_actionModePasteDrawable = 24;
		public static final int AppCompatTheme_actionModePopupWindowStyle = 25;
		public static final int AppCompatTheme_actionModeSelectAllDrawable = 26;
		public static final int AppCompatTheme_actionModeShareDrawable = 27;
		public static final int AppCompatTheme_actionModeSplitBackground = 28;
		public static final int AppCompatTheme_actionModeStyle = 29;
		public static final int AppCompatTheme_actionModeTheme = 30;
		public static final int AppCompatTheme_actionModeWebSearchDrawable = 31;
		public static final int AppCompatTheme_actionOverflowButtonStyle = 32;
		public static final int AppCompatTheme_actionOverflowMenuStyle = 33;
		public static final int AppCompatTheme_activityChooserViewStyle = 34;
		public static final int AppCompatTheme_alertDialogButtonGroupStyle = 35;
		public static final int AppCompatTheme_alertDialogCenterButtons = 36;
		public static final int AppCompatTheme_alertDialogStyle = 37;
		public static final int AppCompatTheme_alertDialogTheme = 38;
		public static final int AppCompatTheme_android_windowAnimationStyle = 1;
		public static final int AppCompatTheme_android_windowIsFloating = 0;
		public static final int AppCompatTheme_autoCompleteTextViewStyle = 39;
		public static final int AppCompatTheme_borderlessButtonStyle = 40;
		public static final int AppCompatTheme_buttonBarButtonStyle = 41;
		public static final int AppCompatTheme_buttonBarNegativeButtonStyle = 42;
		public static final int AppCompatTheme_buttonBarNeutralButtonStyle = 43;
		public static final int AppCompatTheme_buttonBarPositiveButtonStyle = 44;
		public static final int AppCompatTheme_buttonBarStyle = 45;
		public static final int AppCompatTheme_buttonStyle = 46;
		public static final int AppCompatTheme_buttonStyleSmall = 47;
		public static final int AppCompatTheme_checkboxStyle = 48;
		public static final int AppCompatTheme_checkedTextViewStyle = 49;
		public static final int AppCompatTheme_colorAccent = 50;
		public static final int AppCompatTheme_colorBackgroundFloating = 51;
		public static final int AppCompatTheme_colorButtonNormal = 52;
		public static final int AppCompatTheme_colorControlActivated = 53;
		public static final int AppCompatTheme_colorControlHighlight = 54;
		public static final int AppCompatTheme_colorControlNormal = 55;
		public static final int AppCompatTheme_colorError = 56;
		public static final int AppCompatTheme_colorPrimary = 57;
		public static final int AppCompatTheme_colorPrimaryDark = 58;
		public static final int AppCompatTheme_colorSwitchThumbNormal = 59;
		public static final int AppCompatTheme_controlBackground = 60;
		public static final int AppCompatTheme_dialogCornerRadius = 61;
		public static final int AppCompatTheme_dialogPreferredPadding = 62;
		public static final int AppCompatTheme_dialogTheme = 63;
		public static final int AppCompatTheme_dividerHorizontal = 64;
		public static final int AppCompatTheme_dividerVertical = 65;
		public static final int AppCompatTheme_dropDownListViewStyle = 66;
		public static final int AppCompatTheme_dropdownListPreferredItemHeight = 67;
		public static final int AppCompatTheme_editTextBackground = 68;
		public static final int AppCompatTheme_editTextColor = 69;
		public static final int AppCompatTheme_editTextStyle = 70;
		public static final int AppCompatTheme_homeAsUpIndicator = 71;
		public static final int AppCompatTheme_imageButtonStyle = 72;
		public static final int AppCompatTheme_listChoiceBackgroundIndicator = 73;
		public static final int AppCompatTheme_listChoiceIndicatorMultipleAnimated = 74;
		public static final int AppCompatTheme_listChoiceIndicatorSingleAnimated = 75;
		public static final int AppCompatTheme_listDividerAlertDialog = 76;
		public static final int AppCompatTheme_listMenuViewStyle = 77;
		public static final int AppCompatTheme_listPopupWindowStyle = 78;
		public static final int AppCompatTheme_listPreferredItemHeight = 79;
		public static final int AppCompatTheme_listPreferredItemHeightLarge = 80;
		public static final int AppCompatTheme_listPreferredItemHeightSmall = 81;
		public static final int AppCompatTheme_listPreferredItemPaddingEnd = 82;
		public static final int AppCompatTheme_listPreferredItemPaddingLeft = 83;
		public static final int AppCompatTheme_listPreferredItemPaddingRight = 84;
		public static final int AppCompatTheme_listPreferredItemPaddingStart = 85;
		public static final int AppCompatTheme_panelBackground = 86;
		public static final int AppCompatTheme_panelMenuListTheme = 87;
		public static final int AppCompatTheme_panelMenuListWidth = 88;
		public static final int AppCompatTheme_popupMenuStyle = 89;
		public static final int AppCompatTheme_popupWindowStyle = 90;
		public static final int AppCompatTheme_radioButtonStyle = 91;
		public static final int AppCompatTheme_ratingBarStyle = 92;
		public static final int AppCompatTheme_ratingBarStyleIndicator = 93;
		public static final int AppCompatTheme_ratingBarStyleSmall = 94;
		public static final int AppCompatTheme_searchViewStyle = 95;
		public static final int AppCompatTheme_seekBarStyle = 96;
		public static final int AppCompatTheme_selectableItemBackground = 97;
		public static final int AppCompatTheme_selectableItemBackgroundBorderless = 98;
		public static final int AppCompatTheme_spinnerDropDownItemStyle = 99;
		public static final int AppCompatTheme_spinnerStyle = 100;
		public static final int AppCompatTheme_switchStyle = 101;
		public static final int AppCompatTheme_textAppearanceLargePopupMenu = 102;
		public static final int AppCompatTheme_textAppearanceListItem = 103;
		public static final int AppCompatTheme_textAppearanceListItemSecondary = 104;
		public static final int AppCompatTheme_textAppearanceListItemSmall = 105;
		public static final int AppCompatTheme_textAppearancePopupMenuHeader = 106;
		public static final int AppCompatTheme_textAppearanceSearchResultSubtitle = 107;
		public static final int AppCompatTheme_textAppearanceSearchResultTitle = 108;
		public static final int AppCompatTheme_textAppearanceSmallPopupMenu = 109;
		public static final int AppCompatTheme_textColorAlertDialogListItem = 110;
		public static final int AppCompatTheme_textColorSearchUrl = 111;
		public static final int AppCompatTheme_toolbarNavigationButtonStyle = 112;
		public static final int AppCompatTheme_toolbarStyle = 113;
		public static final int AppCompatTheme_tooltipForegroundColor = 114;
		public static final int AppCompatTheme_tooltipFrameBackground = 115;
		public static final int AppCompatTheme_viewInflaterClass = 116;
		public static final int AppCompatTheme_windowActionBar = 117;
		public static final int AppCompatTheme_windowActionBarOverlay = 118;
		public static final int AppCompatTheme_windowActionModeOverlay = 119;
		public static final int AppCompatTheme_windowFixedHeightMajor = 120;
		public static final int AppCompatTheme_windowFixedHeightMinor = 121;
		public static final int AppCompatTheme_windowFixedWidthMajor = 122;
		public static final int AppCompatTheme_windowFixedWidthMinor = 123;
		public static final int AppCompatTheme_windowMinWidthMajor = 124;
		public static final int AppCompatTheme_windowMinWidthMinor = 125;
		public static final int AppCompatTheme_windowNoTitle = 126;
		public static final int[] Badge = new int[] { 0x7f030042, 0x7f03004e, 0x7f030058, 0x7f030059, 0x7f03005a, 0x7f03005b, 0x7f03005c, 0x7f03005e, 0x7f03005f, 0x7f030060, 0x7f030061, 0x7f030062, 0x7f030063, 0x7f030064, 0x7f030065, 0x7f030066, 0x7f030067, 0x7f030068, 0x7f030241, 0x7f030242, 0x7f030287, 0x7f030326, 0x7f03032a, 0x7f030378, 0x7f03037a, 0x7f030502, 0x7f030503 };
		public static final int Badge_autoAdjustToWithinGrandparentBounds = 0;
		public static final int Badge_backgroundColor = 1;
		public static final int Badge_badgeGravity = 2;
		public static final int Badge_badgeHeight = 3;
		public static final int Badge_badgeRadius = 4;
		public static final int Badge_badgeShapeAppearance = 5;
		public static final int Badge_badgeShapeAppearanceOverlay = 6;
		public static final int Badge_badgeText = 7;
		public static final int Badge_badgeTextAppearance = 8;
		public static final int Badge_badgeTextColor = 9;
		public static final int Badge_badgeVerticalPadding = 10;
		public static final int Badge_badgeWidePadding = 11;
		public static final int Badge_badgeWidth = 12;
		public static final int Badge_badgeWithTextHeight = 13;
		public static final int Badge_badgeWithTextRadius = 14;
		public static final int Badge_badgeWithTextShapeAppearance = 15;
		public static final int Badge_badgeWithTextShapeAppearanceOverlay = 16;
		public static final int Badge_badgeWithTextWidth = 17;
		public static final int Badge_horizontalOffset = 18;
		public static final int Badge_horizontalOffsetWithText = 19;
		public static final int Badge_largeFontVerticalOffsetAdjustment = 20;
		public static final int Badge_maxCharacterCount = 21;
		public static final int Badge_maxNumber = 22;
		public static final int Badge_number = 23;
		public static final int Badge_offsetAlignmentMode = 24;
		public static final int Badge_verticalOffset = 25;
		public static final int Badge_verticalOffsetWithText = 26;
		public static final int[] BaseProgressIndicator = new int[] { 0x01010139, 0x7f030236, 0x7f030256, 0x7f03025b, 0x7f030334, 0x7f0303ef, 0x7f0303f1, 0x7f0304e5, 0x7f0304e8, 0x7f0304ef };
		public static final int BaseProgressIndicator_android_indeterminate = 0;
		public static final int BaseProgressIndicator_hideAnimationBehavior = 1;
		public static final int BaseProgressIndicator_indicatorColor = 2;
		public static final int BaseProgressIndicator_minHideDelay = 4;
		public static final int BaseProgressIndicator_showAnimationBehavior = 5;
		public static final int BaseProgressIndicator_showDelay = 6;
		public static final int BaseProgressIndicator_trackColor = 7;
		public static final int BaseProgressIndicator_trackCornerRadius = 8;
		public static final int BaseProgressIndicator_trackThickness = 9;
		public static final int[] BottomAppBar = new int[] { 0x7f03002b, 0x7f030056, 0x7f0301a9, 0x7f0301d9, 0x7f0301da, 0x7f0301db, 0x7f0301dc, 0x7f0301dd, 0x7f0301de, 0x7f0301df, 0x7f03023a, 0x7f03032f, 0x7f030370, 0x7f030385, 0x7f030387, 0x7f030388, 0x7f0303c8 };
		public static final int BottomAppBar_addElevationShadow = 0;
		public static final int BottomAppBar_backgroundTint = 1;
		public static final int BottomAppBar_elevation = 2;
		public static final int BottomAppBar_fabAlignmentMode = 3;
		public static final int BottomAppBar_fabAlignmentModeEndMargin = 4;
		public static final int BottomAppBar_fabAnchorMode = 5;
		public static final int BottomAppBar_fabAnimationMode = 6;
		public static final int BottomAppBar_fabCradleMargin = 7;
		public static final int BottomAppBar_fabCradleRoundedCornerRadius = 8;
		public static final int BottomAppBar_fabCradleVerticalOffset = 9;
		public static final int BottomAppBar_hideOnScroll = 10;
		public static final int BottomAppBar_menuAlignmentMode = 11;
		public static final int BottomAppBar_navigationIconTint = 12;
		public static final int BottomAppBar_paddingBottomSystemWindowInsets = 13;
		public static final int BottomAppBar_paddingLeftSystemWindowInsets = 14;
		public static final int BottomAppBar_paddingRightSystemWindowInsets = 15;
		public static final int BottomAppBar_removeEmbeddedFabElevation = 16;
		public static final int[] BottomNavigationView = new int[] { 0x01010140, 0x7f030139, 0x7f030266, 0x7f0303e2, 0x7f0303ea };
		public static final int BottomNavigationView_android_minHeight = 0;
		public static final int BottomNavigationView_compatShadowEnabled = 1;
		public static final int BottomNavigationView_itemHorizontalTranslationEnabled = 2;
		public static final int BottomNavigationView_shapeAppearance = 3;
		public static final int BottomNavigationView_shapeAppearanceOverlay = 4;
		public static final int[] BottomSheetBehavior_Layout = new int[] { 0x0101011f, 0x01010120, 0x01010440, 0x7f030056, 0x7f03006f, 0x7f030070, 0x7f030071, 0x7f030072, 0x7f030073, 0x7f030075, 0x7f030076, 0x7f030077, 0x7f030078, 0x7f03021f, 0x7f0302ef, 0x7f0302f0, 0x7f0302f1, 0x7f030385, 0x7f030387, 0x7f030388, 0x7f03038c, 0x7f0303e2, 0x7f0303ea, 0x7f0303ee };
		public static final int BottomSheetBehavior_Layout_android_elevation = 2;
		public static final int BottomSheetBehavior_Layout_android_maxHeight = 1;
		public static final int BottomSheetBehavior_Layout_android_maxWidth = 0;
		public static final int BottomSheetBehavior_Layout_backgroundTint = 3;
		public static final int BottomSheetBehavior_Layout_behavior_draggable = 4;
		public static final int BottomSheetBehavior_Layout_behavior_expandedOffset = 5;
		public static final int BottomSheetBehavior_Layout_behavior_fitToContents = 6;
		public static final int BottomSheetBehavior_Layout_behavior_halfExpandedRatio = 7;
		public static final int BottomSheetBehavior_Layout_behavior_hideable = 8;
		public static final int BottomSheetBehavior_Layout_behavior_peekHeight = 9;
		public static final int BottomSheetBehavior_Layout_behavior_saveFlags = 10;
		public static final int BottomSheetBehavior_Layout_behavior_significantVelocityThreshold = 11;
		public static final int BottomSheetBehavior_Layout_behavior_skipCollapsed = 12;
		public static final int BottomSheetBehavior_Layout_gestureInsetBottomIgnored = 13;
		public static final int BottomSheetBehavior_Layout_marginLeftSystemWindowInsets = 14;
		public static final int BottomSheetBehavior_Layout_marginRightSystemWindowInsets = 15;
		public static final int BottomSheetBehavior_Layout_marginTopSystemWindowInsets = 16;
		public static final int BottomSheetBehavior_Layout_paddingBottomSystemWindowInsets = 17;
		public static final int BottomSheetBehavior_Layout_paddingLeftSystemWindowInsets = 18;
		public static final int BottomSheetBehavior_Layout_paddingRightSystemWindowInsets = 19;
		public static final int BottomSheetBehavior_Layout_paddingTopSystemWindowInsets = 20;
		public static final int BottomSheetBehavior_Layout_shapeAppearance = 21;
		public static final int BottomSheetBehavior_Layout_shapeAppearanceOverlay = 22;
		public static final int BottomSheetBehavior_Layout_shouldRemoveExpandedCorners = 23;
		public static final int[] ButtonBarLayout = new int[] { 0x7f030030 };
		public static final int ButtonBarLayout_allowStacking = 0;
		public static final int[] Capability = new int[] { 0x7f0303b9, 0x7f0303ed };
		public static final int Capability_queryPatterns = 0;
		public static final int Capability_shortcutMatchRequired = 1;
		public static final int[] CardView = new int[] { 0x0101013f, 0x01010140, 0x7f0300a1, 0x7f0300a2, 0x7f0300a3, 0x7f0300a5, 0x7f0300a6, 0x7f0300a7, 0x7f030149, 0x7f03014a, 0x7f03014c, 0x7f03014d, 0x7f03014f };
		public static final int CardView_android_minHeight = 1;
		public static final int CardView_android_minWidth = 0;
		public static final int CardView_cardBackgroundColor = 2;
		public static final int CardView_cardCornerRadius = 3;
		public static final int CardView_cardElevation = 4;
		public static final int CardView_cardMaxElevation = 5;
		public static final int CardView_cardPreventCornerOverlap = 6;
		public static final int CardView_cardUseCompatPadding = 7;
		public static final int CardView_contentPadding = 8;
		public static final int CardView_contentPaddingBottom = 9;
		public static final int CardView_contentPaddingLeft = 10;
		public static final int CardView_contentPaddingRight = 11;
		public static final int CardView_contentPaddingTop = 12;
		public static final int[] Carousel = new int[] { 0x7f0300a9, 0x7f0300aa, 0x7f0300ab, 0x7f0300ac, 0x7f0300ad, 0x7f0300ae, 0x7f0300af, 0x7f0300b0, 0x7f0300b1, 0x7f0300b2, 0x7f0300b3 };
		public static final int Carousel_carousel_alignment = 0;
		public static final int[] CheckedTextView = new int[] { 0x01010108, 0x7f0300b6, 0x7f0300b7, 0x7f0300b8 };
		public static final int CheckedTextView_android_checkMark = 0;
		public static final int CheckedTextView_checkMarkCompat = 1;
		public static final int CheckedTextView_checkMarkTint = 2;
		public static final int CheckedTextView_checkMarkTintMode = 3;
		public static final int[] Chip = new int[] { 0x01010034, 0x01010095, 0x01010098, 0x010100ab, 0x0101011f, 0x0101014f, 0x010101e5, 0x7f0300bc, 0x7f0300bd, 0x7f0300c1, 0x7f0300c2, 0x7f0300c5, 0x7f0300c6, 0x7f0300c7, 0x7f0300c9, 0x7f0300ca, 0x7f0300cb, 0x7f0300cc, 0x7f0300cd, 0x7f0300ce, 0x7f0300cf, 0x7f0300d4, 0x7f0300d5, 0x7f0300d6, 0x7f0300d8, 0x7f0300e7, 0x7f0300e8, 0x7f0300e9, 0x7f0300ea, 0x7f0300eb, 0x7f0300ec, 0x7f0300ed, 0x7f0301b9, 0x7f030237, 0x7f030245, 0x7f030249, 0x7f0303cb, 0x7f0303e2, 0x7f0303ea, 0x7f0303f4, 0x7f030497, 0x7f0304a6 };
		public static final int Chip_android_checkable = 6;
		public static final int Chip_android_ellipsize = 3;
		public static final int Chip_android_maxWidth = 4;
		public static final int Chip_android_text = 5;
		public static final int Chip_android_textAppearance = 0;
		public static final int Chip_android_textColor = 2;
		public static final int Chip_android_textSize = 1;
		public static final int Chip_checkedIcon = 7;
		public static final int Chip_checkedIconEnabled = 8;
		public static final int Chip_checkedIconTint = 9;
		public static final int Chip_checkedIconVisible = 10;
		public static final int Chip_chipBackgroundColor = 11;
		public static final int Chip_chipCornerRadius = 12;
		public static final int Chip_chipEndPadding = 13;
		public static final int Chip_chipIcon = 14;
		public static final int Chip_chipIconEnabled = 15;
		public static final int Chip_chipIconSize = 16;
		public static final int Chip_chipIconTint = 17;
		public static final int Chip_chipIconVisible = 18;
		public static final int Chip_chipMinHeight = 19;
		public static final int Chip_chipMinTouchTargetSize = 20;
		public static final int Chip_chipStartPadding = 21;
		public static final int Chip_chipStrokeColor = 22;
		public static final int Chip_chipStrokeWidth = 23;
		public static final int Chip_chipSurfaceColor = 24;
		public static final int Chip_closeIcon = 25;
		public static final int Chip_closeIconEnabled = 26;
		public static final int Chip_closeIconEndPadding = 27;
		public static final int Chip_closeIconSize = 28;
		public static final int Chip_closeIconStartPadding = 29;
		public static final int Chip_closeIconTint = 30;
		public static final int Chip_closeIconVisible = 31;
		public static final int Chip_ensureMinTouchTargetSize = 32;
		public static final int Chip_hideMotionSpec = 33;
		public static final int Chip_iconEndPadding = 34;
		public static final int Chip_iconStartPadding = 35;
		public static final int Chip_rippleColor = 36;
		public static final int Chip_shapeAppearance = 37;
		public static final int Chip_shapeAppearanceOverlay = 38;
		public static final int Chip_showMotionSpec = 39;
		public static final int Chip_textEndPadding = 40;
		public static final int Chip_textStartPadding = 41;
		public static final int[] ChipGroup = new int[] { 0x7f0300bb, 0x7f0300d0, 0x7f0300d1, 0x7f0300d2, 0x7f0303df, 0x7f030400, 0x7f030401 };
		public static final int ChipGroup_checkedChip = 0;
		public static final int ChipGroup_chipSpacing = 1;
		public static final int ChipGroup_chipSpacingHorizontal = 2;
		public static final int ChipGroup_chipSpacingVertical = 3;
		public static final int ChipGroup_selectionRequired = 4;
		public static final int ChipGroup_singleLine = 5;
		public static final int ChipGroup_singleSelection = 6;
		public static final int[] CircularProgressIndicator = new int[] { 0x7f030257, 0x7f030259, 0x7f03025a };
		public static final int CircularProgressIndicator_indicatorDirectionCircular = 0;
		public static final int CircularProgressIndicator_indicatorInset = 1;
		public static final int CircularProgressIndicator_indicatorSize = 2;
		public static final int[] ClockFaceView = new int[] { 0x7f0300e3, 0x7f0300e6 };
		public static final int ClockFaceView_clockFaceBackgroundColor = 0;
		public static final int ClockFaceView_clockNumberTextColor = 1;
		public static final int[] ClockHandView = new int[] { 0x7f0300e4, 0x7f03030e, 0x7f0303e0 };
		public static final int ClockHandView_clockHandColor = 0;
		public static final int ClockHandView_materialCircleRadius = 1;
		public static final int ClockHandView_selectorSize = 2;
		public static final int[] CollapsingToolbarLayout = new int[] { 0x7f0300f2, 0x7f0300f3, 0x7f0300f4, 0x7f030150, 0x7f0301c9, 0x7f0301ca, 0x7f0301cb, 0x7f0301cc, 0x7f0301cd, 0x7f0301ce, 0x7f0301cf, 0x7f0301d0, 0x7f0301d8, 0x7f03021a, 0x7f030329, 0x7f0303d2, 0x7f0303d4, 0x7f03042f, 0x7f0304c8, 0x7f0304ca, 0x7f0304cb, 0x7f0304d2, 0x7f0304d5, 0x7f0304d8 };
		public static final int CollapsingToolbarLayout_collapsedTitleGravity = 0;
		public static final int CollapsingToolbarLayout_collapsedTitleTextAppearance = 1;
		public static final int CollapsingToolbarLayout_collapsedTitleTextColor = 2;
		public static final int CollapsingToolbarLayout_contentScrim = 3;
		public static final int CollapsingToolbarLayout_expandedTitleGravity = 4;
		public static final int CollapsingToolbarLayout_expandedTitleMargin = 5;
		public static final int CollapsingToolbarLayout_expandedTitleMarginBottom = 6;
		public static final int CollapsingToolbarLayout_expandedTitleMarginEnd = 7;
		public static final int CollapsingToolbarLayout_expandedTitleMarginStart = 8;
		public static final int CollapsingToolbarLayout_expandedTitleMarginTop = 9;
		public static final int CollapsingToolbarLayout_expandedTitleTextAppearance = 10;
		public static final int CollapsingToolbarLayout_expandedTitleTextColor = 11;
		public static final int CollapsingToolbarLayout_extraMultilineHeightEnabled = 12;
		public static final int CollapsingToolbarLayout_forceApplySystemWindowInsetTop = 13;
		public static final int CollapsingToolbarLayout_maxLines = 14;
		public static final int CollapsingToolbarLayout_scrimAnimationDuration = 15;
		public static final int CollapsingToolbarLayout_scrimVisibleHeightTrigger = 16;
		public static final int CollapsingToolbarLayout_statusBarScrim = 17;
		public static final int CollapsingToolbarLayout_title = 18;
		public static final int CollapsingToolbarLayout_titleCollapseMode = 19;
		public static final int CollapsingToolbarLayout_titleEnabled = 20;
		public static final int CollapsingToolbarLayout_titlePositionInterpolator = 21;
		public static final int CollapsingToolbarLayout_titleTextEllipsize = 22;
		public static final int CollapsingToolbarLayout_toolbarId = 23;
		public static final int[] CollapsingToolbarLayout_Layout = new int[] { 0x7f030292, 0x7f030293 };
		public static final int CollapsingToolbarLayout_Layout_layout_collapseMode = 0;
		public static final int CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier = 1;
		public static final int[] ColorStateListItem = new int[] { 0x010101a5, 0x0101031f, 0x01010647, 0x7f030031, 0x7f030283 };
		public static final int ColorStateListItem_alpha = 3;
		public static final int ColorStateListItem_android_alpha = 1;
		public static final int ColorStateListItem_android_color = 0;
		public static final int ColorStateListItem_android_lStar = 2;
		public static final int ColorStateListItem_lStar = 4;
		public static final int[] CompoundButton = new int[] { 0x01010107, 0x7f030096, 0x7f03009f, 0x7f0300a0 };
		public static final int CompoundButton_android_button = 0;
		public static final int CompoundButton_buttonCompat = 1;
		public static final int CompoundButton_buttonTint = 2;
		public static final int CompoundButton_buttonTintMode = 3;
		public static final int[] Constraint = new int[] { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f030035, 0x7f030038, 0x7f03006a, 0x7f03006b, 0x7f03006c, 0x7f0300b5, 0x7f03013e, 0x7f03013f, 0x7f030194, 0x7f0301fa, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030202, 0x7f030203, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030208, 0x7f030209, 0x7f03020a, 0x7f03020b, 0x7f03020c, 0x7f03022d, 0x7f030294, 0x7f030295, 0x7f030296, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a0, 0x7f0302a1, 0x7f0302a2, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a6, 0x7f0302a7, 0x7f0302a8, 0x7f0302a9, 0x7f0302aa, 0x7f0302ab, 0x7f0302ac, 0x7f0302ad, 0x7f0302ae, 0x7f0302af, 0x7f0302b0, 0x7f0302b1, 0x7f0302b2, 0x7f0302b3, 0x7f0302b4, 0x7f0302b5, 0x7f0302b6, 0x7f0302b7, 0x7f0302b8, 0x7f0302b9, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302bd, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1, 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f0302c6, 0x7f0302c7, 0x7f0302c8, 0x7f0302c9, 0x7f0302ca, 0x7f0302cb, 0x7f0302ce, 0x7f0302d3, 0x7f030366, 0x7f030367, 0x7f030395, 0x7f03039c, 0x7f0303a2, 0x7f0303b4, 0x7f0303b5, 0x7f0303b6, 0x7f0304f2, 0x7f0304f4, 0x7f0304f6, 0x7f030509 };
		public static final int Constraint_android_alpha = 13;
		public static final int Constraint_android_elevation = 26;
		public static final int Constraint_android_id = 1;
		public static final int Constraint_android_layout_height = 4;
		public static final int Constraint_android_layout_marginBottom = 8;
		public static final int Constraint_android_layout_marginEnd = 24;
		public static final int Constraint_android_layout_marginLeft = 5;
		public static final int Constraint_android_layout_marginRight = 7;
		public static final int Constraint_android_layout_marginStart = 23;
		public static final int Constraint_android_layout_marginTop = 6;
		public static final int Constraint_android_layout_width = 3;
		public static final int Constraint_android_maxHeight = 10;
		public static final int Constraint_android_maxWidth = 9;
		public static final int Constraint_android_minHeight = 12;
		public static final int Constraint_android_minWidth = 11;
		public static final int Constraint_android_orientation = 0;
		public static final int Constraint_android_rotation = 20;
		public static final int Constraint_android_rotationX = 21;
		public static final int Constraint_android_rotationY = 22;
		public static final int Constraint_android_scaleX = 18;
		public static final int Constraint_android_scaleY = 19;
		public static final int Constraint_android_transformPivotX = 14;
		public static final int Constraint_android_transformPivotY = 15;
		public static final int Constraint_android_translationX = 16;
		public static final int Constraint_android_translationY = 17;
		public static final int Constraint_android_translationZ = 25;
		public static final int Constraint_android_visibility = 2;
		public static final int Constraint_barrierAllowsGoneWidgets = 29;
		public static final int Constraint_barrierDirection = 30;
		public static final int Constraint_barrierMargin = 31;
		public static final int Constraint_chainUseRtl = 32;
		public static final int Constraint_constraint_referenced_ids = 33;
		public static final int Constraint_drawPath = 35;
		public static final int Constraint_flow_firstHorizontalBias = 36;
		public static final int Constraint_flow_firstHorizontalStyle = 37;
		public static final int Constraint_flow_firstVerticalBias = 38;
		public static final int Constraint_flow_firstVerticalStyle = 39;
		public static final int Constraint_flow_horizontalAlign = 40;
		public static final int Constraint_flow_horizontalBias = 41;
		public static final int Constraint_flow_horizontalGap = 42;
		public static final int Constraint_flow_horizontalStyle = 43;
		public static final int Constraint_flow_lastHorizontalBias = 44;
		public static final int Constraint_flow_lastHorizontalStyle = 45;
		public static final int Constraint_flow_lastVerticalBias = 46;
		public static final int Constraint_flow_lastVerticalStyle = 47;
		public static final int Constraint_flow_maxElementsWrap = 48;
		public static final int Constraint_flow_verticalAlign = 49;
		public static final int Constraint_flow_verticalBias = 50;
		public static final int Constraint_flow_verticalGap = 51;
		public static final int Constraint_flow_verticalStyle = 52;
		public static final int Constraint_flow_wrapMode = 53;
		public static final int Constraint_layout_constrainedHeight = 55;
		public static final int Constraint_layout_constrainedWidth = 56;
		public static final int Constraint_layout_constraintBaseline_creator = 57;
		public static final int Constraint_layout_constraintBaseline_toBaselineOf = 58;
		public static final int Constraint_layout_constraintBottom_creator = 61;
		public static final int Constraint_layout_constraintBottom_toBottomOf = 62;
		public static final int Constraint_layout_constraintBottom_toTopOf = 63;
		public static final int Constraint_layout_constraintCircle = 64;
		public static final int Constraint_layout_constraintCircleAngle = 65;
		public static final int Constraint_layout_constraintCircleRadius = 66;
		public static final int Constraint_layout_constraintDimensionRatio = 67;
		public static final int Constraint_layout_constraintEnd_toEndOf = 68;
		public static final int Constraint_layout_constraintEnd_toStartOf = 69;
		public static final int Constraint_layout_constraintGuide_begin = 70;
		public static final int Constraint_layout_constraintGuide_end = 71;
		public static final int Constraint_layout_constraintGuide_percent = 72;
		public static final int Constraint_layout_constraintHeight_default = 74;
		public static final int Constraint_layout_constraintHeight_max = 75;
		public static final int Constraint_layout_constraintHeight_min = 76;
		public static final int Constraint_layout_constraintHeight_percent = 77;
		public static final int Constraint_layout_constraintHorizontal_bias = 78;
		public static final int Constraint_layout_constraintHorizontal_chainStyle = 79;
		public static final int Constraint_layout_constraintHorizontal_weight = 80;
		public static final int Constraint_layout_constraintLeft_creator = 81;
		public static final int Constraint_layout_constraintLeft_toLeftOf = 82;
		public static final int Constraint_layout_constraintLeft_toRightOf = 83;
		public static final int Constraint_layout_constraintRight_creator = 84;
		public static final int Constraint_layout_constraintRight_toLeftOf = 85;
		public static final int Constraint_layout_constraintRight_toRightOf = 86;
		public static final int Constraint_layout_constraintStart_toEndOf = 87;
		public static final int Constraint_layout_constraintStart_toStartOf = 88;
		public static final int Constraint_layout_constraintTag = 89;
		public static final int Constraint_layout_constraintTop_creator = 90;
		public static final int Constraint_layout_constraintTop_toBottomOf = 91;
		public static final int Constraint_layout_constraintTop_toTopOf = 92;
		public static final int Constraint_layout_constraintVertical_bias = 93;
		public static final int Constraint_layout_constraintVertical_chainStyle = 94;
		public static final int Constraint_layout_constraintVertical_weight = 95;
		public static final int Constraint_layout_constraintWidth_default = 97;
		public static final int Constraint_layout_constraintWidth_max = 98;
		public static final int Constraint_layout_constraintWidth_min = 99;
		public static final int Constraint_layout_constraintWidth_percent = 100;
		public static final int Constraint_layout_editor_absoluteX = 101;
		public static final int Constraint_layout_editor_absoluteY = 102;
		public static final int Constraint_layout_goneMarginBottom = 104;
		public static final int Constraint_layout_goneMarginEnd = 105;
		public static final int Constraint_layout_goneMarginLeft = 106;
		public static final int Constraint_layout_goneMarginRight = 107;
		public static final int Constraint_layout_goneMarginStart = 108;
		public static final int Constraint_layout_goneMarginTop = 109;
		public static final int Constraint_motionProgress = 112;
		public static final int Constraint_motionStagger = 113;
		public static final int Constraint_pathMotionArc = 114;
		public static final int Constraint_pivotAnchor = 115;
		public static final int Constraint_transitionEasing = 121;
		public static final int Constraint_transitionPathRotate = 122;
		public static final int Constraint_visibilityMode = 123;
		public static final int[] ConstraintLayout_Layout = new int[] { 0x010100c4, 0x010100d5, 0x010100d6, 0x010100d7, 0x010100d8, 0x010100d9, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f6, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010103b3, 0x010103b4, 0x010103b5, 0x010103b6, 0x01010440, 0x0101053b, 0x0101053c, 0x7f03006a, 0x7f03006b, 0x7f03006c, 0x7f0300b5, 0x7f0300db, 0x7f0300dc, 0x7f0300dd, 0x7f0300de, 0x7f0300df, 0x7f03013b, 0x7f03013e, 0x7f03013f, 0x7f0301fa, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030202, 0x7f030203, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030208, 0x7f030209, 0x7f03020a, 0x7f03020b, 0x7f03020c, 0x7f03022d, 0x7f03028c, 0x7f030294, 0x7f030295, 0x7f030296, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a0, 0x7f0302a1, 0x7f0302a2, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a6, 0x7f0302a7, 0x7f0302a8, 0x7f0302a9, 0x7f0302aa, 0x7f0302ab, 0x7f0302ac, 0x7f0302ad, 0x7f0302ae, 0x7f0302af, 0x7f0302b0, 0x7f0302b1, 0x7f0302b2, 0x7f0302b3, 0x7f0302b4, 0x7f0302b5, 0x7f0302b6, 0x7f0302b7, 0x7f0302b8, 0x7f0302b9, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302bd, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1, 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f0302c6, 0x7f0302c7, 0x7f0302c8, 0x7f0302c9, 0x7f0302ca, 0x7f0302cb, 0x7f0302ce, 0x7f0302cf, 0x7f0302d3 };
		public static final int ConstraintLayout_Layout_android_elevation = 22;
		public static final int ConstraintLayout_Layout_android_maxHeight = 15;
		public static final int ConstraintLayout_Layout_android_maxWidth = 14;
		public static final int ConstraintLayout_Layout_android_minHeight = 17;
		public static final int ConstraintLayout_Layout_android_minWidth = 16;
		public static final int ConstraintLayout_Layout_android_orientation = 0;
		public static final int ConstraintLayout_Layout_android_padding = 1;
		public static final int ConstraintLayout_Layout_android_paddingBottom = 5;
		public static final int ConstraintLayout_Layout_android_paddingEnd = 19;
		public static final int ConstraintLayout_Layout_android_paddingLeft = 2;
		public static final int ConstraintLayout_Layout_android_paddingRight = 4;
		public static final int ConstraintLayout_Layout_android_paddingStart = 18;
		public static final int ConstraintLayout_Layout_android_paddingTop = 3;
		public static final int ConstraintLayout_Layout_android_visibility = 6;
		public static final int ConstraintLayout_Layout_barrierAllowsGoneWidgets = 25;
		public static final int ConstraintLayout_Layout_barrierDirection = 26;
		public static final int ConstraintLayout_Layout_barrierMargin = 27;
		public static final int ConstraintLayout_Layout_chainUseRtl = 28;
		public static final int ConstraintLayout_Layout_constraintSet = 34;
		public static final int ConstraintLayout_Layout_constraint_referenced_ids = 35;
		public static final int ConstraintLayout_Layout_flow_firstHorizontalBias = 37;
		public static final int ConstraintLayout_Layout_flow_firstHorizontalStyle = 38;
		public static final int ConstraintLayout_Layout_flow_firstVerticalBias = 39;
		public static final int ConstraintLayout_Layout_flow_firstVerticalStyle = 40;
		public static final int ConstraintLayout_Layout_flow_horizontalAlign = 41;
		public static final int ConstraintLayout_Layout_flow_horizontalBias = 42;
		public static final int ConstraintLayout_Layout_flow_horizontalGap = 43;
		public static final int ConstraintLayout_Layout_flow_horizontalStyle = 44;
		public static final int ConstraintLayout_Layout_flow_lastHorizontalBias = 45;
		public static final int ConstraintLayout_Layout_flow_lastHorizontalStyle = 46;
		public static final int ConstraintLayout_Layout_flow_lastVerticalBias = 47;
		public static final int ConstraintLayout_Layout_flow_lastVerticalStyle = 48;
		public static final int ConstraintLayout_Layout_flow_maxElementsWrap = 49;
		public static final int ConstraintLayout_Layout_flow_verticalAlign = 50;
		public static final int ConstraintLayout_Layout_flow_verticalBias = 51;
		public static final int ConstraintLayout_Layout_flow_verticalGap = 52;
		public static final int ConstraintLayout_Layout_flow_verticalStyle = 53;
		public static final int ConstraintLayout_Layout_flow_wrapMode = 54;
		public static final int ConstraintLayout_Layout_layoutDescription = 56;
		public static final int ConstraintLayout_Layout_layout_constrainedHeight = 57;
		public static final int ConstraintLayout_Layout_layout_constrainedWidth = 58;
		public static final int ConstraintLayout_Layout_layout_constraintBaseline_creator = 59;
		public static final int ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf = 60;
		public static final int ConstraintLayout_Layout_layout_constraintBottom_creator = 63;
		public static final int ConstraintLayout_Layout_layout_constraintBottom_toBottomOf = 64;
		public static final int ConstraintLayout_Layout_layout_constraintBottom_toTopOf = 65;
		public static final int ConstraintLayout_Layout_layout_constraintCircle = 66;
		public static final int ConstraintLayout_Layout_layout_constraintCircleAngle = 67;
		public static final int ConstraintLayout_Layout_layout_constraintCircleRadius = 68;
		public static final int ConstraintLayout_Layout_layout_constraintDimensionRatio = 69;
		public static final int ConstraintLayout_Layout_layout_constraintEnd_toEndOf = 70;
		public static final int ConstraintLayout_Layout_layout_constraintEnd_toStartOf = 71;
		public static final int ConstraintLayout_Layout_layout_constraintGuide_begin = 72;
		public static final int ConstraintLayout_Layout_layout_constraintGuide_end = 73;
		public static final int ConstraintLayout_Layout_layout_constraintGuide_percent = 74;
		public static final int ConstraintLayout_Layout_layout_constraintHeight_default = 76;
		public static final int ConstraintLayout_Layout_layout_constraintHeight_max = 77;
		public static final int ConstraintLayout_Layout_layout_constraintHeight_min = 78;
		public static final int ConstraintLayout_Layout_layout_constraintHeight_percent = 79;
		public static final int ConstraintLayout_Layout_layout_constraintHorizontal_bias = 80;
		public static final int ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle = 81;
		public static final int ConstraintLayout_Layout_layout_constraintHorizontal_weight = 82;
		public static final int ConstraintLayout_Layout_layout_constraintLeft_creator = 83;
		public static final int ConstraintLayout_Layout_layout_constraintLeft_toLeftOf = 84;
		public static final int ConstraintLayout_Layout_layout_constraintLeft_toRightOf = 85;
		public static final int ConstraintLayout_Layout_layout_constraintRight_creator = 86;
		public static final int ConstraintLayout_Layout_layout_constraintRight_toLeftOf = 87;
		public static final int ConstraintLayout_Layout_layout_constraintRight_toRightOf = 88;
		public static final int ConstraintLayout_Layout_layout_constraintStart_toEndOf = 89;
		public static final int ConstraintLayout_Layout_layout_constraintStart_toStartOf = 90;
		public static final int ConstraintLayout_Layout_layout_constraintTag = 91;
		public static final int ConstraintLayout_Layout_layout_constraintTop_creator = 92;
		public static final int ConstraintLayout_Layout_layout_constraintTop_toBottomOf = 93;
		public static final int ConstraintLayout_Layout_layout_constraintTop_toTopOf = 94;
		public static final int ConstraintLayout_Layout_layout_constraintVertical_bias = 95;
		public static final int ConstraintLayout_Layout_layout_constraintVertical_chainStyle = 96;
		public static final int ConstraintLayout_Layout_layout_constraintVertical_weight = 97;
		public static final int ConstraintLayout_Layout_layout_constraintWidth_default = 99;
		public static final int ConstraintLayout_Layout_layout_constraintWidth_max = 100;
		public static final int ConstraintLayout_Layout_layout_constraintWidth_min = 101;
		public static final int ConstraintLayout_Layout_layout_constraintWidth_percent = 102;
		public static final int ConstraintLayout_Layout_layout_editor_absoluteX = 103;
		public static final int ConstraintLayout_Layout_layout_editor_absoluteY = 104;
		public static final int ConstraintLayout_Layout_layout_goneMarginBottom = 106;
		public static final int ConstraintLayout_Layout_layout_goneMarginEnd = 107;
		public static final int ConstraintLayout_Layout_layout_goneMarginLeft = 108;
		public static final int ConstraintLayout_Layout_layout_goneMarginRight = 109;
		public static final int ConstraintLayout_Layout_layout_goneMarginStart = 110;
		public static final int ConstraintLayout_Layout_layout_goneMarginTop = 111;
		public static final int ConstraintLayout_Layout_layout_optimizationLevel = 113;
		public static final int[] ConstraintLayout_placeholder = new int[] { 0x7f030141, 0x7f0303a1 };
		public static final int ConstraintLayout_placeholder_content = 0;
		public static final int ConstraintLayout_placeholder_placeholder_emptyVisibility = 1;
		public static final int[] ConstraintSet = new int[] { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010101b5, 0x010101b6, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f030035, 0x7f030038, 0x7f03006a, 0x7f03006b, 0x7f03006c, 0x7f0300b5, 0x7f03013a, 0x7f03013e, 0x7f03013f, 0x7f030183, 0x7f030194, 0x7f0301fa, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030202, 0x7f030203, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030208, 0x7f030209, 0x7f03020a, 0x7f03020b, 0x7f03020c, 0x7f03022d, 0x7f030294, 0x7f030295, 0x7f030296, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a0, 0x7f0302a1, 0x7f0302a2, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a7, 0x7f0302a8, 0x7f0302a9, 0x7f0302aa, 0x7f0302ab, 0x7f0302ac, 0x7f0302ad, 0x7f0302ae, 0x7f0302af, 0x7f0302b0, 0x7f0302b1, 0x7f0302b2, 0x7f0302b3, 0x7f0302b4, 0x7f0302b5, 0x7f0302b6, 0x7f0302b7, 0x7f0302b8, 0x7f0302b9, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1, 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f0302c6, 0x7f0302c7, 0x7f0302c8, 0x7f0302c9, 0x7f0302ca, 0x7f0302cb, 0x7f0302ce, 0x7f0302d3, 0x7f030366, 0x7f030367, 0x7f030395, 0x7f03039c, 0x7f0303a2, 0x7f0303b6, 0x7f030423, 0x7f0304f4, 0x7f0304f6 };
		public static final int ConstraintSet_android_alpha = 15;
		public static final int ConstraintSet_android_elevation = 28;
		public static final int ConstraintSet_android_id = 1;
		public static final int ConstraintSet_android_layout_height = 4;
		public static final int ConstraintSet_android_layout_marginBottom = 8;
		public static final int ConstraintSet_android_layout_marginEnd = 26;
		public static final int ConstraintSet_android_layout_marginLeft = 5;
		public static final int ConstraintSet_android_layout_marginRight = 7;
		public static final int ConstraintSet_android_layout_marginStart = 25;
		public static final int ConstraintSet_android_layout_marginTop = 6;
		public static final int ConstraintSet_android_layout_width = 3;
		public static final int ConstraintSet_android_maxHeight = 10;
		public static final int ConstraintSet_android_maxWidth = 9;
		public static final int ConstraintSet_android_minHeight = 12;
		public static final int ConstraintSet_android_minWidth = 11;
		public static final int ConstraintSet_android_orientation = 0;
		public static final int ConstraintSet_android_pivotX = 13;
		public static final int ConstraintSet_android_pivotY = 14;
		public static final int ConstraintSet_android_rotation = 22;
		public static final int ConstraintSet_android_rotationX = 23;
		public static final int ConstraintSet_android_rotationY = 24;
		public static final int ConstraintSet_android_scaleX = 20;
		public static final int ConstraintSet_android_scaleY = 21;
		public static final int ConstraintSet_android_transformPivotX = 16;
		public static final int ConstraintSet_android_transformPivotY = 17;
		public static final int ConstraintSet_android_translationX = 18;
		public static final int ConstraintSet_android_translationY = 19;
		public static final int ConstraintSet_android_translationZ = 27;
		public static final int ConstraintSet_android_visibility = 2;
		public static final int ConstraintSet_barrierAllowsGoneWidgets = 31;
		public static final int ConstraintSet_barrierDirection = 32;
		public static final int ConstraintSet_barrierMargin = 33;
		public static final int ConstraintSet_chainUseRtl = 34;
		public static final int ConstraintSet_constraint_referenced_ids = 36;
		public static final int ConstraintSet_deriveConstraintsFrom = 38;
		public static final int ConstraintSet_drawPath = 39;
		public static final int ConstraintSet_flow_firstHorizontalBias = 40;
		public static final int ConstraintSet_flow_firstHorizontalStyle = 41;
		public static final int ConstraintSet_flow_firstVerticalBias = 42;
		public static final int ConstraintSet_flow_firstVerticalStyle = 43;
		public static final int ConstraintSet_flow_horizontalAlign = 44;
		public static final int ConstraintSet_flow_horizontalBias = 45;
		public static final int ConstraintSet_flow_horizontalGap = 46;
		public static final int ConstraintSet_flow_horizontalStyle = 47;
		public static final int ConstraintSet_flow_lastHorizontalBias = 48;
		public static final int ConstraintSet_flow_lastHorizontalStyle = 49;
		public static final int ConstraintSet_flow_lastVerticalBias = 50;
		public static final int ConstraintSet_flow_lastVerticalStyle = 51;
		public static final int ConstraintSet_flow_maxElementsWrap = 52;
		public static final int ConstraintSet_flow_verticalAlign = 53;
		public static final int ConstraintSet_flow_verticalBias = 54;
		public static final int ConstraintSet_flow_verticalGap = 55;
		public static final int ConstraintSet_flow_verticalStyle = 56;
		public static final int ConstraintSet_flow_wrapMode = 57;
		public static final int ConstraintSet_layout_constrainedHeight = 59;
		public static final int ConstraintSet_layout_constrainedWidth = 60;
		public static final int ConstraintSet_layout_constraintBaseline_creator = 61;
		public static final int ConstraintSet_layout_constraintBaseline_toBaselineOf = 62;
		public static final int ConstraintSet_layout_constraintBottom_creator = 65;
		public static final int ConstraintSet_layout_constraintBottom_toBottomOf = 66;
		public static final int ConstraintSet_layout_constraintBottom_toTopOf = 67;
		public static final int ConstraintSet_layout_constraintCircle = 68;
		public static final int ConstraintSet_layout_constraintCircleAngle = 69;
		public static final int ConstraintSet_layout_constraintCircleRadius = 70;
		public static final int ConstraintSet_layout_constraintDimensionRatio = 71;
		public static final int ConstraintSet_layout_constraintEnd_toEndOf = 72;
		public static final int ConstraintSet_layout_constraintEnd_toStartOf = 73;
		public static final int ConstraintSet_layout_constraintGuide_begin = 74;
		public static final int ConstraintSet_layout_constraintGuide_end = 75;
		public static final int ConstraintSet_layout_constraintGuide_percent = 76;
		public static final int ConstraintSet_layout_constraintHeight_default = 77;
		public static final int ConstraintSet_layout_constraintHeight_max = 78;
		public static final int ConstraintSet_layout_constraintHeight_min = 79;
		public static final int ConstraintSet_layout_constraintHeight_percent = 80;
		public static final int ConstraintSet_layout_constraintHorizontal_bias = 81;
		public static final int ConstraintSet_layout_constraintHorizontal_chainStyle = 82;
		public static final int ConstraintSet_layout_constraintHorizontal_weight = 83;
		public static final int ConstraintSet_layout_constraintLeft_creator = 84;
		public static final int ConstraintSet_layout_constraintLeft_toLeftOf = 85;
		public static final int ConstraintSet_layout_constraintLeft_toRightOf = 86;
		public static final int ConstraintSet_layout_constraintRight_creator = 87;
		public static final int ConstraintSet_layout_constraintRight_toLeftOf = 88;
		public static final int ConstraintSet_layout_constraintRight_toRightOf = 89;
		public static final int ConstraintSet_layout_constraintStart_toEndOf = 90;
		public static final int ConstraintSet_layout_constraintStart_toStartOf = 91;
		public static final int ConstraintSet_layout_constraintTag = 92;
		public static final int ConstraintSet_layout_constraintTop_creator = 93;
		public static final int ConstraintSet_layout_constraintTop_toBottomOf = 94;
		public static final int ConstraintSet_layout_constraintTop_toTopOf = 95;
		public static final int ConstraintSet_layout_constraintVertical_bias = 96;
		public static final int ConstraintSet_layout_constraintVertical_chainStyle = 97;
		public static final int ConstraintSet_layout_constraintVertical_weight = 98;
		public static final int ConstraintSet_layout_constraintWidth_default = 99;
		public static final int ConstraintSet_layout_constraintWidth_max = 100;
		public static final int ConstraintSet_layout_constraintWidth_min = 101;
		public static final int ConstraintSet_layout_constraintWidth_percent = 102;
		public static final int ConstraintSet_layout_editor_absoluteX = 103;
		public static final int ConstraintSet_layout_editor_absoluteY = 104;
		public static final int ConstraintSet_layout_goneMarginBottom = 106;
		public static final int ConstraintSet_layout_goneMarginEnd = 107;
		public static final int ConstraintSet_layout_goneMarginLeft = 108;
		public static final int ConstraintSet_layout_goneMarginRight = 109;
		public static final int ConstraintSet_layout_goneMarginStart = 110;
		public static final int ConstraintSet_layout_goneMarginTop = 111;
		public static final int ConstraintSet_motionProgress = 114;
		public static final int ConstraintSet_motionStagger = 115;
		public static final int ConstraintSet_pathMotionArc = 116;
		public static final int ConstraintSet_pivotAnchor = 117;
		public static final int ConstraintSet_transitionEasing = 121;
		public static final int ConstraintSet_transitionPathRotate = 122;
		public static final int[] CoordinatorLayout = new int[] { 0x7f030282, 0x7f03042d };
		public static final int CoordinatorLayout_keylines = 0;
		public static final int CoordinatorLayout_statusBarBackground = 1;
		public static final int[] CoordinatorLayout_Layout = new int[] { 0x010100b3, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f0302c2, 0x7f0302cc, 0x7f0302cd };
		public static final int CoordinatorLayout_Layout_android_layout_gravity = 0;
		public static final int CoordinatorLayout_Layout_layout_anchor = 1;
		public static final int CoordinatorLayout_Layout_layout_anchorGravity = 2;
		public static final int CoordinatorLayout_Layout_layout_behavior = 3;
		public static final int CoordinatorLayout_Layout_layout_dodgeInsetEdges = 4;
		public static final int CoordinatorLayout_Layout_layout_insetEdge = 5;
		public static final int CoordinatorLayout_Layout_layout_keyline = 6;
		public static final int[] CustomAttribute = new int[] { 0x7f030041, 0x7f03016b, 0x7f03016c, 0x7f03016d, 0x7f03016e, 0x7f03016f, 0x7f030170, 0x7f030172, 0x7f030173, 0x7f030174, 0x7f030331 };
		public static final int CustomAttribute_attributeName = 0;
		public static final int CustomAttribute_customBoolean = 1;
		public static final int CustomAttribute_customColorDrawableValue = 2;
		public static final int CustomAttribute_customColorValue = 3;
		public static final int CustomAttribute_customDimension = 4;
		public static final int CustomAttribute_customFloatValue = 5;
		public static final int CustomAttribute_customIntegerValue = 6;
		public static final int CustomAttribute_customPixelDimension = 7;
		public static final int CustomAttribute_customStringValue = 9;
		public static final int[] DrawerArrowToggle = new int[] { 0x7f03003f, 0x7f030040, 0x7f030069, 0x7f0300fb, 0x7f030199, 0x7f03021e, 0x7f030408, 0x7f0304ac };
		public static final int DrawerArrowToggle_arrowHeadLength = 0;
		public static final int DrawerArrowToggle_arrowShaftLength = 1;
		public static final int DrawerArrowToggle_barLength = 2;
		public static final int DrawerArrowToggle_color = 3;
		public static final int DrawerArrowToggle_drawableSize = 4;
		public static final int DrawerArrowToggle_gapBetweenBars = 5;
		public static final int DrawerArrowToggle_spinBars = 6;
		public static final int DrawerArrowToggle_thickness = 7;
		public static final int[] DrawerLayout = new int[] { 0x7f0301a9 };
		public static final int DrawerLayout_elevation = 0;
		public static final int[] ExtendedFloatingActionButton = new int[] { 0x7f0300f1, 0x7f0301a9, 0x7f0301d1, 0x7f0301d2, 0x7f030237, 0x7f0303f4, 0x7f0303f8 };
		public static final int ExtendedFloatingActionButton_collapsedSize = 0;
		public static final int ExtendedFloatingActionButton_elevation = 1;
		public static final int ExtendedFloatingActionButton_extendMotionSpec = 2;
		public static final int ExtendedFloatingActionButton_extendStrategy = 3;
		public static final int ExtendedFloatingActionButton_hideMotionSpec = 4;
		public static final int ExtendedFloatingActionButton_showMotionSpec = 5;
		public static final int ExtendedFloatingActionButton_shrinkMotionSpec = 6;
		public static final int[] ExtendedFloatingActionButton_Behavior_Layout = new int[] { 0x7f03006d, 0x7f03006e };
		public static final int ExtendedFloatingActionButton_Behavior_Layout_behavior_autoHide = 0;
		public static final int ExtendedFloatingActionButton_Behavior_Layout_behavior_autoShrink = 1;
		public static final int[] FloatingActionButton = new int[] { 0x0101000e, 0x7f030056, 0x7f030057, 0x7f03007c, 0x7f0301a9, 0x7f0301b9, 0x7f0301e0, 0x7f0301e1, 0x7f030237, 0x7f030243, 0x7f030328, 0x7f0303b0, 0x7f0303cb, 0x7f0303e2, 0x7f0303ea, 0x7f0303f4, 0x7f0304fe };
		public static final int FloatingActionButton_android_enabled = 0;
		public static final int FloatingActionButton_backgroundTint = 1;
		public static final int FloatingActionButton_backgroundTintMode = 2;
		public static final int FloatingActionButton_borderWidth = 3;
		public static final int FloatingActionButton_elevation = 4;
		public static final int FloatingActionButton_ensureMinTouchTargetSize = 5;
		public static final int FloatingActionButton_fabCustomSize = 6;
		public static final int FloatingActionButton_fabSize = 7;
		public static final int FloatingActionButton_hideMotionSpec = 8;
		public static final int FloatingActionButton_hoveredFocusedTranslationZ = 9;
		public static final int FloatingActionButton_maxImageSize = 10;
		public static final int FloatingActionButton_pressedTranslationZ = 11;
		public static final int FloatingActionButton_rippleColor = 12;
		public static final int FloatingActionButton_shapeAppearance = 13;
		public static final int FloatingActionButton_shapeAppearanceOverlay = 14;
		public static final int FloatingActionButton_showMotionSpec = 15;
		public static final int FloatingActionButton_useCompatPadding = 16;
		public static final int[] FloatingActionButton_Behavior_Layout = new int[] { 0x7f03006d };
		public static final int FloatingActionButton_Behavior_Layout_behavior_autoHide = 0;
		public static final int[] FlowLayout = new int[] { 0x7f030277, 0x7f0302d9 };
		public static final int FlowLayout_itemSpacing = 0;
		public static final int FlowLayout_lineSpacing = 1;
		public static final int[] FontFamily = new int[] { 0x7f03020f, 0x7f030210, 0x7f030211, 0x7f030212, 0x7f030213, 0x7f030214, 0x7f030215, 0x7f030216 };
		public static final int FontFamily_fontProviderAuthority = 0;
		public static final int FontFamily_fontProviderCerts = 1;
		public static final int FontFamily_fontProviderFetchStrategy = 3;
		public static final int FontFamily_fontProviderFetchTimeout = 4;
		public static final int FontFamily_fontProviderPackage = 5;
		public static final int FontFamily_fontProviderQuery = 6;
		public static final int FontFamily_fontProviderSystemFontFamily = 7;
		public static final int[] FontFamilyFont = new int[] { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f03020d, 0x7f030217, 0x7f030218, 0x7f030219, 0x7f0304fb };
		public static final int FontFamilyFont_android_font = 0;
		public static final int FontFamilyFont_android_fontStyle = 2;
		public static final int FontFamilyFont_android_fontVariationSettings = 4;
		public static final int FontFamilyFont_android_fontWeight = 1;
		public static final int FontFamilyFont_android_ttcIndex = 3;
		public static final int FontFamilyFont_font = 5;
		public static final int FontFamilyFont_fontStyle = 6;
		public static final int FontFamilyFont_fontVariationSettings = 7;
		public static final int FontFamilyFont_fontWeight = 8;
		public static final int FontFamilyFont_ttcIndex = 9;
		public static final int[] ForegroundLinearLayout = new int[] { 0x01010109, 0x01010200, 0x7f03021c };
		public static final int ForegroundLinearLayout_android_foreground = 0;
		public static final int ForegroundLinearLayout_android_foregroundGravity = 1;
		public static final int ForegroundLinearLayout_foregroundInsidePadding = 2;
		public static final int[] Fragment = new int[] { 0x01010003, 0x010100d0, 0x010100d1 };
		public static final int Fragment_android_id = 1;
		public static final int Fragment_android_name = 0;
		public static final int Fragment_android_tag = 2;
		public static final int[] FragmentContainerView = new int[] { 0x01010003, 0x010100d1 };
		public static final int FragmentContainerView_android_name = 0;
		public static final int FragmentContainerView_android_tag = 1;
		public static final int[] GradientColor = new int[] { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 };
		public static final int GradientColor_android_centerColor = 7;
		public static final int GradientColor_android_centerX = 3;
		public static final int GradientColor_android_centerY = 4;
		public static final int GradientColor_android_endColor = 1;
		public static final int GradientColor_android_endX = 10;
		public static final int GradientColor_android_endY = 11;
		public static final int GradientColor_android_gradientRadius = 5;
		public static final int GradientColor_android_startColor = 0;
		public static final int GradientColor_android_startX = 8;
		public static final int GradientColor_android_startY = 9;
		public static final int GradientColor_android_tileMode = 6;
		public static final int GradientColor_android_type = 2;
		public static final int[] GradientColorItem = new int[] { 0x010101a5, 0x01010514 };
		public static final int GradientColorItem_android_color = 0;
		public static final int GradientColorItem_android_offset = 1;
		public static final int[] ImageFilterView = new int[] { 0x7f030033, 0x7f030079, 0x7f030090, 0x7f030151, 0x7f030166, 0x7f030250, 0x7f030251, 0x7f030252, 0x7f030253, 0x7f030383, 0x7f0303cd, 0x7f0303ce, 0x7f0303d0, 0x7f03050b };
		public static final int ImageFilterView_altSrc = 0;
		public static final int ImageFilterView_brightness = 2;
		public static final int ImageFilterView_contrast = 3;
		public static final int ImageFilterView_crossfade = 4;
		public static final int ImageFilterView_overlay = 9;
		public static final int ImageFilterView_round = 10;
		public static final int ImageFilterView_roundPercent = 11;
		public static final int ImageFilterView_saturation = 12;
		public static final int ImageFilterView_warmth = 13;
		public static final int[] Insets = new int[] { 0x7f0302ef, 0x7f0302f0, 0x7f0302f1, 0x7f030385, 0x7f030387, 0x7f030388, 0x7f03038a, 0x7f03038c };
		public static final int Insets_marginLeftSystemWindowInsets = 0;
		public static final int Insets_marginRightSystemWindowInsets = 1;
		public static final int Insets_marginTopSystemWindowInsets = 2;
		public static final int Insets_paddingBottomSystemWindowInsets = 3;
		public static final int Insets_paddingLeftSystemWindowInsets = 4;
		public static final int Insets_paddingRightSystemWindowInsets = 5;
		public static final int Insets_paddingStartSystemWindowInsets = 6;
		public static final int Insets_paddingTopSystemWindowInsets = 7;
		public static final int[] KeyAttribute = new int[] { 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f03016a, 0x7f03021d, 0x7f030366, 0x7f030368, 0x7f0304f2, 0x7f0304f4, 0x7f0304f6 };
		public static final int KeyAttribute_android_alpha = 0;
		public static final int KeyAttribute_android_elevation = 11;
		public static final int KeyAttribute_android_rotation = 7;
		public static final int KeyAttribute_android_rotationX = 8;
		public static final int KeyAttribute_android_rotationY = 9;
		public static final int KeyAttribute_android_scaleX = 5;
		public static final int KeyAttribute_android_scaleY = 6;
		public static final int KeyAttribute_android_transformPivotX = 1;
		public static final int KeyAttribute_android_transformPivotY = 2;
		public static final int KeyAttribute_android_translationX = 3;
		public static final int KeyAttribute_android_translationY = 4;
		public static final int KeyAttribute_android_translationZ = 10;
		public static final int KeyAttribute_curveFit = 12;
		public static final int KeyAttribute_framePosition = 13;
		public static final int KeyAttribute_motionProgress = 14;
		public static final int KeyAttribute_motionTarget = 15;
		public static final int KeyAttribute_transitionEasing = 17;
		public static final int KeyAttribute_transitionPathRotate = 18;
		public static final int[] KeyCycle = new int[] { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f03016a, 0x7f03021d, 0x7f030366, 0x7f030368, 0x7f0304f4, 0x7f0304f6, 0x7f03050d, 0x7f03050e, 0x7f03050f, 0x7f030510, 0x7f030511 };
		public static final int KeyCycle_android_alpha = 0;
		public static final int KeyCycle_android_elevation = 9;
		public static final int KeyCycle_android_rotation = 5;
		public static final int KeyCycle_android_rotationX = 6;
		public static final int KeyCycle_android_rotationY = 7;
		public static final int KeyCycle_android_scaleX = 3;
		public static final int KeyCycle_android_scaleY = 4;
		public static final int KeyCycle_android_translationX = 1;
		public static final int KeyCycle_android_translationY = 2;
		public static final int KeyCycle_android_translationZ = 8;
		public static final int KeyCycle_curveFit = 10;
		public static final int KeyCycle_framePosition = 11;
		public static final int KeyCycle_motionProgress = 12;
		public static final int KeyCycle_motionTarget = 13;
		public static final int KeyCycle_transitionEasing = 14;
		public static final int KeyCycle_transitionPathRotate = 15;
		public static final int KeyCycle_waveOffset = 16;
		public static final int KeyCycle_wavePeriod = 17;
		public static final int KeyCycle_waveShape = 19;
		public static final int KeyCycle_waveVariesBy = 20;
		public static final int[] KeyPosition = new int[] { 0x7f03016a, 0x7f030194, 0x7f03021d, 0x7f030280, 0x7f030368, 0x7f030395, 0x7f030397, 0x7f030398, 0x7f030399, 0x7f03039a, 0x7f030402, 0x7f0304f4 };
		public static final int KeyPosition_curveFit = 0;
		public static final int KeyPosition_drawPath = 1;
		public static final int KeyPosition_framePosition = 2;
		public static final int KeyPosition_keyPositionType = 3;
		public static final int KeyPosition_motionTarget = 4;
		public static final int KeyPosition_pathMotionArc = 5;
		public static final int KeyPosition_percentHeight = 6;
		public static final int KeyPosition_percentWidth = 7;
		public static final int KeyPosition_percentX = 8;
		public static final int KeyPosition_percentY = 9;
		public static final int KeyPosition_sizePercent = 10;
		public static final int KeyPosition_transitionEasing = 11;
		public static final int[] KeyTimeCycle = new int[] { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f03016a, 0x7f03021d, 0x7f030366, 0x7f030368, 0x7f0304f4, 0x7f0304f6, 0x7f03050c, 0x7f03050d, 0x7f03050e, 0x7f03050f, 0x7f030510 };
		public static final int KeyTimeCycle_android_alpha = 0;
		public static final int KeyTimeCycle_android_elevation = 9;
		public static final int KeyTimeCycle_android_rotation = 5;
		public static final int KeyTimeCycle_android_rotationX = 6;
		public static final int KeyTimeCycle_android_rotationY = 7;
		public static final int KeyTimeCycle_android_scaleX = 3;
		public static final int KeyTimeCycle_android_scaleY = 4;
		public static final int KeyTimeCycle_android_translationX = 1;
		public static final int KeyTimeCycle_android_translationY = 2;
		public static final int KeyTimeCycle_android_translationZ = 8;
		public static final int KeyTimeCycle_curveFit = 10;
		public static final int KeyTimeCycle_framePosition = 11;
		public static final int KeyTimeCycle_motionProgress = 12;
		public static final int KeyTimeCycle_motionTarget = 13;
		public static final int KeyTimeCycle_transitionEasing = 14;
		public static final int KeyTimeCycle_transitionPathRotate = 15;
		public static final int KeyTimeCycle_waveDecay = 16;
		public static final int KeyTimeCycle_waveOffset = 17;
		public static final int KeyTimeCycle_wavePeriod = 18;
		public static final int KeyTimeCycle_waveShape = 20;
		public static final int[] KeyTrigger = new int[] { 0x7f03021d, 0x7f030368, 0x7f030369, 0x7f03036a, 0x7f03037b, 0x7f03037d, 0x7f03037e, 0x7f0304f8, 0x7f0304f9, 0x7f0304fa, 0x7f030506, 0x7f030507, 0x7f030508 };
		public static final int KeyTrigger_framePosition = 0;
		public static final int KeyTrigger_motionTarget = 1;
		public static final int KeyTrigger_motion_postLayoutCollision = 2;
		public static final int KeyTrigger_motion_triggerOnCollision = 3;
		public static final int KeyTrigger_onCross = 4;
		public static final int KeyTrigger_onNegativeCross = 5;
		public static final int KeyTrigger_onPositiveCross = 6;
		public static final int KeyTrigger_triggerId = 7;
		public static final int KeyTrigger_triggerReceiver = 8;
		public static final int KeyTrigger_triggerSlack = 9;
		public static final int[] Layout = new int[] { 0x010100c4, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x010103b5, 0x010103b6, 0x7f03006a, 0x7f03006b, 0x7f03006c, 0x7f0300b5, 0x7f03013e, 0x7f03013f, 0x7f03022d, 0x7f030294, 0x7f030295, 0x7f030296, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a0, 0x7f0302a1, 0x7f0302a2, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a6, 0x7f0302a7, 0x7f0302a8, 0x7f0302a9, 0x7f0302aa, 0x7f0302ab, 0x7f0302ac, 0x7f0302ad, 0x7f0302ae, 0x7f0302af, 0x7f0302b0, 0x7f0302b1, 0x7f0302b2, 0x7f0302b3, 0x7f0302b4, 0x7f0302b5, 0x7f0302b7, 0x7f0302b8, 0x7f0302b9, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302bd, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1, 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f0302c6, 0x7f0302c7, 0x7f0302c8, 0x7f0302c9, 0x7f0302ca, 0x7f0302cb, 0x7f0302ce, 0x7f0302d3, 0x7f030327, 0x7f03032c, 0x7f030333, 0x7f030337 };
		public static final int Layout_android_layout_height = 2;
		public static final int Layout_android_layout_marginBottom = 6;
		public static final int Layout_android_layout_marginEnd = 8;
		public static final int Layout_android_layout_marginLeft = 3;
		public static final int Layout_android_layout_marginRight = 5;
		public static final int Layout_android_layout_marginStart = 7;
		public static final int Layout_android_layout_marginTop = 4;
		public static final int Layout_android_layout_width = 1;
		public static final int Layout_android_orientation = 0;
		public static final int Layout_barrierAllowsGoneWidgets = 9;
		public static final int Layout_barrierDirection = 10;
		public static final int Layout_barrierMargin = 11;
		public static final int Layout_chainUseRtl = 12;
		public static final int Layout_constraint_referenced_ids = 13;
		public static final int Layout_layout_constrainedHeight = 16;
		public static final int Layout_layout_constrainedWidth = 17;
		public static final int Layout_layout_constraintBaseline_creator = 18;
		public static final int Layout_layout_constraintBaseline_toBaselineOf = 19;
		public static final int Layout_layout_constraintBottom_creator = 22;
		public static final int Layout_layout_constraintBottom_toBottomOf = 23;
		public static final int Layout_layout_constraintBottom_toTopOf = 24;
		public static final int Layout_layout_constraintCircle = 25;
		public static final int Layout_layout_constraintCircleAngle = 26;
		public static final int Layout_layout_constraintCircleRadius = 27;
		public static final int Layout_layout_constraintDimensionRatio = 28;
		public static final int Layout_layout_constraintEnd_toEndOf = 29;
		public static final int Layout_layout_constraintEnd_toStartOf = 30;
		public static final int Layout_layout_constraintGuide_begin = 31;
		public static final int Layout_layout_constraintGuide_end = 32;
		public static final int Layout_layout_constraintGuide_percent = 33;
		public static final int Layout_layout_constraintHeight_default = 35;
		public static final int Layout_layout_constraintHeight_max = 36;
		public static final int Layout_layout_constraintHeight_min = 37;
		public static final int Layout_layout_constraintHeight_percent = 38;
		public static final int Layout_layout_constraintHorizontal_bias = 39;
		public static final int Layout_layout_constraintHorizontal_chainStyle = 40;
		public static final int Layout_layout_constraintHorizontal_weight = 41;
		public static final int Layout_layout_constraintLeft_creator = 42;
		public static final int Layout_layout_constraintLeft_toLeftOf = 43;
		public static final int Layout_layout_constraintLeft_toRightOf = 44;
		public static final int Layout_layout_constraintRight_creator = 45;
		public static final int Layout_layout_constraintRight_toLeftOf = 46;
		public static final int Layout_layout_constraintRight_toRightOf = 47;
		public static final int Layout_layout_constraintStart_toEndOf = 48;
		public static final int Layout_layout_constraintStart_toStartOf = 49;
		public static final int Layout_layout_constraintTop_creator = 50;
		public static final int Layout_layout_constraintTop_toBottomOf = 51;
		public static final int Layout_layout_constraintTop_toTopOf = 52;
		public static final int Layout_layout_constraintVertical_bias = 53;
		public static final int Layout_layout_constraintVertical_chainStyle = 54;
		public static final int Layout_layout_constraintVertical_weight = 55;
		public static final int Layout_layout_constraintWidth_default = 57;
		public static final int Layout_layout_constraintWidth_max = 58;
		public static final int Layout_layout_constraintWidth_min = 59;
		public static final int Layout_layout_constraintWidth_percent = 60;
		public static final int Layout_layout_editor_absoluteX = 61;
		public static final int Layout_layout_editor_absoluteY = 62;
		public static final int Layout_layout_goneMarginBottom = 64;
		public static final int Layout_layout_goneMarginEnd = 65;
		public static final int Layout_layout_goneMarginLeft = 66;
		public static final int Layout_layout_goneMarginRight = 67;
		public static final int Layout_layout_goneMarginStart = 68;
		public static final int Layout_layout_goneMarginTop = 69;
		public static final int Layout_maxHeight = 72;
		public static final int Layout_maxWidth = 73;
		public static final int Layout_minHeight = 74;
		public static final int Layout_minWidth = 75;
		public static final int[] LinearLayoutCompat = new int[] { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f030189, 0x7f03018e, 0x7f03032d, 0x7f0303f2 };
		public static final int LinearLayoutCompat_android_baselineAligned = 2;
		public static final int LinearLayoutCompat_android_baselineAlignedChildIndex = 3;
		public static final int LinearLayoutCompat_android_gravity = 0;
		public static final int LinearLayoutCompat_android_orientation = 1;
		public static final int LinearLayoutCompat_android_weightSum = 4;
		public static final int LinearLayoutCompat_divider = 5;
		public static final int LinearLayoutCompat_dividerPadding = 6;
		public static final int LinearLayoutCompat_measureWithLargestChild = 7;
		public static final int LinearLayoutCompat_showDividers = 8;
		public static final int[] LinearLayoutCompat_Layout = new int[] { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 };
		public static final int LinearLayoutCompat_Layout_android_layout_gravity = 0;
		public static final int LinearLayoutCompat_Layout_android_layout_height = 2;
		public static final int LinearLayoutCompat_Layout_android_layout_weight = 3;
		public static final int LinearLayoutCompat_Layout_android_layout_width = 1;
		public static final int[] LinearProgressIndicator = new int[] { 0x7f030254, 0x7f030258, 0x7f0304ee };
		public static final int LinearProgressIndicator_indeterminateAnimationType = 0;
		public static final int LinearProgressIndicator_indicatorDirectionLinear = 1;
		public static final int[] ListPopupWindow = new int[] { 0x010102ac, 0x010102ad };
		public static final int ListPopupWindow_android_dropDownHorizontalOffset = 0;
		public static final int ListPopupWindow_android_dropDownVerticalOffset = 1;
		public static final int[] MaterialAlertDialog = new int[] { 0x7f03004f, 0x7f030050, 0x7f030051, 0x7f030052, 0x7f030056 };
		public static final int MaterialAlertDialog_backgroundInsetBottom = 0;
		public static final int MaterialAlertDialog_backgroundInsetEnd = 1;
		public static final int MaterialAlertDialog_backgroundInsetStart = 2;
		public static final int MaterialAlertDialog_backgroundInsetTop = 3;
		public static final int MaterialAlertDialog_backgroundTint = 4;
		public static final int[] MaterialAlertDialogTheme = new int[] { 0x7f0302f2, 0x7f0302f3, 0x7f0302f4, 0x7f0302f5, 0x7f0302f6, 0x7f0302f7 };
		public static final int MaterialAlertDialogTheme_materialAlertDialogBodyTextStyle = 0;
		public static final int MaterialAlertDialogTheme_materialAlertDialogButtonSpacerVisibility = 1;
		public static final int MaterialAlertDialogTheme_materialAlertDialogTheme = 2;
		public static final int MaterialAlertDialogTheme_materialAlertDialogTitleIconStyle = 3;
		public static final int MaterialAlertDialogTheme_materialAlertDialogTitlePanelStyle = 4;
		public static final int MaterialAlertDialogTheme_materialAlertDialogTitleTextStyle = 5;
		public static final int[] MaterialAutoCompleteTextView = new int[] { 0x01010220, 0x0101048c, 0x7f0301a1, 0x7f0303fb, 0x7f0303fc, 0x7f0303fd, 0x7f0303fe };
		public static final int MaterialAutoCompleteTextView_android_inputType = 0;
		public static final int MaterialAutoCompleteTextView_android_popupElevation = 1;
		public static final int MaterialAutoCompleteTextView_dropDownBackgroundTint = 2;
		public static final int MaterialAutoCompleteTextView_simpleItemLayout = 3;
		public static final int MaterialAutoCompleteTextView_simpleItemSelectedColor = 4;
		public static final int MaterialAutoCompleteTextView_simpleItemSelectedRippleColor = 5;
		public static final int MaterialAutoCompleteTextView_simpleItems = 6;
		public static final int[] MaterialButton = new int[] { 0x010100d4, 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x010101e5, 0x7f030056, 0x7f030057, 0x7f03015a, 0x7f0301a9, 0x7f030244, 0x7f030246, 0x7f030247, 0x7f030248, 0x7f03024a, 0x7f03024b, 0x7f0303cb, 0x7f0303e2, 0x7f0303ea, 0x7f030431, 0x7f030432, 0x7f0304d7 };
		public static final int MaterialButton_android_background = 0;
		public static final int MaterialButton_android_checkable = 5;
		public static final int MaterialButton_android_insetBottom = 4;
		public static final int MaterialButton_android_insetLeft = 1;
		public static final int MaterialButton_android_insetRight = 2;
		public static final int MaterialButton_android_insetTop = 3;
		public static final int MaterialButton_backgroundTint = 6;
		public static final int MaterialButton_backgroundTintMode = 7;
		public static final int MaterialButton_cornerRadius = 8;
		public static final int MaterialButton_elevation = 9;
		public static final int MaterialButton_icon = 10;
		public static final int MaterialButton_iconGravity = 11;
		public static final int MaterialButton_iconPadding = 12;
		public static final int MaterialButton_iconSize = 13;
		public static final int MaterialButton_iconTint = 14;
		public static final int MaterialButton_iconTintMode = 15;
		public static final int MaterialButton_rippleColor = 16;
		public static final int MaterialButton_shapeAppearance = 17;
		public static final int MaterialButton_shapeAppearanceOverlay = 18;
		public static final int MaterialButton_strokeColor = 19;
		public static final int MaterialButton_strokeWidth = 20;
		public static final int MaterialButton_toggleCheckedStateOnClick = 21;
		public static final int[] MaterialButtonToggleGroup = new int[] { 0x0101000e, 0x7f0300ba, 0x7f0303df, 0x7f030401 };
		public static final int MaterialButtonToggleGroup_android_enabled = 0;
		public static final int MaterialButtonToggleGroup_checkedButton = 1;
		public static final int MaterialButtonToggleGroup_selectionRequired = 2;
		public static final int MaterialButtonToggleGroup_singleSelection = 3;
		public static final int[] MaterialCalendar = new int[] { 0x0101020d, 0x7f030056, 0x7f030177, 0x7f030178, 0x7f030179, 0x7f03017a, 0x7f030376, 0x7f0303bb, 0x7f03051c, 0x7f03051d, 0x7f03051e };
		public static final int MaterialCalendar_android_windowFullscreen = 0;
		public static final int MaterialCalendar_backgroundTint = 1;
		public static final int MaterialCalendar_dayInvalidStyle = 2;
		public static final int MaterialCalendar_daySelectedStyle = 3;
		public static final int MaterialCalendar_dayStyle = 4;
		public static final int MaterialCalendar_dayTodayStyle = 5;
		public static final int MaterialCalendar_nestedScrollable = 6;
		public static final int MaterialCalendar_rangeFillColor = 7;
		public static final int MaterialCalendar_yearSelectedStyle = 8;
		public static final int MaterialCalendar_yearStyle = 9;
		public static final int MaterialCalendar_yearTodayStyle = 10;
		public static final int[] MaterialCalendarItem = new int[] { 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x7f030264, 0x7f030270, 0x7f030271, 0x7f030278, 0x7f030279, 0x7f03027e };
		public static final int MaterialCalendarItem_android_insetBottom = 3;
		public static final int MaterialCalendarItem_android_insetLeft = 0;
		public static final int MaterialCalendarItem_android_insetRight = 1;
		public static final int MaterialCalendarItem_android_insetTop = 2;
		public static final int MaterialCalendarItem_itemFillColor = 4;
		public static final int MaterialCalendarItem_itemShapeAppearance = 5;
		public static final int MaterialCalendarItem_itemShapeAppearanceOverlay = 6;
		public static final int MaterialCalendarItem_itemStrokeColor = 7;
		public static final int MaterialCalendarItem_itemStrokeWidth = 8;
		public static final int MaterialCalendarItem_itemTextColor = 9;
		public static final int[] MaterialCardView = new int[] { 0x010101e5, 0x7f0300a4, 0x7f0300bc, 0x7f0300be, 0x7f0300bf, 0x7f0300c0, 0x7f0300c1, 0x7f0303cb, 0x7f0303e2, 0x7f0303ea, 0x7f030427, 0x7f030431, 0x7f030432 };
		public static final int MaterialCardView_android_checkable = 0;
		public static final int MaterialCardView_cardForegroundColor = 1;
		public static final int MaterialCardView_checkedIcon = 2;
		public static final int MaterialCardView_checkedIconGravity = 3;
		public static final int MaterialCardView_checkedIconMargin = 4;
		public static final int MaterialCardView_checkedIconSize = 5;
		public static final int MaterialCardView_checkedIconTint = 6;
		public static final int MaterialCardView_rippleColor = 7;
		public static final int MaterialCardView_shapeAppearance = 8;
		public static final int MaterialCardView_shapeAppearanceOverlay = 9;
		public static final int MaterialCardView_state_dragged = 10;
		public static final int MaterialCardView_strokeColor = 11;
		public static final int MaterialCardView_strokeWidth = 12;
		public static final int[] MaterialCheckBox = new int[] { 0x01010107, 0x7f030096, 0x7f030098, 0x7f03009a, 0x7f03009b, 0x7f03009f, 0x7f0300b4, 0x7f0300c3, 0x7f0301bb, 0x7f0301c2, 0x7f030500 };
		public static final int MaterialCheckBox_android_button = 0;
		public static final int MaterialCheckBox_buttonCompat = 1;
		public static final int MaterialCheckBox_buttonIcon = 2;
		public static final int MaterialCheckBox_buttonIconTint = 3;
		public static final int MaterialCheckBox_buttonIconTintMode = 4;
		public static final int MaterialCheckBox_buttonTint = 5;
		public static final int MaterialCheckBox_centerIfNoTextEnabled = 6;
		public static final int MaterialCheckBox_checkedState = 7;
		public static final int MaterialCheckBox_errorAccessibilityLabel = 8;
		public static final int MaterialCheckBox_errorShown = 9;
		public static final int MaterialCheckBox_useMaterialThemeColors = 10;
		public static final int[] MaterialCheckBoxStates = new int[] { 0x7f030428, 0x7f030429 };
		public static final int MaterialCheckBoxStates_state_error = 0;
		public static final int MaterialCheckBoxStates_state_indeterminate = 1;
		public static final int[] MaterialDivider = new int[] { 0x7f03018a, 0x7f03018c, 0x7f03018d, 0x7f03018f, 0x7f030289 };
		public static final int MaterialDivider_dividerColor = 0;
		public static final int MaterialDivider_dividerInsetEnd = 1;
		public static final int MaterialDivider_dividerInsetStart = 2;
		public static final int MaterialDivider_dividerThickness = 3;
		public static final int MaterialDivider_lastItemDecorated = 4;
		public static final int[] MaterialRadioButton = new int[] { 0x7f03009f, 0x7f030500 };
		public static final int MaterialRadioButton_buttonTint = 0;
		public static final int MaterialRadioButton_useMaterialThemeColors = 1;
		public static final int[] MaterialShape = new int[] { 0x7f0303e2, 0x7f0303ea };
		public static final int MaterialShape_shapeAppearance = 0;
		public static final int MaterialShape_shapeAppearanceOverlay = 1;
		public static final int[] MaterialSwitch = new int[] { 0x7f0304b0, 0x7f0304b1, 0x7f0304b2, 0x7f0304b3, 0x7f0304e9, 0x7f0304ea, 0x7f0304eb };
		public static final int MaterialSwitch_thumbIcon = 0;
		public static final int MaterialSwitch_thumbIconSize = 1;
		public static final int MaterialSwitch_thumbIconTint = 2;
		public static final int MaterialSwitch_thumbIconTintMode = 3;
		public static final int MaterialSwitch_trackDecoration = 4;
		public static final int MaterialSwitch_trackDecorationTint = 5;
		public static final int MaterialSwitch_trackDecorationTintMode = 6;
		public static final int[] MaterialTextAppearance = new int[] { 0x010104b6, 0x0101057f, 0x7f0302d8 };
		public static final int MaterialTextAppearance_android_letterSpacing = 0;
		public static final int MaterialTextAppearance_android_lineHeight = 1;
		public static final int MaterialTextAppearance_lineHeight = 2;
		public static final int[] MaterialTextView = new int[] { 0x01010034, 0x0101057f, 0x7f0302d8 };
		public static final int MaterialTextView_android_lineHeight = 1;
		public static final int MaterialTextView_android_textAppearance = 0;
		public static final int MaterialTextView_lineHeight = 2;
		public static final int[] MaterialTimePicker = new int[] { 0x7f030056, 0x7f0300e5, 0x7f030281 };
		public static final int MaterialTimePicker_backgroundTint = 0;
		public static final int MaterialTimePicker_clockIcon = 1;
		public static final int MaterialTimePicker_keyboardIcon = 2;
		public static final int[] MaterialToolbar = new int[] { 0x7f0302eb, 0x7f0302ed, 0x7f030370, 0x7f03043a, 0x7f0304c9 };
		public static final int MaterialToolbar_logoAdjustViewBounds = 0;
		public static final int MaterialToolbar_logoScaleType = 1;
		public static final int MaterialToolbar_navigationIconTint = 2;
		public static final int MaterialToolbar_subtitleCentered = 3;
		public static final int MaterialToolbar_titleCentered = 4;
		public static final int[] MenuGroup = new int[] { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 };
		public static final int MenuGroup_android_checkableBehavior = 5;
		public static final int MenuGroup_android_enabled = 0;
		public static final int MenuGroup_android_id = 1;
		public static final int MenuGroup_android_menuCategory = 3;
		public static final int MenuGroup_android_orderInCategory = 4;
		public static final int MenuGroup_android_visible = 2;
		public static final int[] MenuItem = new int[] { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f030010, 0x7f030024, 0x7f030026, 0x7f030032, 0x7f030142, 0x7f03024a, 0x7f03024b, 0x7f030379, 0x7f0303f0, 0x7f0304df };
		public static final int MenuItem_actionLayout = 13;
		public static final int MenuItem_actionProviderClass = 14;
		public static final int MenuItem_actionViewClass = 15;
		public static final int MenuItem_alphabeticModifiers = 16;
		public static final int MenuItem_android_alphabeticShortcut = 9;
		public static final int MenuItem_android_checkable = 11;
		public static final int MenuItem_android_checked = 3;
		public static final int MenuItem_android_enabled = 1;
		public static final int MenuItem_android_icon = 0;
		public static final int MenuItem_android_id = 2;
		public static final int MenuItem_android_menuCategory = 5;
		public static final int MenuItem_android_numericShortcut = 10;
		public static final int MenuItem_android_onClick = 12;
		public static final int MenuItem_android_orderInCategory = 6;
		public static final int MenuItem_android_title = 7;
		public static final int MenuItem_android_titleCondensed = 8;
		public static final int MenuItem_android_visible = 4;
		public static final int MenuItem_contentDescription = 17;
		public static final int MenuItem_iconTint = 18;
		public static final int MenuItem_iconTintMode = 19;
		public static final int MenuItem_numericModifiers = 20;
		public static final int MenuItem_showAsAction = 21;
		public static final int MenuItem_tooltipText = 22;
		public static final int[] MenuView = new int[] { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f0303af, 0x7f030433 };
		public static final int MenuView_android_headerBackground = 4;
		public static final int MenuView_android_horizontalDivider = 2;
		public static final int MenuView_android_itemBackground = 5;
		public static final int MenuView_android_itemIconDisabledAlpha = 6;
		public static final int MenuView_android_itemTextAppearance = 1;
		public static final int MenuView_android_verticalDivider = 3;
		public static final int MenuView_android_windowAnimationStyle = 0;
		public static final int MenuView_preserveIconSpacing = 7;
		public static final int MenuView_subMenuArrow = 8;
		public static final int[] MockView = new int[] { 0x7f030338, 0x7f030339, 0x7f03033a, 0x7f03033b, 0x7f03033c, 0x7f03033d };
		public static final int MockView_mock_diagonalsColor = 0;
		public static final int MockView_mock_label = 1;
		public static final int MockView_mock_labelBackgroundColor = 2;
		public static final int MockView_mock_labelColor = 3;
		public static final int MockView_mock_showDiagonals = 4;
		public static final int MockView_mock_showLabel = 5;
		public static final int[] Motion = new int[] { 0x7f030035, 0x7f030038, 0x7f030194, 0x7f030365, 0x7f030367, 0x7f030395, 0x7f0303b4, 0x7f0303b5, 0x7f0303b6, 0x7f0304f4 };
		public static final int Motion_drawPath = 2;
		public static final int Motion_motionPathRotate = 3;
		public static final int Motion_motionStagger = 4;
		public static final int Motion_pathMotionArc = 5;
		public static final int Motion_transitionEasing = 9;
		public static final int[] MotionHelper = new int[] { 0x7f03037c, 0x7f03037f };
		public static final int MotionHelper_onHide = 0;
		public static final int MotionHelper_onShow = 1;
		public static final int[] MotionLayout = new int[] { 0x7f03003c, 0x7f030167, 0x7f03028c, 0x7f03033e, 0x7f030366, 0x7f0303f5 };
		public static final int MotionLayout_applyMotionScene = 0;
		public static final int MotionLayout_currentState = 1;
		public static final int MotionLayout_layoutDescription = 2;
		public static final int MotionLayout_motionDebug = 3;
		public static final int MotionLayout_motionProgress = 4;
		public static final int MotionLayout_showPaths = 5;
		public static final int[] MotionScene = new int[] { 0x7f03017b, 0x7f03028d };
		public static final int MotionScene_defaultDuration = 0;
		public static final int MotionScene_layoutDuringTransition = 1;
		public static final int[] MotionTelltales = new int[] { 0x7f030467, 0x7f030468, 0x7f030469 };
		public static final int MotionTelltales_telltales_tailColor = 0;
		public static final int MotionTelltales_telltales_tailScale = 1;
		public static final int MotionTelltales_telltales_velocityMode = 2;
		public static final int[] NavigationBarActiveIndicator = new int[] { 0x01010155, 0x01010159, 0x010101a5, 0x7f0302ee, 0x7f0303e2 };
		public static final int NavigationBarActiveIndicator_android_color = 2;
		public static final int NavigationBarActiveIndicator_android_height = 0;
		public static final int NavigationBarActiveIndicator_android_width = 1;
		public static final int NavigationBarActiveIndicator_marginHorizontal = 3;
		public static final int NavigationBarActiveIndicator_shapeAppearance = 4;
		public static final int[] NavigationBarView = new int[] { 0x7f030027, 0x7f030056, 0x7f0301a9, 0x7f030262, 0x7f030263, 0x7f030268, 0x7f030269, 0x7f03026d, 0x7f03026e, 0x7f03026f, 0x7f03027b, 0x7f03027c, 0x7f03027d, 0x7f03027e, 0x7f030286, 0x7f03032e };
		public static final int NavigationBarView_activeIndicatorLabelPadding = 0;
		public static final int NavigationBarView_backgroundTint = 1;
		public static final int NavigationBarView_elevation = 2;
		public static final int NavigationBarView_itemActiveIndicatorStyle = 3;
		public static final int NavigationBarView_itemBackground = 4;
		public static final int NavigationBarView_itemIconSize = 5;
		public static final int NavigationBarView_itemIconTint = 6;
		public static final int NavigationBarView_itemPaddingBottom = 7;
		public static final int NavigationBarView_itemPaddingTop = 8;
		public static final int NavigationBarView_itemRippleColor = 9;
		public static final int NavigationBarView_itemTextAppearanceActive = 10;
		public static final int NavigationBarView_itemTextAppearanceActiveBoldEnabled = 11;
		public static final int NavigationBarView_itemTextAppearanceInactive = 12;
		public static final int NavigationBarView_itemTextColor = 13;
		public static final int NavigationBarView_labelVisibilityMode = 14;
		public static final int NavigationBarView_menu = 15;
		public static final int[] NavigationRailView = new int[] { 0x7f030230, 0x7f03026b, 0x7f030330, 0x7f030385, 0x7f03038a, 0x7f03038c, 0x7f0303e2, 0x7f0303ea };
		public static final int NavigationRailView_headerLayout = 0;
		public static final int NavigationRailView_itemMinHeight = 1;
		public static final int NavigationRailView_menuGravity = 2;
		public static final int NavigationRailView_paddingBottomSystemWindowInsets = 3;
		public static final int NavigationRailView_paddingStartSystemWindowInsets = 4;
		public static final int NavigationRailView_paddingTopSystemWindowInsets = 5;
		public static final int NavigationRailView_shapeAppearance = 6;
		public static final int NavigationRailView_shapeAppearanceOverlay = 7;
		public static final int[] NavigationView = new int[] { 0x010100b3, 0x010100d4, 0x010100dd, 0x0101011f, 0x7f03007f, 0x7f03018c, 0x7f03018d, 0x7f03019f, 0x7f0301a9, 0x7f030230, 0x7f030263, 0x7f030265, 0x7f030267, 0x7f030268, 0x7f030269, 0x7f03026a, 0x7f03026f, 0x7f030270, 0x7f030271, 0x7f030272, 0x7f030273, 0x7f030274, 0x7f030275, 0x7f030276, 0x7f03027a, 0x7f03027c, 0x7f03027e, 0x7f03027f, 0x7f03032e, 0x7f0303e2, 0x7f0303ea, 0x7f030434, 0x7f030435, 0x7f030436, 0x7f030437, 0x7f0304e0 };
		public static final int NavigationView_android_background = 1;
		public static final int NavigationView_android_fitsSystemWindows = 2;
		public static final int NavigationView_android_layout_gravity = 0;
		public static final int NavigationView_android_maxWidth = 3;
		public static final int NavigationView_bottomInsetScrimEnabled = 4;
		public static final int NavigationView_dividerInsetEnd = 5;
		public static final int NavigationView_dividerInsetStart = 6;
		public static final int NavigationView_drawerLayoutCornerSize = 7;
		public static final int NavigationView_elevation = 8;
		public static final int NavigationView_headerLayout = 9;
		public static final int NavigationView_itemBackground = 10;
		public static final int NavigationView_itemHorizontalPadding = 11;
		public static final int NavigationView_itemIconPadding = 12;
		public static final int NavigationView_itemIconSize = 13;
		public static final int NavigationView_itemIconTint = 14;
		public static final int NavigationView_itemMaxLines = 15;
		public static final int NavigationView_itemRippleColor = 16;
		public static final int NavigationView_itemShapeAppearance = 17;
		public static final int NavigationView_itemShapeAppearanceOverlay = 18;
		public static final int NavigationView_itemShapeFillColor = 19;
		public static final int NavigationView_itemShapeInsetBottom = 20;
		public static final int NavigationView_itemShapeInsetEnd = 21;
		public static final int NavigationView_itemShapeInsetStart = 22;
		public static final int NavigationView_itemShapeInsetTop = 23;
		public static final int NavigationView_itemTextAppearance = 24;
		public static final int NavigationView_itemTextAppearanceActiveBoldEnabled = 25;
		public static final int NavigationView_itemTextColor = 26;
		public static final int NavigationView_itemVerticalPadding = 27;
		public static final int NavigationView_menu = 28;
		public static final int NavigationView_shapeAppearance = 29;
		public static final int NavigationView_shapeAppearanceOverlay = 30;
		public static final int NavigationView_subheaderColor = 31;
		public static final int NavigationView_subheaderInsetEnd = 32;
		public static final int NavigationView_subheaderInsetStart = 33;
		public static final int NavigationView_subheaderTextAppearance = 34;
		public static final int NavigationView_topInsetScrimEnabled = 35;
		public static final int[] OnClick = new int[] { 0x7f0300e2, 0x7f030465 };
		public static final int OnClick_clickAction = 0;
		public static final int OnClick_targetId = 1;
		public static final int[] OnSwipe = new int[] { 0x7f030043, 0x7f030191, 0x7f030192, 0x7f030193, 0x7f0302d7, 0x7f030323, 0x7f03032b, 0x7f03036b, 0x7f030374, 0x7f030381, 0x7f0303cc, 0x7f030413, 0x7f030414, 0x7f030415, 0x7f030416, 0x7f030417, 0x7f0304e1, 0x7f0304e2, 0x7f0304e3 };
		public static final int OnSwipe_dragDirection = 1;
		public static final int OnSwipe_dragScale = 2;
		public static final int OnSwipe_dragThreshold = 3;
		public static final int OnSwipe_limitBoundsTo = 4;
		public static final int OnSwipe_maxAcceleration = 5;
		public static final int OnSwipe_maxVelocity = 6;
		public static final int OnSwipe_moveWhenScrollAtTop = 7;
		public static final int OnSwipe_nestedScrollFlags = 8;
		public static final int OnSwipe_onTouchUp = 9;
		public static final int OnSwipe_touchAnchorId = 16;
		public static final int OnSwipe_touchAnchorSide = 17;
		public static final int OnSwipe_touchRegionId = 18;
		public static final int[] PopupWindow = new int[] { 0x01010176, 0x010102c9, 0x7f030382 };
		public static final int PopupWindow_android_popupAnimationStyle = 1;
		public static final int PopupWindow_android_popupBackground = 0;
		public static final int PopupWindow_overlapAnchor = 2;
		public static final int[] PopupWindowBackgroundState = new int[] { 0x7f030424 };
		public static final int PopupWindowBackgroundState_state_above_anchor = 0;
		public static final int[] PropertySet = new int[] { 0x010100dc, 0x0101031f, 0x7f0302b6, 0x7f030366, 0x7f030509 };
		public static final int PropertySet_android_alpha = 1;
		public static final int PropertySet_android_visibility = 0;
		public static final int PropertySet_layout_constraintTag = 2;
		public static final int PropertySet_motionProgress = 3;
		public static final int PropertySet_visibilityMode = 4;
		public static final int[] RadialViewGroup = new int[] { 0x7f03030e };
		public static final int RadialViewGroup_materialCircleRadius = 0;
		public static final int[] RangeSlider = new int[] { 0x7f030335, 0x7f030501 };
		public static final int RangeSlider_minSeparation = 0;
		public static final int RangeSlider_values = 1;
		public static final int[] RecycleListView = new int[] { 0x7f030384, 0x7f03038b };
		public static final int RecycleListView_paddingBottomNoButtons = 0;
		public static final int RecycleListView_paddingTopNoTitle = 1;
		public static final int[] RecyclerView = new int[] { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f0301e2, 0x7f0301e3, 0x7f0301e4, 0x7f0301e5, 0x7f0301e6, 0x7f03028e, 0x7f0303ca, 0x7f030407, 0x7f030419 };
		public static final int RecyclerView_android_clipToPadding = 1;
		public static final int RecyclerView_android_descendantFocusability = 2;
		public static final int RecyclerView_android_orientation = 0;
		public static final int RecyclerView_fastScrollEnabled = 3;
		public static final int RecyclerView_fastScrollHorizontalThumbDrawable = 4;
		public static final int RecyclerView_fastScrollHorizontalTrackDrawable = 5;
		public static final int RecyclerView_fastScrollVerticalThumbDrawable = 6;
		public static final int RecyclerView_fastScrollVerticalTrackDrawable = 7;
		public static final int RecyclerView_layoutManager = 8;
		public static final int RecyclerView_reverseLayout = 9;
		public static final int RecyclerView_spanCount = 10;
		public static final int RecyclerView_stackFromEnd = 11;
		public static final int[] ScrimInsetsFrameLayout = new int[] { 0x7f03025d };
		public static final int ScrimInsetsFrameLayout_insetForeground = 0;
		public static final int[] ScrollingViewBehavior_Layout = new int[] { 0x7f030074 };
		public static final int ScrollingViewBehavior_Layout_behavior_overlapTop = 0;
		public static final int[] SearchBar = new int[] { 0x01010034, 0x0101014f, 0x01010150, 0x7f030056, 0x7f03017c, 0x7f03017f, 0x7f0301a9, 0x7f03021b, 0x7f030238, 0x7f030370, 0x7f030431, 0x7f030432, 0x7f0304c7 };
		public static final int SearchBar_android_hint = 2;
		public static final int SearchBar_android_text = 1;
		public static final int SearchBar_android_textAppearance = 0;
		public static final int SearchBar_backgroundTint = 3;
		public static final int SearchBar_defaultMarginsEnabled = 4;
		public static final int SearchBar_defaultScrollFlagsEnabled = 5;
		public static final int SearchBar_elevation = 6;
		public static final int SearchBar_forceDefaultNavigationOnClickListener = 7;
		public static final int SearchBar_hideNavigationIcon = 8;
		public static final int SearchBar_navigationIconTint = 9;
		public static final int SearchBar_strokeColor = 10;
		public static final int SearchBar_strokeWidth = 11;
		public static final int SearchBar_tintNavigationIcon = 12;
		public static final int[] SearchView = new int[] { 0x01010034, 0x010100da, 0x0101011f, 0x0101014f, 0x01010150, 0x01010220, 0x01010264, 0x7f030036, 0x7f030037, 0x7f030045, 0x7f03004c, 0x7f030056, 0x7f0300e7, 0x7f030138, 0x7f03017e, 0x7f030220, 0x7f030230, 0x7f030238, 0x7f03024c, 0x7f03028b, 0x7f0303b7, 0x7f0303b8, 0x7f0303d6, 0x7f0303d7, 0x7f0303d8, 0x7f030438, 0x7f030441, 0x7f0304ff, 0x7f03050a };
		public static final int SearchView_android_focusable = 1;
		public static final int SearchView_android_hint = 4;
		public static final int SearchView_android_imeOptions = 6;
		public static final int SearchView_android_inputType = 5;
		public static final int SearchView_android_maxWidth = 2;
		public static final int SearchView_android_text = 3;
		public static final int SearchView_android_textAppearance = 0;
		public static final int SearchView_animateMenuItems = 7;
		public static final int SearchView_animateNavigationIcon = 8;
		public static final int SearchView_autoShowKeyboard = 9;
		public static final int SearchView_backHandlingEnabled = 10;
		public static final int SearchView_backgroundTint = 11;
		public static final int SearchView_closeIcon = 12;
		public static final int SearchView_commitIcon = 13;
		public static final int SearchView_defaultQueryHint = 14;
		public static final int SearchView_goIcon = 15;
		public static final int SearchView_headerLayout = 16;
		public static final int SearchView_hideNavigationIcon = 17;
		public static final int SearchView_iconifiedByDefault = 18;
		public static final int SearchView_layout = 19;
		public static final int SearchView_queryBackground = 20;
		public static final int SearchView_queryHint = 21;
		public static final int SearchView_searchHintIcon = 22;
		public static final int SearchView_searchIcon = 23;
		public static final int SearchView_searchPrefixText = 24;
		public static final int SearchView_submitBackground = 25;
		public static final int SearchView_suggestionRowLayout = 26;
		public static final int SearchView_useDrawerArrowDrawable = 27;
		public static final int SearchView_voiceIcon = 28;
		public static final int[] ShapeAppearance = new int[] { 0x7f030155, 0x7f030156, 0x7f030157, 0x7f030158, 0x7f030159, 0x7f03015b, 0x7f03015c, 0x7f03015d, 0x7f03015e, 0x7f03015f };
		public static final int ShapeAppearance_cornerFamily = 0;
		public static final int ShapeAppearance_cornerFamilyBottomLeft = 1;
		public static final int ShapeAppearance_cornerFamilyBottomRight = 2;
		public static final int ShapeAppearance_cornerFamilyTopLeft = 3;
		public static final int ShapeAppearance_cornerFamilyTopRight = 4;
		public static final int ShapeAppearance_cornerSize = 5;
		public static final int ShapeAppearance_cornerSizeBottomLeft = 6;
		public static final int ShapeAppearance_cornerSizeBottomRight = 7;
		public static final int ShapeAppearance_cornerSizeTopLeft = 8;
		public static final int ShapeAppearance_cornerSizeTopRight = 9;
		public static final int[] ShapeableImageView = new int[] { 0x7f030149, 0x7f03014a, 0x7f03014b, 0x7f03014c, 0x7f03014d, 0x7f03014e, 0x7f03014f, 0x7f0303e2, 0x7f0303ea, 0x7f030431, 0x7f030432 };
		public static final int ShapeableImageView_contentPadding = 0;
		public static final int ShapeableImageView_contentPaddingBottom = 1;
		public static final int ShapeableImageView_contentPaddingEnd = 2;
		public static final int ShapeableImageView_contentPaddingLeft = 3;
		public static final int ShapeableImageView_contentPaddingRight = 4;
		public static final int ShapeableImageView_contentPaddingStart = 5;
		public static final int ShapeableImageView_contentPaddingTop = 6;
		public static final int ShapeableImageView_shapeAppearance = 7;
		public static final int ShapeableImageView_shapeAppearanceOverlay = 8;
		public static final int ShapeableImageView_strokeColor = 9;
		public static final int ShapeableImageView_strokeWidth = 10;
		public static final int[] SideSheetBehavior_Layout = new int[] { 0x0101011f, 0x01010120, 0x01010440, 0x7f030056, 0x7f03006f, 0x7f030154, 0x7f0303e2, 0x7f0303ea };
		public static final int SideSheetBehavior_Layout_android_elevation = 2;
		public static final int SideSheetBehavior_Layout_android_maxHeight = 1;
		public static final int SideSheetBehavior_Layout_android_maxWidth = 0;
		public static final int SideSheetBehavior_Layout_backgroundTint = 3;
		public static final int SideSheetBehavior_Layout_behavior_draggable = 4;
		public static final int SideSheetBehavior_Layout_coplanarSiblingViewId = 5;
		public static final int SideSheetBehavior_Layout_shapeAppearance = 6;
		public static final int SideSheetBehavior_Layout_shapeAppearanceOverlay = 7;
		public static final int[] Slider = new int[] { 0x0101000e, 0x01010024, 0x01010146, 0x010102de, 0x010102df, 0x7f03022e, 0x7f03022f, 0x7f030284, 0x7f030285, 0x7f030336, 0x7f0304ad, 0x7f0304ae, 0x7f0304af, 0x7f0304b4, 0x7f0304b5, 0x7f0304b6, 0x7f0304ba, 0x7f0304bb, 0x7f0304bc, 0x7f0304bd, 0x7f0304be, 0x7f0304c2, 0x7f0304c3, 0x7f0304c4, 0x7f0304e5, 0x7f0304e6, 0x7f0304e7, 0x7f0304ec, 0x7f0304ed, 0x7f0304ee };
		public static final int Slider_android_enabled = 0;
		public static final int Slider_android_stepSize = 2;
		public static final int Slider_android_value = 1;
		public static final int Slider_android_valueFrom = 3;
		public static final int Slider_android_valueTo = 4;
		public static final int Slider_haloColor = 5;
		public static final int Slider_haloRadius = 6;
		public static final int Slider_labelBehavior = 7;
		public static final int Slider_labelStyle = 8;
		public static final int Slider_minTouchTargetSize = 9;
		public static final int Slider_thumbColor = 10;
		public static final int Slider_thumbElevation = 11;
		public static final int Slider_thumbRadius = 13;
		public static final int Slider_thumbStrokeColor = 14;
		public static final int Slider_thumbStrokeWidth = 15;
		public static final int Slider_tickColor = 18;
		public static final int Slider_tickColorActive = 19;
		public static final int Slider_tickColorInactive = 20;
		public static final int Slider_tickRadiusActive = 21;
		public static final int Slider_tickRadiusInactive = 22;
		public static final int Slider_tickVisible = 23;
		public static final int Slider_trackColor = 24;
		public static final int Slider_trackColorActive = 25;
		public static final int Slider_trackColorInactive = 26;
		public static final int Slider_trackHeight = 27;
		public static final int[] Snackbar = new int[] { 0x7f030404, 0x7f030405, 0x7f030406 };
		public static final int Snackbar_snackbarButtonStyle = 0;
		public static final int Snackbar_snackbarStyle = 1;
		public static final int Snackbar_snackbarTextViewStyle = 2;
		public static final int[] SnackbarLayout = new int[] { 0x0101011f, 0x7f030025, 0x7f03003a, 0x7f030053, 0x7f030056, 0x7f030057, 0x7f0301a9, 0x7f030324, 0x7f0303e2, 0x7f0303ea };
		public static final int SnackbarLayout_actionTextColorAlpha = 1;
		public static final int SnackbarLayout_android_maxWidth = 0;
		public static final int SnackbarLayout_animationMode = 2;
		public static final int SnackbarLayout_backgroundOverlayColorAlpha = 3;
		public static final int SnackbarLayout_backgroundTint = 4;
		public static final int SnackbarLayout_backgroundTintMode = 5;
		public static final int SnackbarLayout_elevation = 6;
		public static final int SnackbarLayout_maxActionInlineWidth = 7;
		public static final int SnackbarLayout_shapeAppearance = 8;
		public static final int SnackbarLayout_shapeAppearanceOverlay = 9;
		public static final int[] Spinner = new int[] { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f0303aa };
		public static final int Spinner_android_dropDownWidth = 3;
		public static final int Spinner_android_entries = 0;
		public static final int Spinner_android_popupBackground = 1;
		public static final int Spinner_android_prompt = 2;
		public static final int Spinner_popupTheme = 4;
		public static final int[] SplitPairFilter = new int[] { 0x7f0303b1, 0x7f0303da, 0x7f0303db };
		public static final int SplitPairFilter_primaryActivityName = 0;
		public static final int SplitPairFilter_secondaryActivityAction = 1;
		public static final int SplitPairFilter_secondaryActivityName = 2;
		public static final int[] SplitPairRule = new int[] { 0x7f030039, 0x7f0300e0, 0x7f0301e8, 0x7f0301e9, 0x7f03040b, 0x7f03040c, 0x7f03040d, 0x7f03040e, 0x7f03040f, 0x7f030410, 0x7f030411, 0x7f030464 };
		public static final int SplitPairRule_animationBackgroundColor = 0;
		public static final int SplitPairRule_clearTop = 1;
		public static final int SplitPairRule_finishPrimaryWithSecondary = 2;
		public static final int SplitPairRule_finishSecondaryWithPrimary = 3;
		public static final int SplitPairRule_splitLayoutDirection = 4;
		public static final int SplitPairRule_splitMaxAspectRatioInLandscape = 5;
		public static final int SplitPairRule_splitMaxAspectRatioInPortrait = 6;
		public static final int SplitPairRule_splitMinHeightDp = 7;
		public static final int SplitPairRule_splitMinSmallestWidthDp = 8;
		public static final int SplitPairRule_splitMinWidthDp = 9;
		public static final int SplitPairRule_splitRatio = 10;
		public static final int SplitPairRule_tag = 11;
		public static final int[] SplitPlaceholderRule = new int[] { 0x7f030039, 0x7f0301e7, 0x7f03039d, 0x7f03040b, 0x7f03040c, 0x7f03040d, 0x7f03040e, 0x7f03040f, 0x7f030410, 0x7f030411, 0x7f030430, 0x7f030464 };
		public static final int SplitPlaceholderRule_animationBackgroundColor = 0;
		public static final int SplitPlaceholderRule_finishPrimaryWithPlaceholder = 1;
		public static final int SplitPlaceholderRule_placeholderActivityName = 2;
		public static final int SplitPlaceholderRule_splitLayoutDirection = 3;
		public static final int SplitPlaceholderRule_splitMaxAspectRatioInLandscape = 4;
		public static final int SplitPlaceholderRule_splitMaxAspectRatioInPortrait = 5;
		public static final int SplitPlaceholderRule_splitMinHeightDp = 6;
		public static final int SplitPlaceholderRule_splitMinSmallestWidthDp = 7;
		public static final int SplitPlaceholderRule_splitMinWidthDp = 8;
		public static final int SplitPlaceholderRule_splitRatio = 9;
		public static final int SplitPlaceholderRule_stickyPlaceholder = 10;
		public static final int SplitPlaceholderRule_tag = 11;
		public static final int[] State = new int[] { 0x010100d0, 0x7f030140 };
		public static final int State_android_id = 0;
		public static final int State_constraints = 1;
		public static final int[] StateListDrawable = new int[] { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d };
		public static final int StateListDrawable_android_constantSize = 3;
		public static final int StateListDrawable_android_dither = 0;
		public static final int StateListDrawable_android_enterFadeDuration = 4;
		public static final int StateListDrawable_android_exitFadeDuration = 5;
		public static final int StateListDrawable_android_variablePadding = 2;
		public static final int StateListDrawable_android_visible = 1;
		public static final int[] StateListDrawableItem = new int[] { 0x01010199 };
		public static final int StateListDrawableItem_android_drawable = 0;
		public static final int[] StateSet = new int[] { 0x7f030180 };
		public static final int StateSet_defaultState = 0;
		public static final int[] SwitchCompat = new int[] { 0x01010124, 0x01010125, 0x01010142, 0x7f0303f6, 0x7f030412, 0x7f030443, 0x7f030444, 0x7f030446, 0x7f0304b7, 0x7f0304b8, 0x7f0304b9, 0x7f0304e4, 0x7f0304f0, 0x7f0304f1 };
		public static final int SwitchCompat_android_textOff = 1;
		public static final int SwitchCompat_android_textOn = 0;
		public static final int SwitchCompat_android_thumb = 2;
		public static final int SwitchCompat_showText = 3;
		public static final int SwitchCompat_splitTrack = 4;
		public static final int SwitchCompat_switchMinWidth = 5;
		public static final int SwitchCompat_switchPadding = 6;
		public static final int SwitchCompat_switchTextAppearance = 7;
		public static final int SwitchCompat_thumbTextPadding = 8;
		public static final int SwitchCompat_thumbTint = 9;
		public static final int SwitchCompat_thumbTintMode = 10;
		public static final int SwitchCompat_track = 11;
		public static final int SwitchCompat_trackTint = 12;
		public static final int SwitchCompat_trackTintMode = 13;
		public static final int[] SwitchMaterial = new int[] { 0x7f030500 };
		public static final int SwitchMaterial_useMaterialThemeColors = 0;
		public static final int[] TabItem = new int[] { 0x01010002, 0x010100f2, 0x0101014f };
		public static final int TabItem_android_icon = 0;
		public static final int TabItem_android_layout = 1;
		public static final int TabItem_android_text = 2;
		public static final int[] TabLayout = new int[] { 0x7f030447, 0x7f030448, 0x7f030449, 0x7f03044a, 0x7f03044b, 0x7f03044c, 0x7f03044d, 0x7f03044e, 0x7f03044f, 0x7f030450, 0x7f030451, 0x7f030452, 0x7f030453, 0x7f030454, 0x7f030455, 0x7f030456, 0x7f030457, 0x7f030458, 0x7f030459, 0x7f03045a, 0x7f03045b, 0x7f03045c, 0x7f03045e, 0x7f03045f, 0x7f030461, 0x7f030462, 0x7f030463 };
		public static final int TabLayout_tabBackground = 0;
		public static final int TabLayout_tabContentStart = 1;
		public static final int TabLayout_tabGravity = 2;
		public static final int TabLayout_tabIconTint = 3;
		public static final int TabLayout_tabIconTintMode = 4;
		public static final int TabLayout_tabIndicator = 5;
		public static final int TabLayout_tabIndicatorAnimationDuration = 6;
		public static final int TabLayout_tabIndicatorAnimationMode = 7;
		public static final int TabLayout_tabIndicatorColor = 8;
		public static final int TabLayout_tabIndicatorFullWidth = 9;
		public static final int TabLayout_tabIndicatorGravity = 10;
		public static final int TabLayout_tabIndicatorHeight = 11;
		public static final int TabLayout_tabInlineLabel = 12;
		public static final int TabLayout_tabMaxWidth = 13;
		public static final int TabLayout_tabMinWidth = 14;
		public static final int TabLayout_tabMode = 15;
		public static final int TabLayout_tabPadding = 16;
		public static final int TabLayout_tabPaddingBottom = 17;
		public static final int TabLayout_tabPaddingEnd = 18;
		public static final int TabLayout_tabPaddingStart = 19;
		public static final int TabLayout_tabPaddingTop = 20;
		public static final int TabLayout_tabRippleColor = 21;
		public static final int TabLayout_tabSelectedTextAppearance = 22;
		public static final int TabLayout_tabSelectedTextColor = 23;
		public static final int TabLayout_tabTextAppearance = 24;
		public static final int TabLayout_tabTextColor = 25;
		public static final int TabLayout_tabUnboundedRipple = 26;
		public static final int[] TextAppearance = new int[] { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f03020e, 0x7f030218, 0x7f03046a, 0x7f0304a1 };
		public static final int TextAppearance_android_fontFamily = 10;
		public static final int TextAppearance_android_shadowColor = 6;
		public static final int TextAppearance_android_shadowDx = 7;
		public static final int TextAppearance_android_shadowDy = 8;
		public static final int TextAppearance_android_shadowRadius = 9;
		public static final int TextAppearance_android_textColor = 3;
		public static final int TextAppearance_android_textColorHint = 4;
		public static final int TextAppearance_android_textColorLink = 5;
		public static final int TextAppearance_android_textFontWeight = 11;
		public static final int TextAppearance_android_textSize = 0;
		public static final int TextAppearance_android_textStyle = 2;
		public static final int TextAppearance_android_typeface = 1;
		public static final int TextAppearance_fontFamily = 12;
		public static final int TextAppearance_fontVariationSettings = 13;
		public static final int TextAppearance_textAllCaps = 14;
		public static final int TextAppearance_textLocale = 15;
		public static final int[] TextInputEditText = new int[] { 0x7f03049c };
		public static final int TextInputEditText_textInputLayoutFocusedRectEnabled = 0;
		public static final int[] TextInputLayout = new int[] { 0x0101000e, 0x0101009a, 0x0101011f, 0x0101013f, 0x01010150, 0x01010157, 0x0101015a, 0x7f030085, 0x7f030086, 0x7f030087, 0x7f030088, 0x7f030089, 0x7f03008a, 0x7f03008b, 0x7f03008c, 0x7f03008d, 0x7f03008e, 0x7f03008f, 0x7f030160, 0x7f030161, 0x7f030162, 0x7f030163, 0x7f030164, 0x7f030165, 0x7f030168, 0x7f030169, 0x7f0301af, 0x7f0301b0, 0x7f0301b1, 0x7f0301b2, 0x7f0301b3, 0x7f0301b4, 0x7f0301b5, 0x7f0301b6, 0x7f0301bc, 0x7f0301bd, 0x7f0301be, 0x7f0301bf, 0x7f0301c0, 0x7f0301c1, 0x7f0301c3, 0x7f0301c4, 0x7f0301c8, 0x7f030232, 0x7f030233, 0x7f030234, 0x7f030235, 0x7f03023b, 0x7f03023c, 0x7f03023d, 0x7f03023e, 0x7f030390, 0x7f030391, 0x7f030392, 0x7f030393, 0x7f030394, 0x7f03039e, 0x7f03039f, 0x7f0303a0, 0x7f0303ac, 0x7f0303ad, 0x7f0303ae, 0x7f0303e2, 0x7f0303ea, 0x7f03041c, 0x7f03041d, 0x7f03041e, 0x7f03041f, 0x7f030420, 0x7f030421, 0x7f030422, 0x7f03043e, 0x7f03043f, 0x7f030440 };
		public static final int TextInputLayout_android_enabled = 0;
		public static final int TextInputLayout_android_hint = 4;
		public static final int TextInputLayout_android_maxEms = 5;
		public static final int TextInputLayout_android_maxWidth = 2;
		public static final int TextInputLayout_android_minEms = 6;
		public static final int TextInputLayout_android_minWidth = 3;
		public static final int TextInputLayout_android_textColorHint = 1;
		public static final int TextInputLayout_boxBackgroundColor = 7;
		public static final int TextInputLayout_boxBackgroundMode = 8;
		public static final int TextInputLayout_boxCollapsedPaddingTop = 9;
		public static final int TextInputLayout_boxCornerRadiusBottomEnd = 10;
		public static final int TextInputLayout_boxCornerRadiusBottomStart = 11;
		public static final int TextInputLayout_boxCornerRadiusTopEnd = 12;
		public static final int TextInputLayout_boxCornerRadiusTopStart = 13;
		public static final int TextInputLayout_boxStrokeColor = 14;
		public static final int TextInputLayout_boxStrokeErrorColor = 15;
		public static final int TextInputLayout_boxStrokeWidth = 16;
		public static final int TextInputLayout_boxStrokeWidthFocused = 17;
		public static final int TextInputLayout_counterEnabled = 18;
		public static final int TextInputLayout_counterMaxLength = 19;
		public static final int TextInputLayout_counterOverflowTextAppearance = 20;
		public static final int TextInputLayout_counterOverflowTextColor = 21;
		public static final int TextInputLayout_counterTextAppearance = 22;
		public static final int TextInputLayout_counterTextColor = 23;
		public static final int TextInputLayout_cursorColor = 24;
		public static final int TextInputLayout_cursorErrorColor = 25;
		public static final int TextInputLayout_endIconCheckable = 26;
		public static final int TextInputLayout_endIconContentDescription = 27;
		public static final int TextInputLayout_endIconDrawable = 28;
		public static final int TextInputLayout_endIconMinSize = 29;
		public static final int TextInputLayout_endIconMode = 30;
		public static final int TextInputLayout_endIconScaleType = 31;
		public static final int TextInputLayout_endIconTint = 32;
		public static final int TextInputLayout_endIconTintMode = 33;
		public static final int TextInputLayout_errorAccessibilityLiveRegion = 34;
		public static final int TextInputLayout_errorContentDescription = 35;
		public static final int TextInputLayout_errorEnabled = 36;
		public static final int TextInputLayout_errorIconDrawable = 37;
		public static final int TextInputLayout_errorIconTint = 38;
		public static final int TextInputLayout_errorIconTintMode = 39;
		public static final int TextInputLayout_errorTextAppearance = 40;
		public static final int TextInputLayout_errorTextColor = 41;
		public static final int TextInputLayout_expandedHintEnabled = 42;
		public static final int TextInputLayout_helperText = 43;
		public static final int TextInputLayout_helperTextEnabled = 44;
		public static final int TextInputLayout_helperTextTextAppearance = 45;
		public static final int TextInputLayout_helperTextTextColor = 46;
		public static final int TextInputLayout_hintAnimationEnabled = 47;
		public static final int TextInputLayout_hintEnabled = 48;
		public static final int TextInputLayout_hintTextAppearance = 49;
		public static final int TextInputLayout_hintTextColor = 50;
		public static final int TextInputLayout_passwordToggleContentDescription = 51;
		public static final int TextInputLayout_passwordToggleDrawable = 52;
		public static final int TextInputLayout_passwordToggleEnabled = 53;
		public static final int TextInputLayout_passwordToggleTint = 54;
		public static final int TextInputLayout_passwordToggleTintMode = 55;
		public static final int TextInputLayout_placeholderText = 56;
		public static final int TextInputLayout_placeholderTextAppearance = 57;
		public static final int TextInputLayout_placeholderTextColor = 58;
		public static final int TextInputLayout_prefixText = 59;
		public static final int TextInputLayout_prefixTextAppearance = 60;
		public static final int TextInputLayout_prefixTextColor = 61;
		public static final int TextInputLayout_shapeAppearance = 62;
		public static final int TextInputLayout_shapeAppearanceOverlay = 63;
		public static final int TextInputLayout_startIconCheckable = 64;
		public static final int TextInputLayout_startIconContentDescription = 65;
		public static final int TextInputLayout_startIconDrawable = 66;
		public static final int TextInputLayout_startIconMinSize = 67;
		public static final int TextInputLayout_startIconScaleType = 68;
		public static final int TextInputLayout_startIconTint = 69;
		public static final int TextInputLayout_startIconTintMode = 70;
		public static final int TextInputLayout_suffixText = 71;
		public static final int TextInputLayout_suffixTextAppearance = 72;
		public static final int TextInputLayout_suffixTextColor = 73;
		public static final int[] ThemeEnforcement = new int[] { 0x01010034, 0x7f0301b7, 0x7f0301b8 };
		public static final int ThemeEnforcement_android_textAppearance = 0;
		public static final int ThemeEnforcement_enforceMaterialTheme = 1;
		public static final int ThemeEnforcement_enforceTextAppearance = 2;
		public static final int[] Toolbar = new int[] { 0x010100af, 0x01010140, 0x7f030097, 0x7f0300ef, 0x7f0300f0, 0x7f030143, 0x7f030144, 0x7f030145, 0x7f030146, 0x7f030147, 0x7f030148, 0x7f0302ea, 0x7f0302ec, 0x7f030325, 0x7f03032e, 0x7f03036e, 0x7f03036f, 0x7f0303aa, 0x7f030439, 0x7f03043b, 0x7f03043c, 0x7f0304c8, 0x7f0304cc, 0x7f0304cd, 0x7f0304ce, 0x7f0304cf, 0x7f0304d0, 0x7f0304d1, 0x7f0304d3, 0x7f0304d4 };
		public static final int Toolbar_android_gravity = 0;
		public static final int Toolbar_android_minHeight = 1;
		public static final int Toolbar_buttonGravity = 2;
		public static final int Toolbar_collapseContentDescription = 3;
		public static final int Toolbar_collapseIcon = 4;
		public static final int Toolbar_contentInsetEnd = 5;
		public static final int Toolbar_contentInsetEndWithActions = 6;
		public static final int Toolbar_contentInsetLeft = 7;
		public static final int Toolbar_contentInsetRight = 8;
		public static final int Toolbar_contentInsetStart = 9;
		public static final int Toolbar_contentInsetStartWithNavigation = 10;
		public static final int Toolbar_logo = 11;
		public static final int Toolbar_logoDescription = 12;
		public static final int Toolbar_maxButtonHeight = 13;
		public static final int Toolbar_menu = 14;
		public static final int Toolbar_navigationContentDescription = 15;
		public static final int Toolbar_navigationIcon = 16;
		public static final int Toolbar_popupTheme = 17;
		public static final int Toolbar_subtitle = 18;
		public static final int Toolbar_subtitleTextAppearance = 19;
		public static final int Toolbar_subtitleTextColor = 20;
		public static final int Toolbar_title = 21;
		public static final int Toolbar_titleMargin = 22;
		public static final int Toolbar_titleMarginBottom = 23;
		public static final int Toolbar_titleMarginEnd = 24;
		public static final int Toolbar_titleMarginStart = 25;
		public static final int Toolbar_titleMarginTop = 26;
		public static final int Toolbar_titleMargins = 27;
		public static final int Toolbar_titleTextAppearance = 28;
		public static final int Toolbar_titleTextColor = 29;
		public static final int[] Tooltip = new int[] { 0x01010034, 0x01010098, 0x010100d5, 0x010100f6, 0x0101013f, 0x01010140, 0x0101014f, 0x7f030056, 0x7f0303f3 };
		public static final int Tooltip_android_layout_margin = 3;
		public static final int Tooltip_android_minHeight = 5;
		public static final int Tooltip_android_minWidth = 4;
		public static final int Tooltip_android_padding = 2;
		public static final int Tooltip_android_text = 6;
		public static final int Tooltip_android_textAppearance = 0;
		public static final int Tooltip_android_textColor = 1;
		public static final int Tooltip_backgroundTint = 7;
		public static final int[] Transform = new int[] { 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f0304f2 };
		public static final int Transform_android_elevation = 10;
		public static final int Transform_android_rotation = 6;
		public static final int Transform_android_rotationX = 7;
		public static final int Transform_android_rotationY = 8;
		public static final int Transform_android_scaleX = 4;
		public static final int Transform_android_scaleY = 5;
		public static final int Transform_android_transformPivotX = 0;
		public static final int Transform_android_transformPivotY = 1;
		public static final int Transform_android_translationX = 2;
		public static final int Transform_android_translationY = 3;
		public static final int Transform_android_translationZ = 9;
		public static final int[] Transition = new int[] { 0x010100d0, 0x7f03004b, 0x7f03013c, 0x7f03013d, 0x7f0301a4, 0x7f03028d, 0x7f030363, 0x7f030395, 0x7f03041a, 0x7f0304f3, 0x7f0304f5 };
		public static final int Transition_android_id = 0;
		public static final int Transition_autoTransition = 1;
		public static final int Transition_constraintSetEnd = 2;
		public static final int Transition_constraintSetStart = 3;
		public static final int Transition_duration = 4;
		public static final int Transition_layoutDuringTransition = 5;
		public static final int Transition_motionInterpolator = 6;
		public static final int Transition_pathMotionArc = 7;
		public static final int Transition_staggered = 8;
		public static final int Transition_transitionDisable = 9;
		public static final int Transition_transitionFlags = 10;
		public static final int[] Variant = new int[] { 0x7f030140, 0x7f0303c4, 0x7f0303c5, 0x7f0303c6, 0x7f0303c7 };
		public static final int Variant_constraints = 0;
		public static final int Variant_region_heightLessThan = 1;
		public static final int Variant_region_heightMoreThan = 2;
		public static final int Variant_region_widthLessThan = 3;
		public static final int Variant_region_widthMoreThan = 4;
		public static final int[] View = new int[] { 0x01010000, 0x010100da, 0x7f030386, 0x7f030389, 0x7f0304ab };
		public static final int View_android_focusable = 1;
		public static final int View_android_theme = 0;
		public static final int View_paddingEnd = 2;
		public static final int View_paddingStart = 3;
		public static final int View_theme = 4;
		public static final int[] ViewBackgroundHelper = new int[] { 0x010100d4, 0x7f030056, 0x7f030057 };
		public static final int ViewBackgroundHelper_android_background = 0;
		public static final int ViewBackgroundHelper_backgroundTint = 1;
		public static final int ViewBackgroundHelper_backgroundTintMode = 2;
		public static final int[] ViewPager2 = new int[] { 0x010100c4 };
		public static final int ViewPager2_android_orientation = 0;
		public static final int[] ViewStubCompat = new int[] { 0x010100d0, 0x010100f2, 0x010100f3 };
		public static final int ViewStubCompat_android_id = 0;
		public static final int ViewStubCompat_android_inflatedId = 2;
		public static final int ViewStubCompat_android_layout = 1;
	}
}
