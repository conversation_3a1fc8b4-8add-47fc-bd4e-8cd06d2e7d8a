<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:MauiApp1.ViewModels"
             xmlns:models="clr-namespace:MauiApp1.Models"
             x:Class="MauiApp1.Views.CartPage"
             x:DataType="viewmodels:CartViewModel"
             Title="{Binding Title}">

    <Grid RowDefinitions="Auto,*,Auto" BackgroundColor="{AppThemeBinding Light={StaticResource Background}, Dark={StaticResource BackgroundDark}}">

        <!-- Header -->
        <Border Grid.Row="0"
                BackgroundColor="{AppThemeBinding Light={StaticResource Surface}, Dark={StaticResource SurfaceDark}}"
                Style="{StaticResource SummaryCardStyle}"
                Margin="0,0,0,0"
                StrokeShape="Rectangle">
            <Grid ColumnDefinitions="Auto,*,Auto" Padding="20,15">
                <Label Grid.Column="0"
                       Text="🛒"
                       FontSize="24"
                       VerticalOptions="Center"
                       Margin="0,0,10,0" />
                <Label Grid.Column="1"
                       Text="{Binding Title}"
                       Style="{StaticResource TitleStyle}"
                       VerticalOptions="Center" />
                <Label Grid.Column="2"
                       Text="{Binding CartService.FormattedTotalAmount}"
                       Style="{StaticResource PriceStyle}"
                       FontSize="18"
                       VerticalOptions="Center" />
            </Grid>
        </Border>

        <!-- Cart Items List -->
        <ScrollView Grid.Row="1" Padding="15">
            <StackLayout Spacing="15">

                <!-- Empty Cart Message -->
                <StackLayout IsVisible="{Binding CartService.IsEmpty}"
                             VerticalOptions="CenterAndExpand"
                             HorizontalOptions="CenterAndExpand"
                             Padding="50">
                    <Border BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray800}}"
                            StrokeShape="RoundRectangle 50"
                            WidthRequest="100"
                            HeightRequest="100"
                            HorizontalOptions="Center"
                            Margin="0,0,0,30">
                        <Label Text="🛒"
                               FontSize="48"
                               HorizontalOptions="Center"
                               VerticalOptions="Center" />
                    </Border>
                    <Label Text="Your cart is empty"
                           Style="{StaticResource TitleStyle}"
                           HorizontalOptions="Center"
                           Margin="0,0,0,10" />
                    <Label Text="Add some delicious items to get started!"
                           Style="{StaticResource BodyStyle}"
                           HorizontalOptions="Center"
                           HorizontalTextAlignment="Center"
                           Margin="0,0,0,30" />
                    <Button Text="🛍️ Continue Shopping"
                            Command="{Binding ContinueShoppingCommand}"
                            Style="{StaticResource CategoryButtonStyle}"
                            BackgroundColor="{StaticResource Primary}"
                            TextColor="{StaticResource White}"
                            BorderWidth="0"
                            WidthRequest="220" />
                </StackLayout>

                <!-- Cart Items -->
                <CollectionView ItemsSource="{Binding CartService.Items}"
                                IsVisible="{Binding CartService.IsEmpty, Converter={StaticResource InvertedBoolConverter}}"
                                SelectionMode="None"
                                VerticalScrollBarVisibility="Never">

                    <CollectionView.ItemTemplate>
                        <DataTemplate x:DataType="models:CartItem">
                            <Border Style="{StaticResource ProductCardStyle}" Margin="0,0,0,12">

                                <Grid ColumnDefinitions="Auto,*,Auto,Auto,Auto"
                                      Padding="16"
                                      ColumnSpacing="15"
                                      MinimumHeightRequest="90">

                                    <!-- Product Icon with Category Color -->
                                    <Border Grid.Column="0"
                                            BackgroundColor="{Binding Product.CategoryColor}"
                                            StrokeShape="RoundRectangle 20"
                                            WidthRequest="40"
                                            HeightRequest="40"
                                            VerticalOptions="Center">
                                        <Label Text="{Binding Product.ImageUrl}"
                                               FontSize="20"
                                               HorizontalOptions="Center"
                                               VerticalOptions="Center" />
                                    </Border>

                                    <!-- Product Info -->
                                    <StackLayout Grid.Column="1" VerticalOptions="Center" Spacing="4">
                                        <Label Text="{Binding Product.Name}"
                                               Style="{StaticResource SubtitleStyle}"
                                               FontSize="16" />
                                        <Label Text="{Binding Product.FormattedPrice}"
                                               Style="{StaticResource CaptionStyle}" />
                                    </StackLayout>

                                    <!-- Quantity Controls -->
                                    <Border Grid.Column="2"
                                            BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray700}}"
                                            StrokeShape="RoundRectangle 25"
                                            Padding="8,4"
                                            VerticalOptions="Center">
                                        <StackLayout Orientation="Horizontal" Spacing="12">
                                            <Button Text="−"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:CartViewModel}}, Path=DecreaseQuantityCommand}"
                                                    CommandParameter="{Binding .}"
                                                    Style="{StaticResource ErrorButtonStyle}"
                                                    FontSize="16"
                                                    WidthRequest="36"
                                                    HeightRequest="36"
                                                    CornerRadius="18"
                                                    Padding="0" />

                                            <Label Text="{Binding Quantity}"
                                                   Style="{StaticResource SubtitleStyle}"
                                                   VerticalOptions="Center"
                                                   HorizontalOptions="Center"
                                                   WidthRequest="30"
                                                   HorizontalTextAlignment="Center" />

                                            <Button Text="+"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:CartViewModel}}, Path=IncreaseQuantityCommand}"
                                                    CommandParameter="{Binding .}"
                                                    Style="{StaticResource SuccessButtonStyle}"
                                                    FontSize="16"
                                                    WidthRequest="36"
                                                    HeightRequest="36"
                                                    CornerRadius="18"
                                                    Padding="0" />
                                        </StackLayout>
                                    </Border>

                                    <!-- Total Price -->
                                    <Label Grid.Column="3"
                                           Text="{Binding FormattedTotalPrice}"
                                           Style="{StaticResource PriceStyle}"
                                           VerticalOptions="Center"
                                           WidthRequest="80"
                                           HorizontalTextAlignment="End" />

                                    <!-- Remove Button -->
                                    <Button Grid.Column="4"
                                            Text="🗑️"
                                            Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:CartViewModel}}, Path=RemoveItemCommand}"
                                            CommandParameter="{Binding .}"
                                            Style="{StaticResource ErrorButtonStyle}"
                                            FontSize="14"
                                            WidthRequest="40"
                                            HeightRequest="40"
                                            CornerRadius="20"
                                            Padding="0" />
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>

                <!-- Cart Summary -->
                <Border IsVisible="{Binding CartService.IsEmpty, Converter={StaticResource InvertedBoolConverter}}"
                        Style="{StaticResource SummaryCardStyle}"
                        Margin="0,20,0,0">

                    <StackLayout Padding="20" Spacing="12">
                        <Label Text="Order Summary"
                               Style="{StaticResource SubtitleStyle}"
                               HorizontalOptions="Center"
                               Margin="0,0,0,10" />

                        <Grid ColumnDefinitions="*,Auto">
                            <Label Grid.Column="0"
                                   Text="Subtotal:"
                                   Style="{StaticResource BodyStyle}"
                                   VerticalOptions="Center" />
                            <Label Grid.Column="1"
                                   Text="{Binding CartService.FormattedTotalAmount}"
                                   Style="{StaticResource SubtitleStyle}"
                                   VerticalOptions="Center" />
                        </Grid>

                        <Grid ColumnDefinitions="*,Auto">
                            <Label Grid.Column="0"
                                   Text="Tax (8%):"
                                   Style="{StaticResource BodyStyle}"
                                   VerticalOptions="Center" />
                            <Label Grid.Column="1"
                                   Text="{Binding CartService.TotalAmount, StringFormat='${0:F2}', Converter={StaticResource TaxCalculatorConverter}}"
                                   Style="{StaticResource BodyStyle}"
                                   VerticalOptions="Center" />
                        </Grid>

                        <BoxView HeightRequest="1"
                                 BackgroundColor="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}"
                                 Margin="0,8" />

                        <Grid ColumnDefinitions="*,Auto">
                            <Label Grid.Column="0"
                                   Text="Total:"
                                   Style="{StaticResource TitleStyle}"
                                   FontSize="18"
                                   VerticalOptions="Center" />
                            <Label Grid.Column="1"
                                   Text="{Binding CartService.TotalAmount, StringFormat='${0:F2}', Converter={StaticResource TotalWithTaxConverter}}"
                                   Style="{StaticResource PriceStyle}"
                                   FontSize="20"
                                   VerticalOptions="Center" />
                        </Grid>
                    </StackLayout>
                </Border>
            </StackLayout>
        </ScrollView>

        <!-- Action Buttons -->
        <Border Grid.Row="2"
                BackgroundColor="{AppThemeBinding Light={StaticResource Surface}, Dark={StaticResource SurfaceDark}}"
                Style="{StaticResource SummaryCardStyle}"
                Margin="0,0,0,0"
                StrokeShape="Rectangle"
                IsVisible="{Binding CartService.IsEmpty, Converter={StaticResource InvertedBoolConverter}}">

            <Grid ColumnDefinitions="*,*,*"
                  ColumnSpacing="12"
                  Padding="20,15">

                <Button Grid.Column="0"
                        Text="🛍️ Continue"
                        Command="{Binding ContinueShoppingCommand}"
                        Style="{StaticResource CategoryButtonStyle}"
                        BackgroundColor="{StaticResource Info}"
                        TextColor="{StaticResource White}"
                        BorderWidth="0"
                        HorizontalOptions="Fill" />

                <Button Grid.Column="1"
                        Text="🗑️ Clear"
                        Command="{Binding ClearCartCommand}"
                        Style="{StaticResource ErrorButtonStyle}"
                        HorizontalOptions="Fill" />

                <Button Grid.Column="2"
                        Text="💳 Checkout"
                        Command="{Binding CheckoutCommand}"
                        Style="{StaticResource WarningButtonStyle}"
                        HorizontalOptions="Fill" />
            </Grid>
        </Border>
    </Grid>

</ContentPage>
