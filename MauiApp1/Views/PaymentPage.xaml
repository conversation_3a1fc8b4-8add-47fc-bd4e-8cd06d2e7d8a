<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:MauiApp1.ViewModels"
             xmlns:models="clr-namespace:MauiApp1.Models"
             x:Class="MauiApp1.Views.PaymentPage"
             x:DataType="viewmodels:PaymentViewModel"
             Title="{Binding Title}">

    <Grid RowDefinitions="Auto,*" BackgroundColor="{AppThemeBinding Light={StaticResource Background}, Dark={StaticResource BackgroundDark}}">

        <!-- Header -->
        <Border Grid.Row="0"
                BackgroundColor="{AppThemeBinding Light={StaticResource Surface}, Dark={StaticResource SurfaceDark}}"
                Style="{StaticResource SummaryCardStyle}"
                Margin="0,0,0,0"
                StrokeShape="Rectangle">
            <Grid ColumnDefinitions="Auto,*,Auto" Padding="20,15">
                <Label Grid.Column="0"
                       Text="💳"
                       FontSize="24"
                       VerticalOptions="Center"
                       Margin="0,0,10,0" />
                <Label Grid.Column="1"
                       Text="{Binding Title}"
                       Style="{StaticResource TitleStyle}"
                       VerticalOptions="Center" />
                <Label Grid.Column="2"
                       Text="{Binding CartService.TotalAmount, StringFormat='${0:F2}', Converter={StaticResource TotalWithTaxConverter}}"
                       Style="{StaticResource PriceStyle}"
                       FontSize="18"
                       VerticalOptions="Center" />
            </Grid>
        </Border>

        <ScrollView Grid.Row="1" Padding="20">
            <StackLayout Spacing="20">

                <!-- Order Summary -->
                <Border Style="{StaticResource SummaryCardStyle}">

                    <StackLayout Padding="20" Spacing="15">
                        <Label Text="📋 Order Summary"
                               Style="{StaticResource SubtitleStyle}"
                               HorizontalOptions="Center" />
                    
                    <CollectionView ItemsSource="{Binding CartService.Items}"
                                    SelectionMode="None"
                                    VerticalScrollBarVisibility="Never"
                                    MaximumHeightRequest="200">
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="models:CartItem">
                                <Grid ColumnDefinitions="*,Auto,Auto" Margin="0,5">
                                    <Label Grid.Column="0" 
                                           Text="{Binding DisplayText}"
                                           FontSize="14"
                                           VerticalOptions="Center" />
                                    <Label Grid.Column="1" 
                                           Text="{Binding Product.FormattedPrice}"
                                           FontSize="14"
                                           VerticalOptions="Center"
                                           Margin="10,0" />
                                    <Label Grid.Column="2" 
                                           Text="{Binding FormattedTotalPrice}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="{AppThemeBinding Light=#2E7D32, Dark=#4CAF50}"
                                           VerticalOptions="Center" />
                                </Grid>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                    
                    <BoxView HeightRequest="1" 
                             BackgroundColor="{AppThemeBinding Light=#E0E0E0, Dark=#555}" />
                    
                    <Grid ColumnDefinitions="*,Auto">
                        <Label Grid.Column="0" 
                               Text="Subtotal:"
                               FontSize="16"
                               VerticalOptions="Center" />
                        <Label Grid.Column="1" 
                               Text="{Binding CartService.FormattedTotalAmount}"
                               FontSize="16"
                               FontAttributes="Bold"
                               VerticalOptions="Center" />
                    </Grid>
                    
                    <Grid ColumnDefinitions="*,Auto">
                        <Label Grid.Column="0" 
                               Text="Tax (8%):"
                               FontSize="16"
                               VerticalOptions="Center" />
                        <Label Grid.Column="1" 
                               Text="{Binding CartService.TotalAmount, StringFormat='${0:F2}', Converter={StaticResource TaxCalculatorConverter}}"
                               FontSize="16"
                               VerticalOptions="Center" />
                    </Grid>
                    
                    <Grid ColumnDefinitions="*,Auto">
                        <Label Grid.Column="0" 
                               Text="Total:"
                               FontSize="18"
                               FontAttributes="Bold"
                               VerticalOptions="Center" />
                        <Label Grid.Column="1" 
                               Text="{Binding CartService.TotalAmount, StringFormat='${0:F2}', Converter={StaticResource TotalWithTaxConverter}}"
                               FontSize="18"
                               FontAttributes="Bold"
                               TextColor="{AppThemeBinding Light=#2E7D32, Dark=#4CAF50}"
                               VerticalOptions="Center" />
                    </Grid>
                </StackLayout>
            </Border>
            
            <!-- Payment Method Selection -->
            <Border BackgroundColor="{AppThemeBinding Light=White, Dark=#2C2C2C}"
                    Stroke="{AppThemeBinding Light=#E0E0E0, Dark=#404040}"
                    StrokeThickness="1"
                    StrokeShape="RoundRectangle 15">
                
                <StackLayout Padding="20" Spacing="15">
                    <Label Text="Payment Method"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{AppThemeBinding Light=Black, Dark=White}" />
                    
                    <CollectionView ItemsSource="{Binding PaymentMethods}"
                                    SelectionMode="Single"
                                    SelectedItem="{Binding SelectedPaymentMethod}"
                                    VerticalScrollBarVisibility="Never">
                        <CollectionView.ItemsLayout>
                            <GridItemsLayout Orientation="Vertical" 
                                             Span="2" 
                                             HorizontalItemSpacing="10" 
                                             VerticalItemSpacing="10" />
                        </CollectionView.ItemsLayout>
                        
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="models:PaymentMethod">
                                <Border BackgroundColor="{AppThemeBinding Light=#F0F0F0, Dark=#404040}"
                                        Stroke="{AppThemeBinding Light=#D0D0D0, Dark=#606060}"
                                        StrokeThickness="1"
                                        StrokeShape="RoundRectangle 10">
                                    
                                    <StackLayout Padding="15" Spacing="5" HorizontalOptions="Center">
                                        <Label Text="{Binding ., Converter={StaticResource PaymentMethodIconConverter}}"
                                               FontSize="24"
                                               HorizontalOptions="Center" />
                                        <Label Text="{Binding .}"
                                               FontSize="14"
                                               FontAttributes="Bold"
                                               HorizontalOptions="Center"
                                               TextColor="{AppThemeBinding Light=Black, Dark=White}" />
                                    </StackLayout>
                                </Border>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                </StackLayout>
            </Border>
            
            <!-- Amount Received (for Cash payments) -->
            <Border BackgroundColor="{AppThemeBinding Light=White, Dark=#2C2C2C}"
                    Stroke="{AppThemeBinding Light=#E0E0E0, Dark=#404040}"
                    StrokeThickness="1"
                    StrokeShape="RoundRectangle 15"
                    IsVisible="{Binding SelectedPaymentMethod, Converter={StaticResource PaymentMethodToCashVisibilityConverter}}">
                
                <StackLayout Padding="20" Spacing="15">
                    <Label Text="Amount Received"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{AppThemeBinding Light=Black, Dark=White}" />
                    
                    <Entry Text="{Binding AmountReceived}"
                           Keyboard="Numeric"
                           FontSize="16"
                           HeightRequest="50"
                           BackgroundColor="{AppThemeBinding Light=#F8F8F8, Dark=#404040}"
                           TextColor="{AppThemeBinding Light=Black, Dark=White}" />
                    
                    <Grid ColumnDefinitions="*,Auto" IsVisible="{Binding ChangeAmount, Converter={StaticResource GreaterThanZeroConverter}}">
                        <Label Grid.Column="0" 
                               Text="Change:"
                               FontSize="16"
                               FontAttributes="Bold"
                               VerticalOptions="Center" />
                        <Label Grid.Column="1" 
                               Text="{Binding FormattedChangeAmount}"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="{AppThemeBinding Light=#4CAF50, Dark=#66BB6A}"
                               VerticalOptions="Center" />
                    </Grid>
                </StackLayout>
            </Border>
            
            <!-- Notes -->
            <Border BackgroundColor="{AppThemeBinding Light=White, Dark=#2C2C2C}"
                    Stroke="{AppThemeBinding Light=#E0E0E0, Dark=#404040}"
                    StrokeThickness="1"
                    StrokeShape="RoundRectangle 15">
                
                <StackLayout Padding="20" Spacing="15">
                    <Label Text="Notes (Optional)"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{AppThemeBinding Light=Black, Dark=White}" />
                    
                    <Editor Text="{Binding Notes}"
                            Placeholder="Add any special instructions or notes..."
                            FontSize="16"
                            HeightRequest="80"
                            BackgroundColor="{AppThemeBinding Light=#F8F8F8, Dark=#404040}"
                            TextColor="{AppThemeBinding Light=Black, Dark=White}" />
                </StackLayout>
            </Border>
            
            <!-- Action Buttons -->
            <Grid ColumnDefinitions="*,*" ColumnSpacing="15" Margin="0,30,0,20">
                <Button Grid.Column="0"
                        Text="❌ Cancel"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource ErrorButtonStyle}"
                        HorizontalOptions="Fill" />

                <Button Grid.Column="1"
                        Text="✅ Process Payment"
                        Command="{Binding ProcessPaymentCommand}"
                        Style="{StaticResource SuccessButtonStyle}"
                        HorizontalOptions="Fill" />
            </Grid>
        </StackLayout>
        </ScrollView>
    </Grid>

</ContentPage>
