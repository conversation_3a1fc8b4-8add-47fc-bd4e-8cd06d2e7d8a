using System.Windows.Input;
using MauiApp1.Models;
using MauiApp1.Services;

namespace MauiApp1.ViewModels
{
    public class CartViewModel : BaseViewModel
    {
        private readonly CartService _cartService;
        
        public CartViewModel(CartService cartService)
        {
            _cartService = cartService;
            Title = "Shopping Cart";
            
            IncreaseQuantityCommand = new Command<CartItem>(OnIncreaseQuantity);
            DecreaseQuantityCommand = new Command<CartItem>(OnDecreaseQuantity);
            RemoveItemCommand = new Command<CartItem>(OnRemoveItem);
            ClearCartCommand = new Command(OnClearCart, CanClearCart);
            ContinueShoppingCommand = new Command(OnContinueShopping);
            CheckoutCommand = new Command(OnCheckout, CanCheckout);
            
            // Subscribe to cart changes
            _cartService.PropertyChanged += (s, e) => 
            {
                OnPropertyChanged(nameof(CartService));
                ((Command)ClearCartCommand).ChangeCanExecute();
                ((Command)CheckoutCommand).ChangeCanExecute();
            };
        }
        
        public CartService CartService => _cartService;
        
        public ICommand IncreaseQuantityCommand { get; }
        public ICommand DecreaseQuantityCommand { get; }
        public ICommand RemoveItemCommand { get; }
        public ICommand ClearCartCommand { get; }
        public ICommand ContinueShoppingCommand { get; }
        public ICommand CheckoutCommand { get; }
        
        private void OnIncreaseQuantity(CartItem item)
        {
            if (item != null)
            {
                _cartService.UpdateQuantity(item.Product.Id, item.Quantity + 1);
            }
        }
        
        private void OnDecreaseQuantity(CartItem item)
        {
            if (item != null && item.Quantity > 1)
            {
                _cartService.UpdateQuantity(item.Product.Id, item.Quantity - 1);
            }
        }
        
        private void OnRemoveItem(CartItem item)
        {
            if (item != null)
            {
                _cartService.RemoveProduct(item.Product.Id);
            }
        }
        
        private async void OnClearCart()
        {
            var result = await Application.Current?.MainPage?.DisplayAlert(
                "Clear Cart", 
                "Are you sure you want to remove all items from the cart?", 
                "Yes", 
                "No");
                
            if (result == true)
            {
                _cartService.Clear();
            }
        }
        
        private bool CanClearCart()
        {
            return !_cartService.IsEmpty;
        }
        
        private async void OnContinueShopping()
        {
            await Shell.Current.GoToAsync("//main");
        }
        
        private async void OnCheckout()
        {
            await Shell.Current.GoToAsync("//payment");
        }
        
        private bool CanCheckout()
        {
            return !_cartService.IsEmpty;
        }
    }
}
