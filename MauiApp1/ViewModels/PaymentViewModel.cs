using System.Collections.ObjectModel;
using System.Windows.Input;
using MauiApp1.Models;
using MauiApp1.Services;

namespace MauiApp1.ViewModels
{
    public class PaymentViewModel : BaseViewModel
    {
        private readonly CartService _cartService;
        private PaymentMethod _selectedPaymentMethod = PaymentMethod.Cash;
        private decimal _amountReceived;
        private string _notes = string.Empty;
        
        public PaymentViewModel(CartService cartService)
        {
            _cartService = cartService;
            Title = "Payment";
            
            PaymentMethods = new ObservableCollection<PaymentMethod>
            {
                PaymentMethod.Cash,
                PaymentMethod.CreditCard,
                PaymentMethod.DebitCard,
                PaymentMethod.DigitalWallet,
                PaymentMethod.GiftCard
            };
            
            ProcessPaymentCommand = new Command(OnProcessPayment, CanProcessPayment);
            CancelCommand = new Command(OnCancel);
            
            // Set default amount received to total
            AmountReceived = _cartService.TotalAmount;
        }
        
        public CartService CartService => _cartService;
        
        public ObservableCollection<PaymentMethod> PaymentMethods { get; }
        
        public PaymentMethod SelectedPaymentMethod
        {
            get => _selectedPaymentMethod;
            set 
            { 
                SetProperty(ref _selectedPaymentMethod, value);
                ((Command)ProcessPaymentCommand).ChangeCanExecute();
            }
        }
        
        public decimal AmountReceived
        {
            get => _amountReceived;
            set 
            { 
                SetProperty(ref _amountReceived, value);
                OnPropertyChanged(nameof(ChangeAmount));
                OnPropertyChanged(nameof(FormattedChangeAmount));
                ((Command)ProcessPaymentCommand).ChangeCanExecute();
            }
        }
        
        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }
        
        public decimal ChangeAmount => Math.Max(0, AmountReceived - _cartService.TotalAmount);
        public string FormattedChangeAmount => $"${ChangeAmount:F2}";
        
        public ICommand ProcessPaymentCommand { get; }
        public ICommand CancelCommand { get; }
        
        private async void OnProcessPayment()
        {
            if (IsBusy) return;
            
            IsBusy = true;
            
            try
            {
                // Create order
                var order = _cartService.CreateOrder(SelectedPaymentMethod, Notes);
                
                // Clear cart
                _cartService.Clear();
                
                // Show success message
                await Application.Current?.MainPage?.DisplayAlert(
                    "Payment Successful", 
                    $"Order completed successfully!\nTotal: {order.FormattedTotal}\nChange: {FormattedChangeAmount}", 
                    "OK");
                
                // Navigate back to main
                await Shell.Current.GoToAsync("//main");
            }
            catch (Exception ex)
            {
                await Application.Current?.MainPage?.DisplayAlert("Error", $"Payment failed: {ex.Message}", "OK");
            }
            finally
            {
                IsBusy = false;
            }
        }
        
        private bool CanProcessPayment()
        {
            return !_cartService.IsEmpty && 
                   AmountReceived >= _cartService.TotalAmount &&
                   !IsBusy;
        }
        
        private async void OnCancel()
        {
            await Shell.Current.GoToAsync("//cart");
        }
        
        public void RefreshTotals()
        {
            OnPropertyChanged(nameof(CartService));
            AmountReceived = _cartService.TotalAmount;
        }
    }
}
