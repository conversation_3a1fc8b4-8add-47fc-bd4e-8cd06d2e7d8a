using System.Collections.ObjectModel;
using System.Windows.Input;
using MauiApp1.Models;
using MauiApp1.Services;

namespace MauiApp1.ViewModels
{
    public class MainViewModel : BaseViewModel
    {
        private readonly ProductService _productService;
        private readonly CartService _cartService;
        private string _selectedCategory = "All";
        private Product? _selectedProduct;
        
        public MainViewModel(ProductService productService, CartService cartService)
        {
            _productService = productService;
            _cartService = cartService;
            
            Title = "POS System";
            
            Products = new ObservableCollection<Product>();
            Categories = new ObservableCollection<string>();
            
            AddToCartCommand = new Command<Product>(OnAddToCart, CanAddToCart);
            SelectCategoryCommand = new Command<string>(OnSelectCategory);
            ViewCartCommand = new Command(OnViewCart);
            CheckoutCommand = new Command(OnCheckout, CanCheckout);
            ClearCartCommand = new Command(OnClearCart, CanClearCart);

            LoadDataCommand = new Command(async () => await LoadDataAsync());
            
            // Subscribe to cart changes
            _cartService.PropertyChanged += (s, e) =>
            {
                OnPropertyChanged(nameof(CartItemCount));
                OnPropertyChanged(nameof(CartTotal));
                ((Command)CheckoutCommand).ChangeCanExecute();
                ((Command)ClearCartCommand).ChangeCanExecute();
            };
        }
        
        public ObservableCollection<Product> Products { get; }
        public ObservableCollection<string> Categories { get; }
        
        public CartService CartService => _cartService;
        
        public string SelectedCategory
        {
            get => _selectedCategory;
            set => SetProperty(ref _selectedCategory, value);
        }
        
        public Product? SelectedProduct
        {
            get => _selectedProduct;
            set => SetProperty(ref _selectedProduct, value);
        }
        
        public int CartItemCount => _cartService.ItemCount;
        public string CartTotal => _cartService.FormattedTotalAmount;
        
        public ICommand AddToCartCommand { get; }
        public ICommand SelectCategoryCommand { get; }
        public ICommand ViewCartCommand { get; }
        public ICommand CheckoutCommand { get; }
        public ICommand ClearCartCommand { get; }
        public ICommand LoadDataCommand { get; }
        
        public async Task LoadDataAsync()
        {
            if (IsBusy) return;
            
            IsBusy = true;
            
            try
            {
                // Load categories
                var categories = await _productService.GetCategoriesAsync();
                Categories.Clear();
                Categories.Add("All");
                foreach (var category in categories)
                {
                    Categories.Add(category);
                }
                
                // Load products
                await LoadProductsAsync();
            }
            catch (Exception ex)
            {
                await Application.Current?.MainPage?.DisplayAlert("Error", $"Failed to load data: {ex.Message}", "OK");
            }
            finally
            {
                IsBusy = false;
            }
        }
        
        private async Task LoadProductsAsync()
        {
            var products = SelectedCategory == "All" 
                ? await _productService.GetAllProductsAsync()
                : await _productService.GetProductsByCategoryAsync(SelectedCategory);
                
            Products.Clear();
            foreach (var product in products)
            {
                Products.Add(product);
            }
        }
        
        private void OnAddToCart(Product product)
        {
            if (product != null)
            {
                _cartService.AddProduct(product);
            }
        }
        
        private bool CanAddToCart(Product product)
        {
            return product?.IsAvailable == true;
        }
        
        private async void OnSelectCategory(string category)
        {
            if (category != null && category != SelectedCategory)
            {
                SelectedCategory = category;
                await LoadProductsAsync();
            }
        }
        
        private async void OnViewCart()
        {
            // Navigate to cart view - will be implemented when we create the cart page
            await Shell.Current.GoToAsync("//cart");
        }
        
        private async void OnCheckout()
        {
            // Navigate to checkout - will be implemented when we create the payment page
            await Shell.Current.GoToAsync("//payment");
        }
        
        private bool CanCheckout()
        {
            return !_cartService.IsEmpty;
        }

        private async void OnClearCart()
        {
            var result = await Application.Current?.MainPage?.DisplayAlert(
                "Clear Cart",
                "Are you sure you want to remove all items from the cart?",
                "Yes",
                "No");

            if (result == true)
            {
                _cartService.Clear();
            }
        }

        private bool CanClearCart()
        {
            return !_cartService.IsEmpty;
        }
    }
}
