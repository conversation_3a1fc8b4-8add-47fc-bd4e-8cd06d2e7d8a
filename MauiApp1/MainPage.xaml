﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:MauiApp1.ViewModels"
             xmlns:models="clr-namespace:MauiApp1.Models"
             x:Class="MauiApp1.MainPage"
             x:DataType="viewmodels:MainViewModel"
             Title="{Binding Title}">

    <Grid RowDefinitions="Auto,Auto,*,Auto" BackgroundColor="{AppThemeBinding Light={StaticResource Background}, Dark={StaticResource BackgroundDark}}">

        <!-- App Header -->
        <Border Grid.Row="0"
                BackgroundColor="{AppThemeBinding Light={StaticResource Surface}, Dark={StaticResource SurfaceDark}}"
                Style="{StaticResource SummaryCardStyle}"
                Margin="0,0,0,0"
                StrokeShape="Rectangle">
            <Grid ColumnDefinitions="*,Auto" Padding="20,15">
                <Label Grid.Column="0"
                       Text="POS System"
                       Style="{StaticResource TitleStyle}"
                       VerticalOptions="Center" />
                <StackLayout Grid.Column="1" Orientation="Horizontal" Spacing="15" VerticalOptions="Center">
                    <Border BackgroundColor="{AppThemeBinding Light={StaticResource Success}, Dark={StaticResource SecondaryDark}}"
                            StrokeShape="RoundRectangle 20"
                            Padding="12,8">
                        <Label Text="{Binding CartItemCount, StringFormat='🛒 {0}'}"
                               TextColor="{StaticResource White}"
                               FontSize="14"
                               FontAttributes="Bold" />
                    </Border>
                    <Label Text="{Binding CartTotal}"
                           Style="{StaticResource PriceStyle}"
                           FontSize="18"
                           VerticalOptions="Center" />
                </StackLayout>
            </Grid>
        </Border>

        <!-- Categories Section -->
        <Grid Grid.Row="1" Margin="15,15,15,10">
            <ScrollView Orientation="Horizontal" HorizontalScrollBarVisibility="Never">
                <CollectionView ItemsSource="{Binding Categories}"
                                SelectionMode="None"
                                ItemsLayout="HorizontalList">
                    <CollectionView.ItemTemplate>
                        <DataTemplate x:DataType="x:String">
                            <Border Style="{StaticResource CategoryCardStyle}" Margin="5,0">
                                <Button Text="{Binding .}"
                                        Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:MainViewModel}}, Path=SelectCategoryCommand}"
                                        CommandParameter="{Binding .}"
                                        Style="{StaticResource CategoryButtonStyle}"
                                        BackgroundColor="Transparent"
                                        BorderWidth="0" />
                            </Border>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
            </ScrollView>
        </Grid>

        <!-- Products Grid -->
        <ScrollView Grid.Row="2" Padding="15,0,15,15">
            <CollectionView ItemsSource="{Binding Products}"
                            SelectionMode="None"
                            BackgroundColor="Transparent">
                <CollectionView.ItemsLayout>
                    <GridItemsLayout Orientation="Vertical"
                                     Span="4"
                                     HorizontalItemSpacing="12"
                                     VerticalItemSpacing="12" />
                </CollectionView.ItemsLayout>

                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="models:Product">
                        <Border Style="{StaticResource ProductCardStyle}">
                            <!-- Category Color Accent -->
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="{Binding CategoryLightColor}" Offset="0.0" />
                                    <GradientStop Color="{AppThemeBinding Light={StaticResource Surface}, Dark={StaticResource SurfaceDark}}" Offset="0.3" />
                                </LinearGradientBrush>
                            </Border.Background>

                            <Grid RowDefinitions="Auto,*,Auto,Auto" Padding="12" MinimumHeightRequest="200">

                                <!-- Product Emoji/Icon with Background -->
                                <Border Grid.Row="0"
                                        BackgroundColor="{Binding CategoryColor}"
                                        StrokeShape="RoundRectangle 25"
                                        WidthRequest="50"
                                        HeightRequest="50"
                                        HorizontalOptions="Center"
                                        Margin="0,8,0,12">
                                    <Label Text="{Binding ImageUrl}"
                                           FontSize="24"
                                           HorizontalOptions="Center"
                                           VerticalOptions="Center" />
                                </Border>

                                <!-- Product Name -->
                                <Label Grid.Row="1"
                                       Text="{Binding Name}"
                                       Style="{StaticResource ProductNameStyle}"
                                       HorizontalOptions="Center"
                                       VerticalOptions="Center"
                                       Margin="0,0,0,8" />

                                <!-- Price -->
                                <Label Grid.Row="2"
                                       Text="{Binding FormattedPrice}"
                                       Style="{StaticResource PriceStyle}"
                                       HorizontalOptions="Center"
                                       Margin="0,0,0,12" />

                                <!-- Add to Cart Button -->
                                <Button Grid.Row="3"
                                        Text="+ Add"
                                        Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:MainViewModel}}, Path=AddToCartCommand}"
                                        CommandParameter="{Binding .}"
                                        Style="{StaticResource ProductButtonStyle}"
                                        BackgroundColor="{Binding CategoryColor}"
                                        HorizontalOptions="Fill" />
                            </Grid>
                        </Border>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </ScrollView>

        <!-- Bottom Action Bar -->
        <Border Grid.Row="3"
                BackgroundColor="{AppThemeBinding Light={StaticResource Surface}, Dark={StaticResource SurfaceDark}}"
                Style="{StaticResource SummaryCardStyle}"
                Margin="0,0,0,0"
                StrokeShape="Rectangle">
            <Grid ColumnDefinitions="*,Auto,*" Padding="20,15" ColumnSpacing="15">
                <Button Grid.Column="0"
                        Text="🗑️ Clear Cart"
                        Command="{Binding ClearCartCommand}"
                        Style="{StaticResource ErrorButtonStyle}"
                        HorizontalOptions="Fill" />

                <Button Grid.Column="1"
                        Text="🛒 View Cart"
                        Command="{Binding ViewCartCommand}"
                        Style="{StaticResource CategoryButtonStyle}"
                        BackgroundColor="{AppThemeBinding Light={StaticResource Info}, Dark={StaticResource PrimaryLight}}"
                        TextColor="{StaticResource White}"
                        BorderWidth="0"
                        MinimumWidthRequest="120" />

                <Button Grid.Column="2"
                        Text="💳 Checkout"
                        Command="{Binding CheckoutCommand}"
                        Style="{StaticResource WarningButtonStyle}"
                        HorizontalOptions="Fill" />
            </Grid>
        </Border>

    </Grid>

</ContentPage>
