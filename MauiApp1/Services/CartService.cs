using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using MauiApp1.Models;

namespace MauiApp1.Services
{
    public class CartService : INotifyPropertyChanged
    {
        private readonly ObservableCollection<CartItem> _items;
        
        public CartService()
        {
            _items = new ObservableCollection<CartItem>();
            _items.CollectionChanged += (s, e) => 
            {
                OnPropertyChanged(nameof(Items));
                OnPropertyChanged(nameof(TotalAmount));
                OnPropertyChanged(nameof(FormattedTotalAmount));
                OnPropertyChanged(nameof(ItemCount));
                OnPropertyChanged(nameof(IsEmpty));
            };
        }
        
        public ObservableCollection<CartItem> Items => _items;
        
        public decimal TotalAmount => _items.Sum(item => item.TotalPrice);
        
        public string FormattedTotalAmount => $"${TotalAmount:F2}";
        
        public int ItemCount => _items.Sum(item => item.Quantity);
        
        public bool IsEmpty => !_items.Any();
        
        public void AddProduct(Product product, int quantity = 1)
        {
            var existingItem = _items.FirstOrDefault(item => item.Product.Id == product.Id);
            
            if (existingItem != null)
            {
                existingItem.Quantity += quantity;
            }
            else
            {
                var newItem = new CartItem { Product = product, Quantity = quantity };
                newItem.PropertyChanged += OnCartItemPropertyChanged;
                _items.Add(newItem);
            }
        }
        
        public void RemoveProduct(int productId)
        {
            var item = _items.FirstOrDefault(item => item.Product.Id == productId);
            if (item != null)
            {
                item.PropertyChanged -= OnCartItemPropertyChanged;
                _items.Remove(item);
            }
        }
        
        public void UpdateQuantity(int productId, int newQuantity)
        {
            var item = _items.FirstOrDefault(item => item.Product.Id == productId);
            if (item != null)
            {
                if (newQuantity <= 0)
                {
                    RemoveProduct(productId);
                }
                else
                {
                    item.Quantity = newQuantity;
                }
            }
        }
        
        public void Clear()
        {
            foreach (var item in _items.ToList())
            {
                item.PropertyChanged -= OnCartItemPropertyChanged;
            }
            _items.Clear();
        }
        
        public Order CreateOrder(PaymentMethod paymentMethod, string? notes = null)
        {
            var order = new Order
            {
                Items = _items.ToList(),
                PaymentMethod = paymentMethod,
                Notes = notes,
                Status = OrderStatus.Completed
            };
            
            return order;
        }
        
        private void OnCartItemPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(CartItem.TotalPrice))
            {
                OnPropertyChanged(nameof(TotalAmount));
                OnPropertyChanged(nameof(FormattedTotalAmount));
            }
        }
        
        public event PropertyChangedEventHandler? PropertyChanged;
        
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
