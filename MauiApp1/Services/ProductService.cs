using MauiApp1.Models;

namespace MauiApp1.Services
{
    public class ProductService
    {
        private readonly List<Product> _products;
        
        public ProductService()
        {
            _products = GenerateSampleProducts();
        }
        
        public Task<List<Product>> GetAllProductsAsync()
        {
            return Task.FromResult(_products.Where(p => p.IsAvailable).ToList());
        }
        
        public Task<List<Product>> GetProductsByCategoryAsync(string category)
        {
            return Task.FromResult(_products
                .Where(p => p.IsAvailable && p.Category.Equals(category, StringComparison.OrdinalIgnoreCase))
                .ToList());
        }
        
        public Task<Product?> GetProductByIdAsync(int id)
        {
            return Task.FromResult(_products.FirstOrDefault(p => p.Id == id));
        }
        
        public Task<List<string>> GetCategoriesAsync()
        {
            return Task.FromResult(_products
                .Where(p => p.IsAvailable)
                .Select(p => p.Category)
                .Distinct()
                .OrderBy(c => c)
                .ToList());
        }
        
        private List<Product> GenerateSampleProducts()
        {
            return new List<Product>
            {
                // Food Items
                new Product { Id = 1, Name = "Premium Cheeseburger", Description = "Beef patty with cheese, lettuce, tomato", Price = 12.99m, Category = "Food", ImageUrl = "🍔" },
                new Product { Id = 2, Name = "Chicken Sandwich", Description = "Grilled chicken breast with mayo", Price = 10.99m, Category = "Food", ImageUrl = "🥪" },
                new Product { Id = 3, Name = "Caesar Salad", Description = "Fresh romaine with caesar dressing", Price = 8.99m, Category = "Food", ImageUrl = "🥗" },
                new Product { Id = 4, Name = "Fish & Chips", Description = "Beer battered fish with fries", Price = 14.99m, Category = "Food", ImageUrl = "🍟" },
                new Product { Id = 5, Name = "Margherita Pizza", Description = "Fresh mozzarella, basil, tomato sauce", Price = 16.99m, Category = "Food", ImageUrl = "🍕" },
                new Product { Id = 6, Name = "Pasta Carbonara", Description = "Creamy pasta with bacon and parmesan", Price = 13.99m, Category = "Food", ImageUrl = "🍝" },
                
                // Beverages
                new Product { Id = 7, Name = "Coca Cola", Description = "Classic soft drink", Price = 2.99m, Category = "Beverage", ImageUrl = "🥤" },
                new Product { Id = 8, Name = "Fresh Orange Juice", Description = "Freshly squeezed orange juice", Price = 4.99m, Category = "Beverage", ImageUrl = "🍊" },
                new Product { Id = 9, Name = "Coffee", Description = "Freshly brewed coffee", Price = 3.49m, Category = "Beverage", ImageUrl = "☕" },
                new Product { Id = 10, Name = "Iced Tea", Description = "Refreshing iced tea", Price = 2.79m, Category = "Beverage", ImageUrl = "🧊" },
                new Product { Id = 11, Name = "Smoothie", Description = "Mixed fruit smoothie", Price = 5.99m, Category = "Beverage", ImageUrl = "🥤" },
                
                // Appetizers
                new Product { Id = 12, Name = "Chicken Wings", Description = "Spicy buffalo wings", Price = 9.99m, Category = "Appetizer", ImageUrl = "🍗" },
                new Product { Id = 13, Name = "Mozzarella Sticks", Description = "Breaded mozzarella with marinara", Price = 7.99m, Category = "Appetizer", ImageUrl = "🧀" },
                new Product { Id = 14, Name = "Nachos", Description = "Tortilla chips with cheese and jalapeños", Price = 8.49m, Category = "Appetizer", ImageUrl = "🌮" },
                
                // Desserts
                new Product { Id = 15, Name = "Chocolate Cake", Description = "Rich chocolate layer cake", Price = 6.99m, Category = "Dessert", ImageUrl = "🍰" },
                new Product { Id = 16, Name = "Ice Cream", Description = "Vanilla ice cream scoop", Price = 4.49m, Category = "Dessert", ImageUrl = "🍦" },
                new Product { Id = 17, Name = "Apple Pie", Description = "Classic apple pie slice", Price = 5.99m, Category = "Dessert", ImageUrl = "🥧" }
            };
        }
    }
}
